package de.dasjeff.aSMPVCore.util;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * Utility-Klasse für häufig verwendete Funktionen im ASMP-VCore Plugin.
 */
public final class VCoreUtils {

    private static final Pattern UUID_PATTERN = Pattern.compile(
            "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
    );
    
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private VCoreUtils() {
        // Utility class - no instantiation
    }

    /**
     * Validiert eine UUID-String.
     * @param uuid Der zu validierende UUID-String.
     * @return true, wenn die UUID gültig ist.
     */
    public static boolean isValidUUID(@Nullable String uuid) {
        return uuid != null && UUID_PATTERN.matcher(uuid).matches();
    }

    /**
     * Konvertiert einen String sicher zu einer UUID.
     * @param uuid Der UUID-String.
     * @return Die UUID oder null, wenn ungültig.
     */
    @Nullable
    public static UUID parseUUID(@Nullable String uuid) {
        if (!isValidUUID(uuid)) {
            return null;
        }
        
        try {
            return UUID.fromString(uuid);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Formatiert einen Zeitstempel zu einem lesbaren String.
     * @param timestamp Der Zeitstempel in Millisekunden.
     * @return Der formatierte Zeitstempel.
     */
    @NotNull
    public static String formatTimestamp(long timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp), 
                ZoneId.systemDefault()
        );
        return dateTime.format(TIMESTAMP_FORMATTER);
    }

    /**
     * Formatiert eine Zeitdauer zu einem lesbaren String.
     * @param durationMillis Die Dauer in Millisekunden.
     * @return Die formatierte Dauer.
     */
    @NotNull
    public static String formatDuration(long durationMillis) {
        if (durationMillis < 0) {
            return "0ms";
        }
        
        long days = TimeUnit.MILLISECONDS.toDays(durationMillis);
        long hours = TimeUnit.MILLISECONDS.toHours(durationMillis) % 24;
        long minutes = TimeUnit.MILLISECONDS.toMinutes(durationMillis) % 60;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(durationMillis) % 60;
        long millis = durationMillis % 1000;
        
        StringBuilder sb = new StringBuilder();
        
        if (days > 0) {
            sb.append(days).append("d ");
        }
        if (hours > 0) {
            sb.append(hours).append("h ");
        }
        if (minutes > 0) {
            sb.append(minutes).append("m ");
        }
        if (seconds > 0) {
            sb.append(seconds).append("s ");
        }
        if (millis > 0 && sb.isEmpty()) {
            sb.append(millis).append("ms");
        }
        
        return !sb.isEmpty() ? sb.toString().trim() : "0ms";
    }

    /**
     * Parst eine Zeitdauer aus einem String.
     * Unterstützt Formate wie "1d", "2h", "30m", "45s".
     * @param duration Der Dauer-String.
     * @return Die Dauer in Millisekunden oder -1 bei Fehler.
     */
    public static long parseDuration(@Nullable String duration) {
        if (duration == null || duration.trim().isEmpty()) {
            return -1;
        }
        
        duration = duration.trim().toLowerCase();
        
        try {
            if (duration.endsWith("d")) {
                return Long.parseLong(duration.substring(0, duration.length() - 1)) * 24 * 60 * 60 * 1000;
            } else if (duration.endsWith("h")) {
                return Long.parseLong(duration.substring(0, duration.length() - 1)) * 60 * 60 * 1000;
            } else if (duration.endsWith("m")) {
                return Long.parseLong(duration.substring(0, duration.length() - 1)) * 60 * 1000;
            } else if (duration.endsWith("s")) {
                return Long.parseLong(duration.substring(0, duration.length() - 1)) * 1000;
            } else if (duration.endsWith("ms")) {
                return Long.parseLong(duration.substring(0, duration.length() - 2));
            } else {
                // Assume seconds if no unit specified
                return Long.parseLong(duration) * 1000;
            }
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * Bereinigt einen String für sichere Verwendung in Logs.
     * @param input Der zu bereinigende String.
     * @param maxLength Die maximale Länge.
     * @return Der bereinigte String.
     */
    @NotNull
    public static String sanitizeForLog(@Nullable String input, int maxLength) {
        if (input == null) {
            return "null";
        }
        
        // Remove potential log injection characters
        String sanitized = input.replaceAll("[\r\n\t]", "_");
        
        // Truncate if too long
        if (sanitized.length() > maxLength) {
            sanitized = sanitized.substring(0, maxLength - 3) + "...";
        }
        
        return sanitized;
    }

    /**
     * Bereinigt einen String für sichere Verwendung in Logs (Standard-Länge 100).
     * @param input Der zu bereinigende String.
     * @return Der bereinigte String.
     */
    @NotNull
    public static String sanitizeForLog(@Nullable String input) {
        return sanitizeForLog(input, 100);
    }

    /**
     * Prüft, ob ein String null oder leer ist.
     * @param str Der zu prüfende String.
     * @return true, wenn der String null oder leer ist.
     */
    public static boolean isNullOrEmpty(@Nullable String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * Gibt einen Default-Wert zurück, wenn der String null oder leer ist.
     * @param str Der zu prüfende String.
     * @param defaultValue Der Default-Wert.
     * @return Der String oder der Default-Wert.
     */
    @NotNull
    public static String defaultIfEmpty(@Nullable String str, @NotNull String defaultValue) {
        return isNullOrEmpty(str) ? defaultValue : str.trim();
    }

    /**
     * Formatiert Bytes zu einer lesbaren Größenangabe.
     * @param bytes Die Anzahl der Bytes.
     * @return Die formatierte Größenangabe.
     */
    @NotNull
    public static String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        }
        
        String[] units = {"KB", "MB", "GB", "TB", "PB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }

    /**
     * Berechnet einen einfachen Hash für einen String.
     * @param input Der Input-String.
     * @return Der Hash-Wert.
     */
    public static int simpleHash(@Nullable String input) {
        if (input == null) {
            return 0;
        }
        
        int hash = 0;
        for (int i = 0; i < input.length(); i++) {
            hash = 31 * hash + input.charAt(i);
        }
        return Math.abs(hash);
    }

    /**
     * Erstellt einen sicheren Dateinamen aus einem String.
     * @param input Der Input-String.
     * @return Der sichere Dateiname.
     */
    @NotNull
    public static String toSafeFileName(@Nullable String input) {
        if (isNullOrEmpty(input)) {
            return "unnamed";
        }
        
        // Replace unsafe characters with underscores
        String safe = input.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // Remove multiple consecutive underscores
        safe = safe.replaceAll("_{2,}", "_");
        
        // Remove leading/trailing underscores
        safe = safe.replaceAll("^_+|_+$", "");
        
        // Ensure it's not empty after cleaning
        if (safe.isEmpty()) {
            safe = "unnamed";
        }
        
        // Limit length
        if (safe.length() > 50) {
            safe = safe.substring(0, 50);
        }
        
        return safe;
    }

    /**
     * Prüft, ob eine IP-Adresse gültig ist (IPv4 oder IPv6).
     * @param ip Die zu prüfende IP-Adresse.
     * @return true, wenn die IP-Adresse gültig ist.
     */
    public static boolean isValidIP(@Nullable String ip) {
        if (isNullOrEmpty(ip)) {
            return false;
        }
        
        // Simple IPv4 check
        if (ip.matches("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$")) {
            return true;
        }
        
        // Simple IPv6 check (basic pattern)
        if (ip.matches("^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$") ||
            ip.matches("^::1$") || ip.equals("::")) {
            return true;
        }
        
        return false;
    }

    /**
     * Maskiert eine IP-Adresse für Datenschutz.
     * @param ip Die zu maskierende IP-Adresse.
     * @return Die maskierte IP-Adresse.
     */
    @NotNull
    public static String maskIP(@Nullable String ip) {
        if (!isValidIP(ip)) {
            return "invalid";
        }
        
        if (ip.contains(".")) {
            // IPv4
            String[] parts = ip.split("\\.");
            if (parts.length == 4) {
                return parts[0] + "." + parts[1] + ".xxx.xxx";
            }
        } else if (ip.contains(":")) {
            // IPv6 - mask last 4 groups
            String[] parts = ip.split(":");
            if (parts.length >= 4) {
                StringBuilder masked = new StringBuilder();
                for (int i = 0; i < Math.min(4, parts.length); i++) {
                    if (i > 0) masked.append(":");
                    masked.append(parts[i]);
                }
                masked.append(":xxxx:xxxx:xxxx:xxxx");
                return masked.toString();
            }
        }
        
        return "masked";
    }
}
