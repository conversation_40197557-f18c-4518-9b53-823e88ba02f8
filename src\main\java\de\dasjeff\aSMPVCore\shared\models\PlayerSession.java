package de.dasjeff.aSMPVCore.shared.models;

import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

/**
 * Shared PlayerSession model for ASMP-VCore system.
 * Represents an active or completed player session for tracking and analytics.
 */
public class PlayerSession {
    
    public enum SessionStatus {
        ACTIVE, COMPLETED, DISCONNECTED
    }
    
    @SerializedName("session_id")
    private final UUID sessionId;
    
    @SerializedName("player_uuid")
    private final UUID playerUuid;
    
    @SerializedName("player_name")
    private final String playerName;
    
    @SerializedName("server_id")
    private final String serverId;
    
    @SerializedName("start_time")
    private final Timestamp startTime;
    
    @SerializedName("end_time")
    private final Timestamp endTime;
    
    @SerializedName("duration_seconds")
    private final long durationSeconds;
    
    @SerializedName("status")
    private final SessionStatus status;
    
    @SerializedName("join_ip")
    private final String joinIp;
    
    @SerializedName("join_country")
    private final String joinCountry;
    
    @SerializedName("disconnect_reason")
    private final String disconnectReason;

    /**
     * Full constructor for completed sessions.
     */
    public PlayerSession(@NotNull UUID sessionId,
                        @NotNull UUID playerUuid,
                        @NotNull String playerName,
                        @NotNull String serverId,
                        @NotNull Timestamp startTime,
                        @Nullable Timestamp endTime,
                        long durationSeconds,
                        @NotNull SessionStatus status,
                        @Nullable String joinIp,
                        @Nullable String joinCountry,
                        @Nullable String disconnectReason) {
        this.sessionId = Objects.requireNonNull(sessionId, "sessionId cannot be null");
        this.playerUuid = Objects.requireNonNull(playerUuid, "playerUuid cannot be null");
        this.playerName = Objects.requireNonNull(playerName, "playerName cannot be null");
        this.serverId = Objects.requireNonNull(serverId, "serverId cannot be null");
        this.startTime = Objects.requireNonNull(startTime, "startTime cannot be null");
        this.endTime = endTime;
        this.durationSeconds = Math.max(0, durationSeconds);
        this.status = Objects.requireNonNull(status, "status cannot be null");
        this.joinIp = joinIp;
        this.joinCountry = joinCountry;
        this.disconnectReason = disconnectReason;
    }

    /**
     * Constructor for new active session.
     */
    public PlayerSession(@NotNull UUID playerUuid,
                        @NotNull String playerName,
                        @NotNull String serverId,
                        @Nullable String joinIp) {
        this(UUID.randomUUID(), playerUuid, playerName, serverId,
             Timestamp.from(Instant.now()), null, 0, SessionStatus.ACTIVE,
             joinIp, null, null);
    }

    /**
     * Constructor for session start with country info.
     */
    public PlayerSession(@NotNull UUID playerUuid,
                        @NotNull String playerName,
                        @NotNull String serverId,
                        @Nullable String joinIp,
                        @Nullable String joinCountry) {
        this(UUID.randomUUID(), playerUuid, playerName, serverId,
             Timestamp.from(Instant.now()), null, 0, SessionStatus.ACTIVE,
             joinIp, joinCountry, null);
    }

    // Getters
    @NotNull
    public UUID getSessionId() {
        return sessionId;
    }

    @NotNull
    public UUID getPlayerUuid() {
        return playerUuid;
    }

    @NotNull
    public String getPlayerName() {
        return playerName;
    }

    @NotNull
    public String getServerId() {
        return serverId;
    }

    @NotNull
    public Timestamp getStartTime() {
        return startTime;
    }

    @Nullable
    public Timestamp getEndTime() {
        return endTime;
    }

    public long getDurationSeconds() {
        return durationSeconds;
    }

    @NotNull
    public SessionStatus getStatus() {
        return status;
    }

    @Nullable
    public String getJoinIp() {
        return joinIp;
    }

    @Nullable
    public String getJoinCountry() {
        return joinCountry;
    }

    @Nullable
    public String getDisconnectReason() {
        return disconnectReason;
    }

    // Utility methods

    /**
     * Creates a completed session copy with end time and duration.
     */
    @NotNull
    public PlayerSession withCompletion(@Nullable String disconnectReason) {
        Timestamp now = Timestamp.from(Instant.now());
        long duration = (now.getTime() - startTime.getTime()) / 1000; // Convert to seconds
        
        return new PlayerSession(sessionId, playerUuid, playerName, serverId,
                               startTime, now, duration, SessionStatus.COMPLETED,
                               joinIp, joinCountry, disconnectReason);
    }

    /**
     * Creates a disconnected session copy.
     */
    @NotNull
    public PlayerSession withDisconnection(@Nullable String disconnectReason) {
        Timestamp now = Timestamp.from(Instant.now());
        long duration = (now.getTime() - startTime.getTime()) / 1000; // Convert to seconds
        
        return new PlayerSession(sessionId, playerUuid, playerName, serverId,
                               startTime, now, duration, SessionStatus.DISCONNECTED,
                               joinIp, joinCountry, disconnectReason);
    }

    /**
     * Calculates current session duration in seconds.
     */
    public long getCurrentDurationSeconds() {
        if (status == SessionStatus.ACTIVE) {
            return (System.currentTimeMillis() - startTime.getTime()) / 1000;
        }
        return durationSeconds;
    }

    /**
     * Checks if the session is currently active.
     */
    public boolean isActive() {
        return status == SessionStatus.ACTIVE;
    }

    /**
     * Checks if the session has ended (completed or disconnected).
     */
    public boolean hasEnded() {
        return status == SessionStatus.COMPLETED || status == SessionStatus.DISCONNECTED;
    }

    /**
     * Gets a human-readable duration string.
     */
    @NotNull
    public String getFormattedDuration() {
        long duration = getCurrentDurationSeconds();
        long hours = duration / 3600;
        long minutes = (duration % 3600) / 60;
        long seconds = duration % 60;
        
        if (hours > 0) {
            return String.format("%dh %dm %ds", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds);
        } else {
            return String.format("%ds", seconds);
        }
    }

    /**
     * Validates the session data.
     */
    public boolean isValid() {
        return sessionId != null &&
               playerUuid != null &&
               playerName != null && !playerName.trim().isEmpty() &&
               playerName.length() >= 3 && playerName.length() <= 16 &&
               playerName.matches("^[a-zA-Z0-9_]+$") &&
               serverId != null && !serverId.trim().isEmpty() &&
               startTime != null &&
               status != null &&
               durationSeconds >= 0 &&
               (endTime == null || endTime.after(startTime)) &&
               (joinCountry == null || joinCountry.length() == 2);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlayerSession that = (PlayerSession) o;
        return sessionId.equals(that.sessionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sessionId);
    }

    @Override
    public String toString() {
        return "PlayerSession{" +
                "sessionId=" + sessionId +
                ", playerUuid=" + playerUuid +
                ", playerName='" + playerName + '\'' +
                ", serverId='" + serverId + '\'' +
                ", status=" + status +
                ", duration='" + getFormattedDuration() + '\'' +
                ", startTime=" + startTime +
                (endTime != null ? ", endTime=" + endTime : "") +
                '}';
    }
}
