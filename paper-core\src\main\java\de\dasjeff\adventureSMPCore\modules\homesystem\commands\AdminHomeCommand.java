package de.dasjeff.adventureSMPCore.modules.homesystem.commands;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.model.Home;
import de.dasjeff.adventureSMPCore.util.PermissionUtil;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.logging.Level;

public class AdminHomeCommand extends BaseHomeCommand {

    private final Map<UUID, UUID> pendingConfirmations = new ConcurrentHashMap<>();
    private static final long CONFIRMATION_TIMEOUT = 30000;
    
    private static final String ALL_PLAYERS_CACHE = "adminHomeAllPlayersCache";
    private static final String PLAYERS_WITH_HOMES_CACHE = "adminHomePlayersWithHomesCache";

    public AdminHomeCommand(AdventureSMPCore corePlugin, HomeModule homeModule) {
        super(corePlugin, homeModule,
                "adminhome",
                PermissionUtil.getFullPermission("homes.admin"),
                false,
                "Manage homes of other players.",
                "/adminhome <set|delete|deleteall|tp|import> <player> ...",
                Collections.singletonList("ahome"));
        
        initializeCaches();
    }

    private void initializeCaches() {
        if (corePlugin.getCacheManager() != null) {
            try {
                corePlugin.getCacheManager().createCache(ALL_PLAYERS_CACHE, 1000, 5, TimeUnit.MINUTES);
                corePlugin.getCacheManager().createCache(PLAYERS_WITH_HOMES_CACHE, 500, 2, TimeUnit.MINUTES);
            } catch (IllegalArgumentException e) {
            }
        }
    }

    @Override
    protected boolean executeCommand(CommandSender sender, String[] args) {
        if (args.length < 1) {
            sendUsageMessage(sender);
            return true;
        }
    
        String subCommand = args[0].toLowerCase();
    
        // Handle import command separately
        if (subCommand.equals("import")) {
            return handleImportCommand(sender);
        }
    
        if (args.length < 2) {
            sendUsageMessage(sender);
            return true;
        }
    
        String targetPlayerName = args[1];
    
        // Input validation
        if (!isValidInput(targetPlayerName)) {
            sendMessage(sender, "invalid_input");
            return true;
        }
    
        return switch (subCommand) {
            case "set" -> handleSetCommand(sender, targetPlayerName, Arrays.copyOfRange(args, 2, args.length));
            case "delete", "del" -> handleDeleteCommand(sender, targetPlayerName, Arrays.copyOfRange(args, 2, args.length));
            case "deleteall", "delall" -> handleDeleteAllCommand(sender, targetPlayerName);
            case "tp", "teleport" -> handleTeleportCommand(sender, targetPlayerName, Arrays.copyOfRange(args, 2, args.length));
            default -> {
                sendUsageMessage(sender);
                yield true;
            }
        };
    }

    private boolean handleSetCommand(CommandSender sender, String targetPlayerName, String[] args) {
        if (!hasPermission(sender, "homes.admin.set")) return true;
        
        if (args.length < 1) {
            sendMessage(sender, "command_usage", "{command_usage}", "/adminhome set <player> <home> [x y z world]");
            return true;
        }

        String homeName = args[0];
        if (!isValidInput(homeName)) {
            sendMessage(sender, "invalid_input");
            return true;
        }

        Location location = determineLocation(sender, args);
        if (location == null) return true;

        executeAsync(
            findTargetPlayerAndSetHome(targetPlayerName, homeName, location),
            result -> handleSetHomeResult(sender, targetPlayerName, homeName, result)
        );

        return true;
    }

    private CompletableFuture<Boolean> findTargetPlayerAndSetHome(String targetPlayerName, String homeName, Location location) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                UUID targetUuid = homeModule.getDataAccessor().getPlayerUuidByName(targetPlayerName);
                if (targetUuid == null) return false;

                Home newHome = new Home(targetUuid, homeName, location);
                boolean success = homeModule.getDataAccessor().setHome(newHome);
                
                if (success) {
                    homeService.refreshPlayerCache(targetUuid);
                }
                
                return success;
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "Error in admin set home operation", e);
                return false;
            }
        });
    }

    private void handleSetHomeResult(CommandSender sender, String targetPlayerName, String homeName, boolean success) {
        if (success) {
            sendMessage(sender, "admin_home_set_success", "{home_name}", homeName, "{player}", targetPlayerName);
            playSound(sender, homeConfig.getSound("set_home"));
        } else {
            sendMessage(sender, "player_not_found", "{player}", targetPlayerName);
            playSound(sender, homeConfig.getSound("error"));
        }
    }

    private Location determineLocation(CommandSender sender, String[] args) {
        if (args.length >= 4) {
            return parseCoordinates(sender, args);
        } else if (sender instanceof Player player) {
            return player.getLocation();
        } else {
            sendMessage(sender, "console_must_specify_coordinates");
            return null;
        }
    }

    private Location parseCoordinates(CommandSender sender, String[] args) {
            try {
                double x = Double.parseDouble(args[1]);
                double y = Double.parseDouble(args[2]);
                double z = Double.parseDouble(args[3]);
                String worldName = args.length >= 5 ? args[4] : "world";
                World world = corePlugin.getServer().getWorld(worldName);
                
                if (world == null) {
                        sendMessage(sender, "world_not_found", "{world}", worldName);
                    playSound(sender, homeConfig.getSound("error"));
                    return null;
                }
                
                return new Location(world, x, y, z);
            } catch (NumberFormatException e) {
                    sendMessage(sender, "invalid_coordinates");
                playSound(sender, homeConfig.getSound("error"));
            return null;
        }
    }

    private boolean handleDeleteCommand(CommandSender sender, String targetPlayerName, String[] args) {
        if (!hasPermission(sender, "homes.admin.delete")) return true;
        
        if (args.length < 1) {
            sendMessage(sender, "command_usage", "{command_usage}", "/adminhome delete <player> <home>");
            return true;
        }

        String homeName = args[0];
        if (!isValidInput(homeName)) {
            sendMessage(sender, "invalid_input");
            return true;
        }

        executeAsync(
            findTargetPlayerAndDeleteHome(targetPlayerName, homeName),
            result -> handleDeleteResult(sender, targetPlayerName, homeName, result)
        );

        return true;
    }

    private CompletableFuture<Boolean> findTargetPlayerAndDeleteHome(String targetPlayerName, String homeName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                UUID targetUuid = homeModule.getDataAccessor().getPlayerUuidByName(targetPlayerName);
                if (targetUuid == null) return false;
                
                boolean success = homeModule.getDataAccessor().deleteHome(targetUuid, homeName);
                if (success) {
                    homeService.refreshPlayerCache(targetUuid);
                }
                return success;
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "Error in admin delete home operation", e);
                return false;
            }
        });
    }

    private void handleDeleteResult(CommandSender sender, String targetPlayerName, String homeName, boolean success) {
        if (success) {
            sendMessage(sender, "admin_home_delete_success", "{home_name}", homeName, "{player}", targetPlayerName);
            playSound(sender, homeConfig.getSound("delete_home"));
        } else {
            sendMessage(sender, "admin_home_not_found", "{home_name}", homeName, "{player}", targetPlayerName);
            playSound(sender, homeConfig.getSound("error"));
        }
    }

    private boolean handleDeleteAllCommand(CommandSender sender, String targetPlayerName) {
        if (!hasPermission(sender, "homes.admin.deleteall")) return true;

        UUID adminUuid = sender instanceof Player ? ((Player) sender).getUniqueId() : new UUID(0, 0);

        if (pendingConfirmations.containsKey(adminUuid)) {
            executeConfirmedDeleteAll(sender, targetPlayerName, adminUuid);
        } else {
            createDeleteAllConfirmation(sender, targetPlayerName, adminUuid);
        }

        return true;
    }

    private void executeConfirmedDeleteAll(CommandSender sender, String targetPlayerName, UUID adminUuid) {
        UUID confirmedTargetUuid = pendingConfirmations.get(adminUuid);
        
        executeAsync(
            CompletableFuture.supplyAsync(() -> {
                try {
                    UUID targetUuid = homeModule.getDataAccessor().getPlayerUuidByName(targetPlayerName);
                    if (targetUuid != null && targetUuid.equals(confirmedTargetUuid)) {
                        int deletedCount = homeModule.getDataAccessor().deleteAllHomes(targetUuid);
                        homeService.refreshPlayerCache(targetUuid);
                        return deletedCount;
                    }
                    return -1;
                } catch (Exception e) {
                    corePlugin.getLogger().log(Level.WARNING, "Error in admin delete all homes operation", e);
                    return -1;
                }
            }),
            deletedCount -> {
                pendingConfirmations.remove(adminUuid);
                if (deletedCount >= 0) {
                    sendMessage(sender, "admin_all_homes_deleted", "{count}", String.valueOf(deletedCount), "{player}", targetPlayerName);
                    playSound(sender, homeConfig.getSound("delete_home"));
                } else {
                    createDeleteAllConfirmation(sender, targetPlayerName, adminUuid);
                }
            }
        );
    }

    private void createDeleteAllConfirmation(CommandSender sender, String targetPlayerName, UUID adminUuid) {
        executeAsync(
            CompletableFuture.supplyAsync(() -> {
                try {
                    UUID targetUuid = homeModule.getDataAccessor().getPlayerUuidByName(targetPlayerName);
                    if (targetUuid == null) return null;
                    
                    int homeCount = homeModule.getDataAccessor().getHomeCount(targetUuid);
                    return new AbstractMap.SimpleEntry<>(targetUuid, homeCount);
                } catch (Exception e) {
                    corePlugin.getLogger().log(Level.WARNING, "Error checking homes for delete all confirmation", e);
                    return null;
                }
            }),
            result -> {
                if (result == null) {
                    sendMessage(sender, "player_not_found", "{player}", targetPlayerName);
                    playSound(sender, homeConfig.getSound("error"));
                    return;
                }
                
                pendingConfirmations.put(adminUuid, result.getKey());
                sendMessage(sender, "admin_deleteall_confirm", "{player}", targetPlayerName, "{count}", String.valueOf(result.getValue()));
                
                scheduleConfirmationTimeout(adminUuid, result.getKey(), sender);
            }
        );
    }

    private void scheduleConfirmationTimeout(UUID adminUuid, UUID targetUuid, CommandSender sender) {
                new BukkitRunnable() {
                    @Override
                    public void run() {
                if (pendingConfirmations.remove(adminUuid, targetUuid)) {
                            sendMessage(sender, "confirmation_expired");
                        }
                    }
                }.runTaskLater(corePlugin, CONFIRMATION_TIMEOUT / 50);
    }

    private boolean handleTeleportCommand(CommandSender sender, String targetPlayerName, String[] args) {
        if (!(sender instanceof Player admin)) {
            sendMessage(sender, "player_only_command");
            return true;
        }

        if (!hasPermission(sender, "homes.admin.tp")) return true;

        if (args.length < 1) {
            sendMessage(sender, "command_usage", "{command_usage}", "/adminhome tp <player> <home>");
            return true;
        }

        String homeName = args[0];
        if (!isValidInput(homeName)) {
            sendMessage(sender, "invalid_input");
            return true;
        }

        executeAsync(
            findTargetPlayerHome(targetPlayerName, homeName),
            home -> handleTeleportResult(admin, targetPlayerName, homeName, home)
        );

        return true;
    }

    private CompletableFuture<Home> findTargetPlayerHome(String targetPlayerName, String homeName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                UUID targetUuid = homeModule.getDataAccessor().getPlayerUuidByName(targetPlayerName);
                if (targetUuid == null) return null;
                
                return homeModule.getDataAccessor().getHome(targetUuid, homeName);
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "Error finding target player home", e);
                return null;
            }
        });
    }

    private void handleTeleportResult(Player admin, String targetPlayerName, String homeName, Home home) {
        if (home == null) {
            sendMessage(admin, "admin_home_not_found", "{home_name}", homeName, "{player}", targetPlayerName);
            playSound(admin, homeConfig.getSound("error"));
            return;
        }

        Location location = home.toLocation();
        if (location == null) {
            sendMessage(admin, "home_world_not_found", "{home_name}", homeName);
            playSound(admin, homeConfig.getSound("error"));
            return;
        }

        homeModule.getTeleportManager().startTeleport(
            admin, 
            location, 
            targetPlayerName + "'s " + homeName,
            "admin_teleport_success",
            "teleport_initiated"
        );
    }

    private boolean handleImportCommand(CommandSender sender) {
        if (!hasPermission(sender, "homes.admin.import")) return true;

        if (homeModule.isImportTaskRunning()) {
            sendMessage(sender, "admin_import_already_running");
            return true;
        }

        sendMessage(sender, "admin_import_started");
        homeModule.runPlayerImportTask();
        
        scheduleImportCompleteMessage(sender);
        return true;
    }

    private void scheduleImportCompleteMessage(CommandSender sender) {
        homeModule.setImportCompleteCallback(() -> {
            sendMessage(sender, "admin_import_finished", "{count}", String.valueOf(homeModule.getLastImportCount()));
            playSound(sender, homeConfig.getSound("set_home"));
        });
    }

    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        if (args.length == 1) {
            return Stream.of("set", "delete", "deleteall", "tp", "import")
                    .filter(cmd -> cmd.startsWith(args[0].toLowerCase()))
                    .collect(Collectors.toList());
        }

        if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            if (subCommand.equals("import")) {
                return Collections.emptyList();
            }
            
            String prefix = args[1].toLowerCase();
            boolean requireHomes = subCommand.equals("delete") || subCommand.equals("del") || 
                                   subCommand.equals("deleteall") || subCommand.equals("delall") || 
                                   subCommand.equals("tp") || subCommand.equals("teleport");
            
            return getPlayerNameSuggestions(prefix, requireHomes);
        }

        if (args.length == 3) {
            String subCommand = args[0].toLowerCase();
            String playerName = args[1];
            
            if (subCommand.equals("delete") || subCommand.equals("del") || 
                subCommand.equals("tp") || subCommand.equals("teleport")) {
                return getHomeNameSuggestions(playerName, args[2].toLowerCase());
            }
        }

        if (args.length >= 4 && args[0].equalsIgnoreCase("set") && args.length == 7) {
            return corePlugin.getServer().getWorlds().stream()
                    .map(World::getName)
                    .filter(name -> name.toLowerCase().startsWith(args[6].toLowerCase()))
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    private List<String> getPlayerNameSuggestions(String prefix, boolean requireHomes) {
        String cacheName = requireHomes ? PLAYERS_WITH_HOMES_CACHE : ALL_PLAYERS_CACHE;
        int limit = prefix.length() <= 2 ? 20 : 50;
        
        if (corePlugin.getCacheManager() != null) {
            String cacheKey = cacheName + ":" + prefix + ":" + limit;
            List<String> cachedNames = corePlugin.getCacheManager().get(
                cacheName,
                cacheKey,
                key -> requireHomes ? 
                    homeModule.getDataAccessor().getPlayerNamesWithHomes(prefix, limit) :
                    homeModule.getDataAccessor().getPlayerNamesStartingWith(prefix, limit)
            );
            
            return cachedNames != null ? cachedNames : Collections.emptyList();
        }
        
        return requireHomes ? 
            homeModule.getDataAccessor().getPlayerNamesWithHomes(prefix, limit) :
            homeModule.getDataAccessor().getPlayerNamesStartingWith(prefix, limit);
    }

    private List<String> getHomeNameSuggestions(String playerName, String prefix) {
        try {
            CompletableFuture<List<String>> future = CompletableFuture.supplyAsync(() -> {
                UUID playerUuid = homeModule.getDataAccessor().getPlayerUuidByName(playerName);
                if (playerUuid == null) {
                    return Collections.<String>emptyList();
                }
                
                if (corePlugin.getCacheManager() != null) {
                    List<Home> homes = corePlugin.getCacheManager().get(
                        HomeModule.PLAYER_HOMES_CACHE_NAME,
                        playerUuid,
                        key -> homeModule.getDataAccessor().getHomes(key)
                    );
                    
                    if (homes != null) {
                        return homes.stream()
                                .map(Home::getHomeName)
                                .filter(name -> name.toLowerCase().startsWith(prefix))
                                .sorted()
                                .collect(Collectors.toList());
                    }
                }
                
                return homeModule.getDataAccessor().getHomes(playerUuid).stream()
                        .map(Home::getHomeName)
                        .filter(name -> name.toLowerCase().startsWith(prefix))
                        .sorted()
                        .collect(Collectors.toList());
            });
            
            return future.get();
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    private void sendUsageMessage(CommandSender sender) {
        sendMessage(sender, "admin_command_usage");
        sender.sendMessage("§6/adminhome set <player> <home> §e[x y z world]");
        sender.sendMessage("§6/adminhome delete <player> <home>");
        sender.sendMessage("§6/adminhome deleteall <player>");
        sender.sendMessage("§6/adminhome tp <player> <home>");
        sender.sendMessage("§6/adminhome import §e- Import offline player data");
    }

    private boolean hasPermission(CommandSender sender, String permission) {
        if (!sender.hasPermission(PermissionUtil.getFullPermission(permission))) {
            sendMessage(sender, "no_permission");
            return false;
        }
        return true;
    }
} 