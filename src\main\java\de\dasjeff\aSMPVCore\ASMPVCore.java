package de.dasjeff.aSMPVCore;

import com.google.inject.Inject;
import com.velocitypowered.api.event.proxy.ProxyInitializeEvent;
import com.velocitypowered.api.event.proxy.ProxyShutdownEvent;
import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.plugin.Plugin;
import com.velocitypowered.api.plugin.annotation.DataDirectory;
import com.velocitypowered.api.proxy.ProxyServer;
import de.dasjeff.aSMPVCore.managers.*;
import de.dasjeff.aSMPVCore.managers.SecurityManager;
import de.dasjeff.aSMPVCore.modules.core.CoreModule;
import de.dasjeff.aSMPVCore.events.EventManager;
import org.slf4j.Logger;

import java.nio.file.Path;

@Plugin(id = "asmp-vcore", name = "ASMP-VCore", version = BuildConstants.VERSION,
        description = "Verwaltet netzwerkweite Systeme und Kommunikation zwischen den Servern.",
        authors = {"DasJeff"})
public class ASMPVCore {

    @Inject
    private Logger logger;

    @Inject
    private ProxyServer proxyServer;

    @Inject
    @DataDirectory
    private Path dataDirectory;

    // Core Managers
    private ConfigManager configManager;
    private DatabaseManager databaseManager;
    private RedisManager redisManager;
    private SecurityManager securityManager;
    private MessageManager messageManager;
    private ModuleManager moduleManager;
    private CacheManager cacheManager;
    private EventManager eventManager;

    @Subscribe
    public void onProxyInitialization(ProxyInitializeEvent event) {
        logger.info("Initializing ASMP-VCore v{}", BuildConstants.VERSION);

        try {
            // Phase 1: Initialize Core Managers
            initializeCoreManagers();

            // Phase 2: Initialize Module System
            initializeModuleSystem();

            logger.info("ASMP-VCore has been successfully initialized!");

        } catch (Exception e) {
            logger.error("Failed to initialize ASMP-VCore!", e);
            // Graceful shutdown on critical failure
            proxyServer.shutdown();
        }
    }

    @Subscribe
    public void onProxyShutdown(ProxyShutdownEvent event) {
        logger.info("Shutting down ASMP-VCore...");

        // Shutdown in reverse order
        if (moduleManager != null) {
            moduleManager.disableAllModules();
        }

        if (messageManager != null) {
            messageManager.shutdown();
        }

        if (eventManager != null) {
            eventManager.shutdown();
        }

        if (redisManager != null) {
            redisManager.shutdown();
        }

        if (databaseManager != null) {
            databaseManager.shutdown();
        }

        if (cacheManager != null) {
            cacheManager.shutdown();
        }

        if (securityManager != null) {
            securityManager.shutdown();
        }

        logger.info("ASMP-VCore has been shut down successfully.");
    }

    private void initializeCoreManagers() {
        logger.info("Initializing core managers...");

        // 1. ConfigManager - Must be first
        configManager = new ConfigManager(this, dataDirectory);
        logger.info("ConfigManager initialized.");

        // 2. SecurityManager - Early initialization for security
        securityManager = new SecurityManager(this);
        logger.info("SecurityManager initialized.");

        // 3. CacheManager - Before database and redis for caching
        cacheManager = new CacheManager(this);
        logger.info("CacheManager initialized.");

        // 4. EventManager - Before modules for event handling
        eventManager = new EventManager(this);
        logger.info("EventManager initialized.");

        // 5. DatabaseManager - If enabled
        if (configManager.getBoolean("database.enabled", true)) {
            databaseManager = new DatabaseManager(this);
            if (!databaseManager.initialize()) {
                throw new RuntimeException("Failed to initialize DatabaseManager with valid configuration");
            }
            logger.info("DatabaseManager initialized.");
        } else {
            logger.info("Database is disabled in configuration.");
        }

        // 6. RedisManager - If enabled
        if (configManager.getBoolean("redis.enabled", true)) {
            redisManager = new RedisManager(this);
            if (!redisManager.initialize()) {
                throw new RuntimeException("Failed to initialize RedisManager with valid configuration");
            }
            logger.info("RedisManager initialized.");
        } else {
            logger.info("Redis is disabled in configuration.");
        }

        // 7. MessageManager - Depends on Redis
        messageManager = new MessageManager(this);
        logger.info("MessageManager initialized.");
    }

    private void initializeModuleSystem() {
        logger.info("Initializing module system...");

        // Initialize ModuleManager
        moduleManager = new ModuleManager(this);

        // Register core modules
        moduleManager.registerModule(new CoreModule());
        moduleManager.registerModule(new de.dasjeff.aSMPVCore.modules.servermanagement.ServerManagementModule());
        moduleManager.registerModule(new de.dasjeff.aSMPVCore.modules.paperintegration.PaperIntegrationModule());

        // Additional modules will be registered here in future phases

        // Load and enable modules
        moduleManager.loadModules();
        moduleManager.enableModules();

        logger.info("Module system initialized.");
    }

    // Getters for managers
    public Logger getLogger() {
        return logger;
    }

    public ProxyServer getProxyServer() {
        return proxyServer;
    }

    public Path getDataDirectory() {
        return dataDirectory;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }

    public RedisManager getRedisManager() {
        return redisManager;
    }

    public SecurityManager getSecurityManager() {
        return securityManager;
    }

    public MessageManager getMessageManager() {
        return messageManager;
    }

    public ModuleManager getModuleManager() {
        return moduleManager;
    }

    public CacheManager getCacheManager() {
        return cacheManager;
    }

    public EventManager getEventManager() {
        return eventManager;
    }
}
