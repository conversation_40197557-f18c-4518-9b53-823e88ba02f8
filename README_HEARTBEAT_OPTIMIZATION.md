# ASMP-VCore - Heartbeat-System Optimierung

## Übersicht

Das bestehende Heartbeat-System wurde erfolgreich optimiert und erweitert, um strukturierte Server-Informationen zu verwenden und die neue ServerInfoDAO-Architektur zu nutzen.

## ✅ Implementierte Optimierungen

### 🔧 CoreModule Erweiterungen

#### **Erweiterte Heartbeat-Daten:**
```java
// Vorher (basic):
payload.addProperty("playerCount", playerCount);
payload.addProperty("timestamp", timestamp);

// Nachher (erweitert):
payload.addProperty("serverId", serverId);
payload.addProperty("serverName", serverName);
payload.addProperty("serverType", "VELOCITY");
payload.addProperty("status", "ONLINE");
payload.addProperty("playerCount", playerCount);
payload.addProperty("maxPlayers", maxPlayers);
payload.addProperty("version", version);
payload.addProperty("timestamp", timestamp);
```

#### **Neue Methoden:**
- **`registerServer()`**: Registriert Server beim Start in der Datenbank
- **`updateServerHeartbeat()`**: Asynchrone Heartbeat-Updates via ServerInfoDAO
- **`markServerOffline()`**: Markiert Server als offline beim Shutdown

#### **ServerInfoDAO Integration:**
- Ersetzt direkte SQL-Updates durch typsichere DAO-Operationen
- Asynchrone Verarbeitung für bessere Performance
- Strukturierte Error-Behandlung

### 📡 MessageManager Erweiterungen

#### **Neue Message Types Support:**
```java
// Erweiterte Kanal-Zuordnung:
case "system-heartbeat", "server-register", "server-update" -> CHANNEL_SERVER_STATUS;
case "player-data-request", "player-search-request" -> CHANNEL_PLAYER_LOOKUP;
case "punishment-ban", "punishment-mute" -> CHANNEL_PUNISHMENT;
```

#### **Neue Message Handler:**
- **`handleEnhancedHeartbeat()`**: Verarbeitet erweiterte Heartbeat-Daten
- **`handleServerUpdate()`**: Server-Status-Updates
- **`handleServerRegister()`**: Server-Registrierung

#### **ServerInfoDAO Integration:**
- Ersetzt direkte SQL-Updates in `updateServerHeartbeat()`
- Strukturierte ServerInfo-Objekte statt JSON-Parsing
- Bessere Typsicherheit und Validierung

## 🔄 Heartbeat-Flow (Optimiert)

### **Velocity Proxy (VCore):**
```
CoreModule (alle 30s)
    ↓ registerServer() (beim Start)
    ↓ updateServerHeartbeat() (alle 30s)
    ↓ ServerInfoDAO.updateHeartbeat()
    ↓ Redis Broadcast (MessageTypes.SYSTEM_HEARTBEAT)
vcore_servers Tabelle (strukturiert)
```

### **Paper Server (Vorbereitet für Phase 2.2):**
```
PaperCoreModule (geplant)
    ↓ Enhanced Heartbeat (TPS, Memory, etc.)
    ↓ ServerInfoDAO.upsertServerInfo()
    ↓ Redis Broadcast
VCore MessageManager
    ↓ handleEnhancedHeartbeat()
    ↓ ServerInfoDAO.upsertServerInfo()
vcore_servers Tabelle (erweitert)
```

## 📊 Datenbank-Schema (Erweitert)

### **vcore_servers Tabelle:**
```sql
CREATE TABLE IF NOT EXISTS vcore_servers (
    server_id VARCHAR(64) PRIMARY KEY,
    server_name VARCHAR(255) NOT NULL,
    server_type ENUM('PAPER', 'VELOCITY') NOT NULL,
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('ONLINE', 'OFFLINE', 'MAINTENANCE') DEFAULT 'OFFLINE',
    player_count INT DEFAULT 0,
    max_players INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Optimierte Indizes
    INDEX idx_server_type (server_type),
    INDEX idx_status (status),
    INDEX idx_last_heartbeat (last_heartbeat)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
```

## 🎯 Vorteile der Optimierung

### **Performance:**
- ✅ **Asynchrone DAO-Operationen**: Keine Blocking-SQL-Calls
- ✅ **Connection Pooling**: Optimierte Datenbankverbindungen
- ✅ **Strukturierte Queries**: Prepared Statements mit Indizes

### **Wartbarkeit:**
- ✅ **Typsicherheit**: ServerInfo-Objekte statt JSON-Parsing
- ✅ **Zentrale DAO-Logik**: Wiederverwendbare Datenbankoperationen
- ✅ **Konsistente Message Types**: MessageTypes-Konstanten

### **Erweiterbarkeit:**
- ✅ **Vorbereitet für Paper Core**: Erweiterte Server-Metriken (TPS, Memory)
- ✅ **Monitoring-Ready**: Basis für Web-Dashboard und Alerting
- ✅ **Skalierbar**: Unterstützt beliebig viele Server

### **Rückwärtskompatibilität:**
- ✅ **Legacy-Handler**: Alte Message Types werden weiterhin unterstützt
- ✅ **Graduelle Migration**: Bestehende Funktionalität bleibt erhalten
- ✅ **Fallback-Mechanismen**: Robuste Error-Behandlung

## 🔧 Technische Details

### **Message Types (Neu):**
```java
// System Heartbeat
MessageTypes.SYSTEM_HEARTBEAT + MessageActions.HEARTBEAT

// Server Management
MessageTypes.SERVER_UPDATE + MessageActions.UPDATE_SERVER
MessageTypes.SERVER_REGISTER + MessageActions.REGISTER_SERVER
```

### **Redis Channels (Erweitert):**
```java
// Heartbeat & Server Status
asmp:server-status -> Enhanced server information

// System Messages
asmp:system -> System-wide notifications
```

### **DAO Operations:**
```java
// Server Registration
serverInfoDAO.upsertServerInfo(serverInfo)

// Heartbeat Updates
serverInfoDAO.updateHeartbeat(serverId, status, playerCount, maxPlayers)

// Offline Marking
serverInfoDAO.updateHeartbeat(serverId, ServerStatus.OFFLINE, 0, 0)
```

## 🚀 Nächste Schritte (Phase 2.2)

### **Paper Core Integration:**
1. **Redis-Manager hinzufügen** zu Paper Core
2. **Enhanced Heartbeat implementieren** mit TPS/Memory-Daten
3. **Bidirektionale Kommunikation** etablieren

### **Monitoring Features:**
1. **Stale Server Detection**: Automatisches Offline-Marking
2. **Performance Metrics**: TPS, Memory, Player-Trends
3. **Alerting System**: Benachrichtigungen bei Server-Problemen

## ✅ Status

**Heartbeat-Optimierung: ABGESCHLOSSEN**
- ✅ CoreModule erweitert und optimiert
- ✅ MessageManager mit ServerInfoDAO integriert
- ✅ Neue Message Types implementiert
- ✅ Rückwärtskompatibilität gewährleistet
- ✅ Build erfolgreich

**Bereit für Phase 2.2**: Paper Core Redis-Integration kann beginnen!
