package de.dasjeff.adventureSMPCore;

import de.dasjeff.adventureSMPCore.managers.*;
import de.dasjeff.adventureSMPCore.managers.SecurityManager;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.vcoreintegration.VCoreIntegrationModule;
import de.dasjeff.adventureSMPCore.commands.CoreCommand;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.logging.Level;

public final class AdventureSMPCore extends JavaPlugin {

    // Core Managers
    private ConfigManager configManager;
    private DatabaseManager databaseManager;
    private CacheManager cacheManager;
    private SecurityManager securityManager;
    private CommandManager commandManager;
    private ListenerManager listenerManager;
    private ModuleManager moduleManager;
    private RedisManager redisManager;
    private MessageManager messageManager;

    @Override
    public void onLoad() {
        getLogger().info("Loading " + getPluginMeta().getName() + " v" + getPluginMeta().getVersion());

        // 1. ConfigManager
        try {
            configManager = new ConfigManager(this);
            getLogger().info("ConfigManager initialized.");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize ConfigManager!", e);
            return;
        }

        // 2. ModuleManager
        try {
            moduleManager = new ModuleManager(this);
            moduleManager.registerModule(new HomeModule());
            moduleManager.registerModule(new VCoreIntegrationModule());
            getLogger().info("ModuleManager initialized and modules registered.");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize ModuleManager!", e);
        }

        // Load modules (onLoad phase)
        if (moduleManager != null) {
            moduleManager.loadModules();
        }

        getLogger().info(getPluginMeta().getName() + " loaded successfully!");
    }

    @Override
    public void onEnable() {
        getLogger().info("Enabling " + getPluginMeta().getName() + " v" + getPluginMeta().getVersion());

        if (configManager == null) {
            getLogger().severe("Cannot enable plugin: ConfigManager failed to initialize.");
            return;
        }

        // 3. DatabaseManager
        if (configManager.getMainConfig().getBoolean("database.enabled", true)) {
            try {
                databaseManager = new DatabaseManager(this);
                if (!databaseManager.initialize()) {
                    getLogger().severe("Disabling plugin due to invalid or default database configuration.");
                    getServer().getPluginManager().disablePlugin(this);
                    return;
                }
            } catch (Exception e) {
                getLogger().log(Level.SEVERE, "Failed to initialize DatabaseManager!", e);
                getServer().getPluginManager().disablePlugin(this);
                return;
            }
        } else {
            getLogger().info("Database is disabled in configuration.");
        }

        // 4. CacheManager
        try {
            cacheManager = new CacheManager(this);
            getLogger().info("CacheManager initialized.");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize CacheManager!", e);
        }

        // 5. SecurityManager
        try {
            securityManager = new de.dasjeff.adventureSMPCore.managers.SecurityManager(this);
            getLogger().info("SecurityManager initialized.");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize SecurityManager!", e);
        }

        // 6. CommandManager
        try {
            commandManager = new CommandManager(this);
            getLogger().info("CommandManager initialized.");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize CommandManager!", e);
        }

        // 7. ListenerManager
        try {
            listenerManager = new ListenerManager(this);
            getLogger().info("ListenerManager initialized.");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize ListenerManager!", e);
        }

        // 8. RedisManager
        try {
            redisManager = new RedisManager(this);
            if (redisManager.initialize()) {
                getLogger().info("RedisManager initialized successfully.");
            } else {
                getLogger().warning("RedisManager initialization failed - VCore integration disabled.");
            }
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize RedisManager!", e);
        }

        // 9. MessageManager
        try {
            messageManager = new MessageManager(this);
            getLogger().info("MessageManager initialized.");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize MessageManager!", e);
        }

        // 10. Register Core Commands
        registerCoreCommands();

        // 11. Enable all registered modules
        if (moduleManager != null) {
            moduleManager.enableModules();
        }

        getLogger().info(getPluginMeta().getName() + " has been enabled successfully!");
        getLogger().info("AdventureSMP-Core by DasJeff is ready!");
        getLogger().info("Running on Server Version: " + getServer().getVersion());
    }

    private void registerCoreCommands() {
        if (commandManager != null) {
            try {
                // Register core commands
                commandManager.registerCommand(new CoreCommand(this));
                getLogger().info("Core commands registered.");
            } catch (Exception e) {
                getLogger().log(Level.WARNING, "Failed to register core commands", e);
            }
        } else {
            getLogger().warning("CommandManager is null, cannot register core commands!");
        }
    }

    @Override
    public void onDisable() {

        // 1. Disable all modules
        if (moduleManager != null) {
            moduleManager.disableModules();
            getLogger().info("All modules disabled.");
        }

        // 2. Unregister commands
        if (commandManager != null) {
            commandManager.unregisterAllCommands();
            getLogger().info("All commands unregistered.");
        }

        // 3. Unregister listeners
        if (listenerManager != null) {
            listenerManager.unregisterAllListeners();
            getLogger().info("All listeners unregistered.");
        }

        // 4. Shutdown SecurityManager
        if (securityManager != null) {
            getLogger().info("SecurityManager stats: " + securityManager.getSecurityStats());
            getLogger().info("SecurityManager shutdown.");
        }

        // 5. Shutdown MessageManager
        if (messageManager != null) {
            messageManager.shutdown();
            getLogger().info("MessageManager shutdown.");
        }

        // 6. Shutdown RedisManager
        if (redisManager != null) {
            redisManager.shutdown();
            getLogger().info("RedisManager shutdown.");
        }

        // 7. Shutdown CacheManager
        if (cacheManager != null) {
            cacheManager.shutdown();
            getLogger().info("CacheManager shutdown.");
        }

        // 8. Close database connections
        if (databaseManager != null) {
            databaseManager.closeDataSource();
            getLogger().info("Database connections closed.");
        }

        getLogger().info(getPluginMeta().getName() + " has been disabled.");
    }

    // Getters for managers
    public ConfigManager getConfigManager() {
        if (configManager == null) {
            getLogger().warning("ConfigManager was accessed before proper initialization or after a failure!");
        }
        return configManager;
    }

    public DatabaseManager getDatabaseManager() {
        if (databaseManager == null) {
            getLogger().warning("Attempted to access DatabaseManager, but it was not initialized (likely due to missing or default configuration).");
        }
        return databaseManager;
    }

    public CommandManager getCommandManager() {
        return commandManager;
    }

    public ListenerManager getListenerManager() {
        return listenerManager;
    }

    public ModuleManager getModuleManager() {
        return moduleManager;
    }

    public CacheManager getCacheManager() {
        return cacheManager;
    }

    public SecurityManager getSecurityManager() {
        return securityManager;
    }

    public RedisManager getRedisManager() {
        return redisManager;
    }

    public MessageManager getMessageManager() {
        return messageManager;
    }
}
