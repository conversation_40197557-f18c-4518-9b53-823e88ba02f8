package de.dasjeff.adventureSMPCore.modules.homesystem.config;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.util.ChatUtil;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.*;

public class MessageConfig {

    private final AdventureSMPCore corePlugin;
    private FileConfiguration config;
    private final Map<String, String> messages = new HashMap<>();
    private String prefix;

    private static final Set<String> ACTION_BAR_MESSAGES = Set.of(
        "teleport_initiated",
        "teleport_cancelled_move", 
        "teleport_cancelled_other",
        "teleport_cancelled_damage",
        "teleport_success",
        "teleport_cooldown",
        "teleport_already_inprogress",
        "home_world_not_found"
    );

    public MessageConfig(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
        loadMessages();
    }

    private void loadMessages() {
        setDefaults();
        
        config = corePlugin.getConfigManager().loadCustomConfig(HomeModule.MODULE_NAME, "messages.yml");
        if (config == null) {
            corePlugin.getLogger().severe("[" + HomeModule.MODULE_NAME + "] messages.yml could not be loaded! Using default messages.");
            return;
        }

        // Override defaults with config values
        prefix = config.getString("general.prefix", prefix);

        if (config.isConfigurationSection("messages")) {
            for (String key : Objects.requireNonNull(config.getConfigurationSection("messages")).getKeys(false)) {
                String configMessage = config.getString("messages." + key);
                if (configMessage != null) {
                    messages.put(key, configMessage);
                }
            }
        }
    }
    
    private void setDefaults() {
        prefix = "&8[&6HomeSystem&8] &r";
        
        // Basic home messages
        messages.put("home_set", "&aHome '{home_name}' set successfully!");
        messages.put("home_deleted", "&aHome '{home_name}' deleted successfully.");
        messages.put("home_already_exists", "&cA home with the name '{home_name}' already exists!");
        messages.put("home_not_found", "&cHome '{home_name}' not found!");
        messages.put("max_homes_reached", "&cYou have reached the maximum number of homes ({max_homes})!");
        
        // Teleport messages
        messages.put("teleport_initiated", "&aTeleporting to '{home_name}' in {delay} seconds...");
        messages.put("teleport_success", "&aTeleported to '{home_name}'!");
        messages.put("teleport_cancelled_move", "&cTeleport cancelled - you moved!");
        messages.put("teleport_cancelled_other", "&cTeleport cancelled!");
        messages.put("teleport_cancelled_damage", "&cTeleport cancelled - you took damage!");
        messages.put("teleport_cooldown", "&cYou must wait {cooldown} seconds before teleporting again!");
        messages.put("teleport_already_inprogress", "&cYou already have a teleport in progress!");
        
        // Validation messages
        messages.put("home_name_too_short", "&cHome name must be at least {min_length} characters long!");
        messages.put("home_name_too_long", "&cHome name must not exceed {max_length} characters!");
        messages.put("home_name_invalid_chars", "&cHome name contains invalid characters!");
        messages.put("home_name_reserved", "&cThe name '{home_name}' is reserved and cannot be used!");
        messages.put("set_home_blacklisted_world", "&cYou cannot set homes in world '{world_name}'!");
        
        // Error messages
        messages.put("no_permission", "&cYou don't have permission to do that!");
        messages.put("player_only", "&cThis command can only be used by players!");
        messages.put("player_not_found", "&cPlayer '{player}' not found!");
        messages.put("invalid_input", "&cInvalid input provided!");
        messages.put("home_world_not_found", "&cThe world for home '{home_name}' no longer exists!");
        
        // Admin messages
        messages.put("admin_home_set_success", "&aSet home '{home_name}' for player {player}!");
        messages.put("admin_home_delete_success", "&aDeleted home '{home_name}' for player {player}!");
        messages.put("admin_home_not_found", "&cPlayer {player} doesn't have a home named '{home_name}'!");
        messages.put("admin_deleteall_success", "&aDeleted all homes for player {player}!");
        messages.put("admin_import_started", "&aStarting player data import...");
        messages.put("admin_import_completed", "&aImported {count} players successfully!");
    }

    public void reloadMessages() {
        corePlugin.getConfigManager().loadCustomConfig(HomeModule.MODULE_NAME, "messages.yml");
        messages.clear();
        loadMessages();
    }

    public String getMessage(@NotNull String key, String... replacements) {
        String message = messages.getOrDefault(key, "&cMissing message for key: " + key);
        return applyPlaceholders(message, replacements);
    }

    public String getGuiMessage(@NotNull String key, String... replacements) {
        String message = messages.getOrDefault(key, "&cMissing GUI message for key: " + key);
        return ChatUtil.formatLegacy(applyPlaceholders(message, replacements));
    }

    public List<String> getGuiMessageList(@NotNull String key, String... replacements) {
        List<String> loreLines = config != null ? config.getStringList("messages." + key) : Collections.emptyList();
        if (loreLines.isEmpty()) {
            loreLines = Collections.singletonList("&cMissing lore for key: " + key);
        }
        
        return loreLines.stream()
            .map(line -> ChatUtil.formatLegacy(applyPlaceholders(line, replacements)))
            .toList();
    }

    public void sendMessage(@NotNull CommandSender sender, @NotNull String key, String... replacements) {
        if (sender instanceof Player player && ACTION_BAR_MESSAGES.contains(key)) {
            sendActionBarMessage(player, key, replacements);
        } else {
            sendChatMessage(sender, key, replacements);
        }
    }
    
    private void sendChatMessage(@NotNull CommandSender sender, @NotNull String key, String... replacements) {
        String message = getMessage(key, replacements);
        if (message.startsWith("noprefix:")) {
            ChatUtil.sendMessage(sender, message.substring("noprefix:".length()));
        } else {
            ChatUtil.sendMessage(sender, prefix + message);
        }
    }
    
    public void sendActionBarMessage(@NotNull Player player, @NotNull String key, String... replacements) {
        String message = getMessage(key, replacements);
        if (message.startsWith("noprefix:")) {
            message = message.substring("noprefix:".length());
        }
        ChatUtil.sendActionBar(player, message);
    }
    
    /**
     * Helper method to apply placeholder replacements
     */
    private String applyPlaceholders(String message, String... replacements) {
        if (replacements != null) {
            for (int i = 0; i < replacements.length; i += 2) {
                if (i + 1 < replacements.length) {
                    message = message.replace(replacements[i], replacements[i + 1]);
                }
            }
        }
        return message;
    }
    
    public String getPrefix() {
        return prefix;
    }
} 