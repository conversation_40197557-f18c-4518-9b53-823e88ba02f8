#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=Vous êtes déjà connecté(e) à ce serveur \!
velocity.error.already-connected-proxy=Vous êtes déjà connecté(e) à ce proxy \!
velocity.error.already-connecting=Vous êtes déjà en train d'essayer de vous connecter à un serveur \!
velocity.error.cant-connect=Impossible de se connecter à {0} \: {1}
velocity.error.connecting-server-error=Impossible de vous connecter à {0}. Veuillez réessayer ultérieurement.
velocity.error.connected-server-error=Votre connexion à {0} a rencontré un problème.
velocity.error.internal-server-connection-error=Une erreur interne s'est produite lors de la connexion au serveur.
velocity.error.logging-in-too-fast=Vous vous connectez trop rapidement, réessayez ultérieurement.
velocity.error.online-mode-only=Vous n'êtes pas connecté(e) à votre compte Minecraft. Si vous l'êtes, essayez de redémarrer votre client Minecraft.
velocity.error.player-connection-error=Une erreur interne s'est produite lors de votre connexion.
velocity.error.modern-forwarding-needs-new-client=Ce serveur est uniquement compatible avec Minecraft 1.13 et les versions ultérieures.
velocity.error.modern-forwarding-failed=Votre serveur n'a pas envoyé de requête de transfert vers le proxy. Assurez-vous que le serveur est configuré pour le transfert Velocity.
velocity.error.moved-to-new-server=Vous avez été expulsé(e) de {0} \: {1}
velocity.error.no-available-servers=Il n'y a pas de serveurs disponibles auxquels vous connecter. Réessayez ultérieurement ou contactez un administrateur.
velocity.error.illegal-chat-characters=Caractères interdits dans le chat.
# Commands
velocity.command.generic-error=Une erreur est survenue lors de l'exécution de cette commande.
velocity.command.command-does-not-exist=Cette commande n'existe pas.
velocity.command.players-only=Seuls les joueurs peuvent exécuter cette commande.
velocity.command.server-does-not-exist=Le serveur spécifié {0} n'existe pas.
velocity.command.player-not-found=Le joueur spécifié {0} n'existe pas.
velocity.command.server-current-server=Vous êtes actuellement connecté(e) à {0}.
velocity.command.server-too-many=Il y a trop de serveurs configurés. Utilisez la saisie semi-automatique via la touche Tab pour afficher tous les serveurs disponibles.
velocity.command.server-available=Serveurs disponibles \:
velocity.command.server-tooltip-player-online={0} joueur connecté
velocity.command.server-tooltip-players-online={0} joueurs connectés
velocity.command.server-tooltip-current-server=Actuellement connecté(e) à ce serveur
velocity.command.server-tooltip-offer-connect-server=Cliquez pour vous connecter à ce serveur
velocity.command.glist-player-singular={0} joueur est actuellement connecté au proxy.
velocity.command.glist-player-plural={0} joueurs sont actuellement connectés au proxy.
velocity.command.glist-view-all=Pour afficher tous les joueurs connectés aux serveurs, utilisez /glist all.
velocity.command.reload-success=Configuration de Velocity rechargée avec succès.
velocity.command.reload-failure=Impossible de recharger votre configuration de Velocity. Consultez la console pour plus de détails.
velocity.command.version-copyright=Copyright 2018-2023 {0}. {1} est sous la licence GNU General Public License v3.
velocity.command.no-plugins=Il n'y a aucun plugin actuellement installé.
velocity.command.plugins-list=Plugins \: {0}
velocity.command.plugin-tooltip-website=Site Internet \: {0}
velocity.command.plugin-tooltip-author=Auteur \: {0}
velocity.command.plugin-tooltip-authors=Auteurs \: {0}
velocity.command.dump-uploading=Envoi des informations collectées...
velocity.command.dump-send-error=Une erreur est survenue lors de la communication avec les serveurs Velocity. Soit les serveurs sont temporairement indisponibles, soit il y a un problème avec les paramètres de votre réseau. Vous trouverez plus d'informations dans le journal ou la console de votre serveur Velocity.
velocity.command.dump-success=Un rapport anonyme contenant des informations utiles sur ce proxy a été créé. Si un développeur vous le demande, vous pouvez le partager avec le lien suivant \:
velocity.command.dump-will-expire=Ce lien expirera dans quelques jours.
velocity.command.dump-server-error=Une erreur s'est produite sur les serveurs Velocity et le dump n'a pas pu être terminé. Veuillez contacter l'équipe de Velocity et leur communiquer les détails sur cette erreur à partir de la console de Velocity ou du journal du serveur.
velocity.command.dump-offline=Cause probable \: paramètres DNS non valides ou aucune connexion Internet
velocity.command.send-usage=/send <joueur> <serveur>
# Kick
velocity.kick.shutdown=Arrêt du proxy.