package de.dasjeff.aSMPVCore.modules.paperintegration;

import com.google.gson.JsonObject;
import de.dasjeff.aSMPVCore.communication.NetworkMessage;
import de.dasjeff.aSMPVCore.modules.AbstractModule;
import de.dasjeff.aSMPVCore.shared.dao.PlayerDataDAO;
import de.dasjeff.aSMPVCore.shared.dao.ServerInfoDAO;
import de.dasjeff.aSMPVCore.shared.messages.MessageActions;
import de.dasjeff.aSMPVCore.shared.messages.MessageTypes;
import de.dasjeff.aSMPVCore.shared.models.PlayerData;
import de.dasjeff.aSMPVCore.shared.models.ServerInfo;
import org.jetbrains.annotations.NotNull;

import java.time.Instant;
import java.util.UUID;

/**
 * PaperIntegrationModule handles communication with Paper Core servers.
 * Manages player data synchronization, server monitoring, and Paper-specific events.
 */
public class PaperIntegrationModule extends AbstractModule {

    private PlayerDataDAO playerDataDAO;
    private ServerInfoDAO serverInfoDAO;

    public PaperIntegrationModule() {
        super("PaperIntegration", "1.0.0", "Handles integration with Paper Core servers");
    }

    @Override
    protected void doLoad() throws Exception {
        debug("PaperIntegrationModule loading...");

        // Initialize DAOs
        this.playerDataDAO = new PlayerDataDAO(plugin);
        this.serverInfoDAO = new ServerInfoDAO(plugin);

        debug("PaperIntegrationModule loaded successfully");
    }

    @Override
    protected void doEnable() throws Exception {
        debug("PaperIntegrationModule enabling...");

        // Register message handlers for Paper Core communication
        registerMessageHandlers();

        info("PaperIntegrationModule enabled successfully");
    }

    @Override
    protected void doDisable() throws Exception {
        debug("PaperIntegrationModule disabling...");

        // Unregister message handlers
        unregisterMessageHandlers();

        debug("PaperIntegrationModule disabled successfully");
    }

    /**
     * Registers message handlers for Paper Core communication.
     */
    private void registerMessageHandlers() {
        if (plugin.getMessageManager() == null) {
            error("MessageManager not available - cannot register handlers");
            return;
        }

        // Server management messages
        plugin.getMessageManager().registerMessageHandler(MessageTypes.SERVER_REGISTER, this::handleServerRegister);
        plugin.getMessageManager().registerMessageHandler(MessageTypes.SERVER_UNREGISTER, this::handleServerUnregister);
        plugin.getMessageManager().registerMessageHandler(MessageTypes.SERVER_UPDATE, this::handleServerUpdate);
        plugin.getMessageManager().registerMessageHandler(MessageTypes.SYSTEM_HEARTBEAT, this::handleHeartbeat);

        // Player synchronization messages
        plugin.getMessageManager().registerMessageHandler(MessageTypes.PLAYER_JOIN, this::handlePlayerJoin);
        plugin.getMessageManager().registerMessageHandler(MessageTypes.PLAYER_QUIT, this::handlePlayerQuit);
        plugin.getMessageManager().registerMessageHandler(MessageTypes.PLAYER_SERVER_SWITCH, this::handlePlayerServerSwitch);
        plugin.getMessageManager().registerMessageHandler(MessageTypes.SESSION_START, this::handleSessionStart);
        plugin.getMessageManager().registerMessageHandler(MessageTypes.SESSION_END, this::handleSessionEnd);

        // Player data messages
        plugin.getMessageManager().registerMessageHandler(MessageTypes.PLAYER_DATA_REQUEST, this::handlePlayerDataRequest);
        plugin.getMessageManager().registerMessageHandler(MessageTypes.PLAYER_DATA_UPDATE, this::handlePlayerDataUpdate);

        debug("Paper integration message handlers registered");
    }

    /**
     * Unregisters message handlers.
     */
    private void unregisterMessageHandlers() {
        if (plugin.getMessageManager() == null) {
            return;
        }

        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.SERVER_REGISTER);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.SERVER_UNREGISTER);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.SERVER_UPDATE);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.SYSTEM_HEARTBEAT);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.PLAYER_JOIN);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.PLAYER_QUIT);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.PLAYER_SERVER_SWITCH);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.SESSION_START);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.SESSION_END);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.PLAYER_DATA_REQUEST);
        plugin.getMessageManager().unregisterMessageHandler(MessageTypes.PLAYER_DATA_UPDATE);

        debug("Paper integration message handlers unregistered");
    }

    // Server Management Handlers

    /**
     * Handles server registration from Paper Core.
     */
    private void handleServerRegister(@NotNull NetworkMessage message) {
        String serverId = message.getSourceServer();
        String serverName = message.getString("serverName");
        String serverType = message.getString("serverType");
        Integer playerCount = message.getInt("playerCount");
        Integer maxPlayers = message.getInt("maxPlayers");
        String version = message.getString("version");
        String motd = message.getString("motd");

        debug("Registering Paper server: {} ({})", serverId, serverName);

        // Create ServerInfo object and upsert it
        ServerInfo serverInfo = new ServerInfo(
            serverId,
            serverName != null ? serverName : serverId,
            serverType != null ? ServerInfo.ServerType.valueOf(serverType.toUpperCase()) : ServerInfo.ServerType.PAPER,
            ServerInfo.ServerStatus.ONLINE,
            playerCount != null ? playerCount : 0,
            maxPlayers != null ? maxPlayers : 0
        );

        serverInfoDAO.upsertServerInfo(serverInfo).thenAccept(success -> {
            if (success) {
                info("Successfully registered Paper server: {} ({})", serverId, serverName);
            } else {
                warn("Failed to register Paper server: {}", serverId);
            }
        }).exceptionally(throwable -> {
            error("Error registering Paper server: {}", serverId, throwable);
            return null;
        });
    }

    /**
     * Handles server unregistration from Paper Core.
     */
    private void handleServerUnregister(@NotNull NetworkMessage message) {
        String serverId = message.getSourceServer();
        String reason = message.getString("reason");

        debug("Unregistering Paper server: %s (reason: %s)", serverId, reason);

        serverInfoDAO.updateHeartbeat(serverId, ServerInfo.ServerStatus.OFFLINE, 0, 0)
                .thenAccept(success -> {
                    if (success) {
                        info("Successfully unregistered Paper server: %s", serverId);
                    } else {
                        warn("Failed to unregister Paper server: %s", serverId);
                    }
                }).exceptionally(throwable -> {
                    error("Error unregistering Paper server: %s", serverId, throwable);
                    return null;
                });
    }

    /**
     * Handles server update messages from Paper Core.
     */
    private void handleServerUpdate(@NotNull NetworkMessage message) {
        String serverId = message.getSourceServer();
        Integer playerCount = message.getInt("playerCount");
        Integer maxPlayers = message.getInt("maxPlayers");

        debug("Updating Paper server: %s (players: %s)", serverId, playerCount);

        serverInfoDAO.updateHeartbeat(
            serverId,
            ServerInfo.ServerStatus.ONLINE,
            playerCount != null ? playerCount : 0,
            maxPlayers != null ? maxPlayers : 0
        ).thenAccept(success -> {
            if (success) {
                debug("Successfully updated Paper server: %s", serverId);
            } else {
                warn("Failed to update Paper server: %s", serverId);
            }
        }).exceptionally(throwable -> {
            error("Error updating Paper server: %s", serverId, throwable);
            return null;
        });
    }

    /**
     * Handles enhanced heartbeat messages from Paper Core.
     */
    private void handleHeartbeat(@NotNull NetworkMessage message) {
        String serverId = message.getSourceServer();
        Integer playerCount = message.getInt("playerCount");
        Integer maxPlayers = message.getInt("maxPlayers");
        // Parse double values manually since getDouble() doesn't exist
        final Double tps;
        final Double memoryUsagePercent;
        Long memoryUsed = message.getLong("memoryUsed");
        Long memoryMax = message.getLong("memoryMax");

        try {
            String tpsStr = message.getString("tps");
            tps = (tpsStr != null) ? Double.parseDouble(tpsStr) : null;

            String memoryPercentStr = message.getString("memoryUsagePercent");
            memoryUsagePercent = (memoryPercentStr != null) ? Double.parseDouble(memoryPercentStr) : null;
        } catch (NumberFormatException e) {
            debug("Failed to parse double values from heartbeat message: %s", e.getMessage());
            return; // Exit early if parsing fails
        }
        Long uptime = message.getLong("uptime");

        debug("Enhanced heartbeat from Paper server: %s (TPS: %s, Memory: %s%%)",
                serverId, tps, memoryUsagePercent);

        // Update basic server info
        serverInfoDAO.updateHeartbeat(
            serverId,
            ServerInfo.ServerStatus.ONLINE,
            playerCount != null ? playerCount : 0,
            maxPlayers != null ? maxPlayers : 0
        ).thenAccept(success -> {
            if (success) {
                debug("Heartbeat processed for Paper server: %s", serverId);

                // Store enhanced metrics in cache for monitoring
                if (plugin.getCacheManager() != null) {
                    storeServerMetrics(serverId, tps, memoryUsed, memoryMax, memoryUsagePercent, uptime);
                }
            } else {
                warn("Failed to process heartbeat for Paper server: %s", serverId);
            }
        }).exceptionally(throwable -> {
            error("Error processing heartbeat for Paper server: %s", serverId, throwable);
            return null;
        });
    }

    /**
     * Stores enhanced server metrics in cache.
     */
    private void storeServerMetrics(String serverId, Double tps, Long memoryUsed, Long memoryMax,
                                  Double memoryUsagePercent, Long uptime) {
        try {
            JsonObject metrics = new JsonObject();
            if (tps != null) metrics.addProperty("tps", tps);
            if (memoryUsed != null) metrics.addProperty("memoryUsed", memoryUsed);
            if (memoryMax != null) metrics.addProperty("memoryMax", memoryMax);
            if (memoryUsagePercent != null) metrics.addProperty("memoryUsagePercent", memoryUsagePercent);
            if (uptime != null) metrics.addProperty("uptime", uptime);
            metrics.addProperty("lastUpdate", System.currentTimeMillis());

            String cacheKey = "server:metrics:" + serverId;
            plugin.getCacheManager().put(cacheKey, metrics.toString(), 300); // 5 minutes TTL

            debug("Stored enhanced metrics for server: %s", serverId);

        } catch (Exception e) {
            warn("Failed to store server metrics for %s: %s", serverId, e.getMessage());
        }
    }

    // Player Event Handlers

    /**
     * Handles player join events from Paper Core.
     */
    private void handlePlayerJoin(@NotNull NetworkMessage message) {
        String playerUuidStr = message.getString("playerUuid");
        String playerName = message.getString("playerName");
        String serverId = message.getSourceServer();
        Long joinTime = message.getLong("joinTime");

        if (playerUuidStr == null || playerName == null) {
            warn("Invalid player join message - missing UUID or name");
            return;
        }

        try {
            UUID playerUuid = UUID.fromString(playerUuidStr);
            debug("Player join: %s (%s) on server %s", playerName, playerUuid, serverId);

            // Update or create player record
            updatePlayerRecord(playerUuid, playerName, serverId, joinTime);

        } catch (IllegalArgumentException e) {
            warn("Invalid player UUID in join message: %s", playerUuidStr);
        }
    }

    /**
     * Handles player quit events from Paper Core.
     */
    private void handlePlayerQuit(@NotNull NetworkMessage message) {
        String playerUuidStr = message.getString("playerUuid");
        String playerName = message.getString("playerName");
        String serverId = message.getSourceServer();
        Long quitTime = message.getLong("quitTime");

        if (playerUuidStr == null) {
            warn("Invalid player quit message - missing UUID");
            return;
        }

        try {
            UUID playerUuid = UUID.fromString(playerUuidStr);
            debug("Player quit: %s (%s) from server %s", playerName, playerUuid, serverId);

            // End current session
            endPlayerSession(playerUuid, serverId, quitTime);

        } catch (IllegalArgumentException e) {
            warn("Invalid player UUID in quit message: %s", playerUuidStr);
        }
    }

    /**
     * Handles player server switch events.
     */
    private void handlePlayerServerSwitch(@NotNull NetworkMessage message) {
        String playerUuidStr = message.getString("playerUuid");
        String fromServer = message.getString("fromServer");
        String toServer = message.getString("toServer");
        Long switchTime = message.getLong("switchTime");

        if (playerUuidStr == null || fromServer == null || toServer == null) {
            warn("Invalid player server switch message - missing required fields");
            return;
        }

        try {
            UUID playerUuid = UUID.fromString(playerUuidStr);
            debug("Player server switch: %s from %s to %s", playerUuid, fromServer, toServer);

            // End session on old server and start on new server
            endPlayerSession(playerUuid, fromServer, switchTime);
            startPlayerSession(playerUuid, toServer, switchTime);

        } catch (IllegalArgumentException e) {
            warn("Invalid player UUID in server switch message: %s", playerUuidStr);
        }
    }

    /**
     * Handles session start events.
     */
    private void handleSessionStart(@NotNull NetworkMessage message) {
        String playerUuidStr = message.getString("playerUuid");
        String serverId = message.getSourceServer();
        Long startTime = message.getLong("startTime");

        if (playerUuidStr == null) {
            warn("Invalid session start message - missing UUID");
            return;
        }

        try {
            UUID playerUuid = UUID.fromString(playerUuidStr);
            startPlayerSession(playerUuid, serverId, startTime);

        } catch (IllegalArgumentException e) {
            warn("Invalid player UUID in session start message: %s", playerUuidStr);
        }
    }

    /**
     * Handles session end events.
     */
    private void handleSessionEnd(@NotNull NetworkMessage message) {
        String playerUuidStr = message.getString("playerUuid");
        String serverId = message.getSourceServer();
        Long endTime = message.getLong("endTime");

        if (playerUuidStr == null) {
            warn("Invalid session end message - missing UUID");
            return;
        }

        try {
            UUID playerUuid = UUID.fromString(playerUuidStr);
            endPlayerSession(playerUuid, serverId, endTime);

        } catch (IllegalArgumentException e) {
            warn("Invalid player UUID in session end message: %s", playerUuidStr);
        }
    }

    // Player Data Handlers

    /**
     * Handles player data requests from Paper Core.
     */
    private void handlePlayerDataRequest(@NotNull NetworkMessage message) {
        String playerUuidStr = message.getString("playerUuid");
        String requestId = message.getString("requestId");

        if (playerUuidStr == null) {
            warn("Invalid player data request - missing UUID");
            return;
        }

        try {
            UUID playerUuid = UUID.fromString(playerUuidStr);
            debug("Player data request for: %s", playerUuid);

            // Fetch player data and send response
            playerDataDAO.getPlayerData(playerUuid).thenAccept(playerData -> {
                sendPlayerDataResponse(message.getSourceServer(), requestId, playerData);
            }).exceptionally(throwable -> {
                error("Error fetching player data for %s: %s", playerUuid, throwable.getMessage());
                sendPlayerDataResponse(message.getSourceServer(), requestId, null);
                return null;
            });

        } catch (IllegalArgumentException e) {
            warn("Invalid player UUID in data request: %s", playerUuidStr);
        }
    }

    /**
     * Handles player data updates from Paper Core.
     */
    private void handlePlayerDataUpdate(@NotNull NetworkMessage message) {
        String playerUuidStr = message.getString("playerUuid");
        String playerName = message.getString("playerName");

        if (playerUuidStr == null || playerName == null) {
            warn("Invalid player data update - missing UUID or name");
            return;
        }

        try {
            UUID playerUuid = UUID.fromString(playerUuidStr);
            debug("Player data update for: %s (%s)", playerName, playerUuid);

            updatePlayerRecord(playerUuid, playerName, message.getSourceServer(), System.currentTimeMillis());

        } catch (IllegalArgumentException e) {
            warn("Invalid player UUID in data update: %s", playerUuidStr);
        }
    }

    // Helper Methods

    /**
     * Updates or creates a player record.
     */
    private void updatePlayerRecord(UUID playerUuid, String playerName, String serverId, Long timestamp) {
        playerDataDAO.getPlayerData(playerUuid).thenAccept(existingPlayerData -> {
            Instant now = timestamp != null ? Instant.ofEpochMilli(timestamp) : Instant.now();

            if (existingPlayerData != null) {
                // Update existing player - create new PlayerData with updated values
                PlayerData updatedPlayerData = new PlayerData(
                        playerUuid,
                        playerName, // updated name
                        existingPlayerData.getFirstSeen(),
                        java.sql.Timestamp.from(now), // updated last seen
                        existingPlayerData.getCurrentServer(), // last server becomes current
                        serverId, // new current server
                        existingPlayerData.getTotalPlaytime(),
                        existingPlayerData.getSessionCount(),
                        existingPlayerData.getLastIp(),
                        existingPlayerData.getLastCountry(),
                        existingPlayerData.isVpnUser(),
                        existingPlayerData.getCreatedAt(),
                        java.sql.Timestamp.from(now) // updated timestamp
                );

                playerDataDAO.upsertPlayerData(updatedPlayerData).thenAccept(success -> {
                    if (success) {
                        debug("Updated player record: %s (%s)", playerName, playerUuid);
                    } else {
                        warn("Failed to update player record: %s", playerUuid);
                    }
                });
            } else {
                // Create new player
                PlayerData newPlayerData = new PlayerData(
                        playerUuid,
                        playerName,
                        java.sql.Timestamp.from(now), // first seen
                        java.sql.Timestamp.from(now), // last seen
                        null, // last server
                        serverId, // current server
                        0L, // total playtime
                        0, // session count
                        null, // last IP
                        null, // last country
                        false, // is VPN user
                        java.sql.Timestamp.from(now), // created at
                        java.sql.Timestamp.from(now) // updated at
                );

                playerDataDAO.upsertPlayerData(newPlayerData).thenAccept(success -> {
                    if (success) {
                        debug("Created new player record: %s (%s)", playerName, playerUuid);
                    } else {
                        warn("Failed to create player record: %s", playerUuid);
                    }
                });
            }
        }).exceptionally(throwable -> {
            error("Error processing player record update for %s: %s", playerUuid, throwable.getMessage());
            return null;
        });
    }

    /**
     * Starts a new player session.
     */
    private void startPlayerSession(UUID playerUuid, String serverId, Long startTime) {
        // For now, just log the session start - PlayerSessionDAO implementation would be needed
        debug("Started session for player {} on server {}", playerUuid, serverId);

        // TODO: Implement PlayerSessionDAO.createSession when needed
        // playerSessionDAO.createSession(session).thenAccept(success -> {
        //     if (success) {
        //         debug("Started session for player {} on server {}", playerUuid, serverId);
        //     } else {
        //         warn("Failed to start session for player {} on server {}", playerUuid, serverId);
        //     }
        // }).exceptionally(throwable -> {
        //     error("Error starting session for player {}: {}", playerUuid, throwable.getMessage());
        //     return null;
        // });
    }

    /**
     * Ends a player session.
     */
    private void endPlayerSession(UUID playerUuid, String serverId, Long endTime) {
        // For now, just log the session end - PlayerSessionDAO implementation would be needed
        debug("Ended session for player {} on server {}", playerUuid, serverId);

        // TODO: Implement PlayerSessionDAO.endCurrentSession when needed
        // playerSessionDAO.endCurrentSession(playerUuid, serverId,
        //         endTime != null ? Instant.ofEpochMilli(endTime) : Instant.now())
        //         .thenAccept(success -> {
        //             if (success) {
        //                 debug("Ended session for player {} on server {}", playerUuid, serverId);
        //             } else {
        //                 warn("Failed to end session for player {} on server {}", playerUuid, serverId);
        //             }
        //         }).exceptionally(throwable -> {
        //             error("Error ending session for player {}: {}", playerUuid, throwable.getMessage());
        //             return null;
        //         });
    }

    /**
     * Sends a player data response back to the requesting server.
     */
    private void sendPlayerDataResponse(String targetServer, String requestId, PlayerData playerData) {
        if (plugin.getMessageManager() == null) {
            return;
        }

        JsonObject payload = new JsonObject();
        if (requestId != null) {
            payload.addProperty("requestId", requestId);
        }

        if (playerData != null) {
            payload.addProperty("found", true);
            payload.addProperty("playerUuid", playerData.getPlayerUuid().toString());
            payload.addProperty("playerName", playerData.getLastKnownName());
            payload.addProperty("firstSeen", playerData.getFirstSeen().getTime());
            payload.addProperty("lastSeen", playerData.getLastSeen().getTime());
            if (playerData.getCurrentServer() != null) {
                payload.addProperty("currentServer", playerData.getCurrentServer());
            }
            payload.addProperty("totalPlaytime", playerData.getTotalPlaytime());
            payload.addProperty("sessionCount", playerData.getSessionCount());
        } else {
            payload.addProperty("found", false);
        }

        plugin.getMessageManager().sendMessage(targetServer, MessageTypes.PLAYER_DATA_RESPONSE,
                MessageActions.RESPONSE, payload);
    }
}
