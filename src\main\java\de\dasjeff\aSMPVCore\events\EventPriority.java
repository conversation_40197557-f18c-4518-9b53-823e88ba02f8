package de.dasjeff.aSMPVCore.events;

/**
 * Enum für Event-Handler-Prioritäten.
 * Bestimmt die Reihenfolge, in der Event-Handler ausgeführt werden.
 */
public enum EventPriority {

    /**
     * Niedrigste Priorität - wird zuerst ausgeführt.
     */
    LOWEST(0),

    /**
     * Niedrige Priorität.
     */
    LOW(1),

    /**
     * Normale Priorität - Standard für die meisten Handler.
     */
    NORMAL(2),

    /**
     * Hohe Priorität.
     */
    HIGH(3),

    /**
     * Höchste Priorität - wird fast zuletzt ausgeführt.
     */
    HIGHEST(4),

    /**
     * Monitor-Priorität - wird immer zuletzt ausgeführt und kann Events nicht abbrechen.
     * Wird auch ausgeführt, wenn das Event bereits abgebrochen wurde.
     */
    MONITOR(5);

    private final int value;

    EventPriority(int value) {
        this.value = value;
    }

    /**
     * Gibt den numerischen Wert der Priorität zurück.
     * @return Der numerische Wert.
     */
    public int getValue() {
        return value;
    }
}
