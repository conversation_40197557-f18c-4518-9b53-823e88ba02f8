package de.dasjeff.adventureSMPCore.shared.messages;

/**
 * Constants for message types used in VCore communication.
 * These must match the VCore MessageTypes exactly.
 */
public final class MessageTypes {

    // System messages
    public static final String SYSTEM_HEARTBEAT = "system-heartbeat";
    public static final String SYSTEM_SHUTDOWN = "system-shutdown";
    public static final String SYSTEM_MAINTENANCE = "system-maintenance";
    public static final String SYSTEM_STATUS_REQUEST = "system-status-request";
    public static final String SYSTEM_STATUS_RESPONSE = "system-status-response";

    // Server management
    public static final String SERVER_REGISTER = "server-register";
    public static final String SERVER_UNREGISTER = "server-unregister";
    public static final String SERVER_UPDATE = "server-update";
    public static final String SERVER_LIST_REQUEST = "server-list-request";
    public static final String SERVER_LIST_RESPONSE = "server-list-response";

    // Player synchronization
    public static final String PLAYER_JOIN = "player-join";
    public static final String PLAYER_QUIT = "player-quit";
    public static final String PLAYER_SERVER_SWITCH = "player-server-switch";
    public static final String SESSION_START = "session-start";
    public static final String SESSION_END = "session-end";

    // Player data
    public static final String PLAYER_DATA_REQUEST = "player-data-request";
    public static final String PLAYER_DATA_RESPONSE = "player-data-response";
    public static final String PLAYER_DATA_UPDATE = "player-data-update";
    public static final String PLAYER_SEARCH_REQUEST = "player-search-request";
    public static final String PLAYER_SEARCH_RESPONSE = "player-search-response";

    // Punishment system
    public static final String PUNISHMENT_BAN = "punishment-ban";
    public static final String PUNISHMENT_UNBAN = "punishment-unban";
    public static final String PUNISHMENT_MUTE = "punishment-mute";
    public static final String PUNISHMENT_UNMUTE = "punishment-unmute";
    public static final String PUNISHMENT_KICK = "punishment-kick";
    public static final String PUNISHMENT_WARN = "punishment-warn";
    public static final String PUNISHMENT_CHECK = "punishment-check";

    // Report system
    public static final String REPORT_CREATE = "report-create";
    public static final String REPORT_UPDATE = "report-update";
    public static final String REPORT_CLOSE = "report-close";
    public static final String REPORT_ASSIGN = "report-assign";
    public static final String REPORT_LIST_REQUEST = "report-list-request";
    public static final String REPORT_LIST_RESPONSE = "report-list-response";

    // Chat system
    public static final String CHAT_MESSAGE = "chat-message";
    public static final String CHAT_BROADCAST = "chat-broadcast";
    public static final String CHAT_PRIVATE = "chat-private";

    // Cache synchronization
    public static final String CACHE_INVALIDATE = "cache-invalidate";
    public static final String CACHE_UPDATE = "cache-update";
    public static final String CACHE_SYNC_REQUEST = "cache-sync-request";

    private MessageTypes() {
        // Utility class
    }
}
