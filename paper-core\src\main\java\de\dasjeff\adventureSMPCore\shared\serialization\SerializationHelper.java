package de.dasjeff.aSMPVCore.shared.serialization;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import de.dasjeff.aSMPVCore.shared.models.PlayerData;
import de.dasjeff.aSMPVCore.shared.models.PlayerSession;
import de.dasjeff.aSMPVCore.shared.models.ServerInfo;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Serialization helper for converting shared models to/from JSON.
 * Provides type-safe serialization for network communication.
 */
public class SerializationHelper {
    
    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .registerTypeAdapter(Timestamp.class, new TimestampAdapter())
            .registerTypeAdapter(UUID.class, new UUIDAdapter())
            .create();

    // Type tokens for generic collections
    public static final Type PLAYER_DATA_LIST_TYPE = new TypeToken<List<PlayerData>>(){}.getType();
    public static final Type SERVER_INFO_LIST_TYPE = new TypeToken<List<ServerInfo>>(){}.getType();
    public static final Type PLAYER_SESSION_LIST_TYPE = new TypeToken<List<PlayerSession>>(){}.getType();

    /**
     * Serializes an object to JSON string.
     */
    @NotNull
    public static String toJson(@NotNull Object object) {
        try {
            return GSON.toJson(object);
        } catch (Exception e) {
            throw new SerializationException("Failed to serialize object to JSON", e);
        }
    }

    /**
     * Deserializes JSON string to specified type.
     */
    @Nullable
    public static <T> T fromJson(@NotNull String json, @NotNull Class<T> clazz) {
        try {
            return GSON.fromJson(json, clazz);
        } catch (Exception e) {
            throw new SerializationException("Failed to deserialize JSON to " + clazz.getSimpleName(), e);
        }
    }

    /**
     * Deserializes JSON string to specified generic type.
     */
    @Nullable
    public static <T> T fromJson(@NotNull String json, @NotNull Type type) {
        try {
            return GSON.fromJson(json, type);
        } catch (Exception e) {
            throw new SerializationException("Failed to deserialize JSON to " + type.getTypeName(), e);
        }
    }

    // Specific serialization methods for shared models

    /**
     * Serializes PlayerData to JSON.
     */
    @NotNull
    public static String serializePlayerData(@NotNull PlayerData playerData) {
        return toJson(playerData);
    }

    /**
     * Deserializes PlayerData from JSON.
     */
    @Nullable
    public static PlayerData deserializePlayerData(@NotNull String json) {
        return fromJson(json, PlayerData.class);
    }

    /**
     * Serializes ServerInfo to JSON.
     */
    @NotNull
    public static String serializeServerInfo(@NotNull ServerInfo serverInfo) {
        return toJson(serverInfo);
    }

    /**
     * Deserializes ServerInfo from JSON.
     */
    @Nullable
    public static ServerInfo deserializeServerInfo(@NotNull String json) {
        return fromJson(json, ServerInfo.class);
    }

    /**
     * Serializes PlayerSession to JSON.
     */
    @NotNull
    public static String serializePlayerSession(@NotNull PlayerSession playerSession) {
        return toJson(playerSession);
    }

    /**
     * Deserializes PlayerSession from JSON.
     */
    @Nullable
    public static PlayerSession deserializePlayerSession(@NotNull String json) {
        return fromJson(json, PlayerSession.class);
    }

    /**
     * Serializes a list of PlayerData to JSON.
     */
    @NotNull
    public static String serializePlayerDataList(@NotNull List<PlayerData> playerDataList) {
        return GSON.toJson(playerDataList, PLAYER_DATA_LIST_TYPE);
    }

    /**
     * Deserializes a list of PlayerData from JSON.
     */
    @Nullable
    public static List<PlayerData> deserializePlayerDataList(@NotNull String json) {
        return GSON.fromJson(json, PLAYER_DATA_LIST_TYPE);
    }

    /**
     * Serializes a list of ServerInfo to JSON.
     */
    @NotNull
    public static String serializeServerInfoList(@NotNull List<ServerInfo> serverInfoList) {
        return GSON.toJson(serverInfoList, SERVER_INFO_LIST_TYPE);
    }

    /**
     * Deserializes a list of ServerInfo from JSON.
     */
    @Nullable
    public static List<ServerInfo> deserializeServerInfoList(@NotNull String json) {
        return GSON.fromJson(json, SERVER_INFO_LIST_TYPE);
    }

    /**
     * Creates a JsonObject from any object.
     */
    @NotNull
    public static JsonObject toJsonObject(@NotNull Object object) {
        return GSON.toJsonTree(object).getAsJsonObject();
    }

    /**
     * Converts JsonObject to specified type.
     */
    @Nullable
    public static <T> T fromJsonObject(@NotNull JsonObject jsonObject, @NotNull Class<T> clazz) {
        return GSON.fromJson(jsonObject, clazz);
    }

    /**
     * Validates if a JSON string is valid.
     */
    public static boolean isValidJson(@NotNull String json) {
        try {
            JsonParser.parseString(json);
            return true;
        } catch (JsonSyntaxException e) {
            return false;
        }
    }

    /**
     * Custom Timestamp adapter for Gson.
     */
    private static class TimestampAdapter implements JsonSerializer<Timestamp>, JsonDeserializer<Timestamp> {
        
        @Override
        public JsonElement serialize(Timestamp src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.getTime());
        }

        @Override
        public Timestamp deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) 
                throws JsonParseException {
            return new Timestamp(json.getAsLong());
        }
    }

    /**
     * Custom UUID adapter for Gson.
     */
    private static class UUIDAdapter implements JsonSerializer<UUID>, JsonDeserializer<UUID> {
        
        @Override
        public JsonElement serialize(UUID src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.toString());
        }

        @Override
        public UUID deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) 
                throws JsonParseException {
            return UUID.fromString(json.getAsString());
        }
    }

    /**
     * Custom exception for serialization errors.
     */
    public static class SerializationException extends RuntimeException {
        public SerializationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
