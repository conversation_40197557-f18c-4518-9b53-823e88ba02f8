package de.dasjeff.adventureSMPCore.modules.homesystem.commands;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.commands.AbstractCommand;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.config.HomeConfig;
import de.dasjeff.adventureSMPCore.modules.homesystem.config.MessageConfig;
import de.dasjeff.adventureSMPCore.modules.homesystem.model.Home;
import de.dasjeff.adventureSMPCore.modules.homesystem.service.HomeService;
import de.dasjeff.adventureSMPCore.modules.homesystem.validation.ValidationResult;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.logging.Level;

/**
 * Base class for all home commands.
 */
public abstract class BaseHomeCommand extends AbstractCommand {

    protected final HomeModule homeModule;
    protected final HomeService homeService;
    protected final MessageConfig messageConfig;
    protected final HomeConfig homeConfig;

    private static final ThreadLocal<CommandSender> currentSender = new ThreadLocal<>();

    public BaseHomeCommand(@NotNull AdventureSMPCore corePlugin, @NotNull HomeModule homeModule,
                          @NotNull String commandName, @Nullable String permission, boolean playerOnly,
                          @NotNull String description, @NotNull String usageMessage, @Nullable List<String> aliases) {
        super(corePlugin, commandName, permission, playerOnly, description, usageMessage, aliases);
        this.homeModule = homeModule;
        this.homeService = homeModule.getHomeService();
        this.messageConfig = homeModule.getMessageConfig();
        this.homeConfig = homeModule.getHomeConfig();
    }

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        // Set current sender for async operations
        currentSender.set(sender);
        
        try {
        if (sender instanceof Player player) {
            if (!performSecurityChecks(player, args)) {
                    return true;
                }
            }
            
            return executeCommand(sender, args);
        } finally {
            currentSender.remove();
        }
    }

    /**
     * Performs security checks for player commands.
     */
    private boolean performSecurityChecks(@NotNull Player player, @NotNull String[] args) {
        if (corePlugin.getSecurityManager() == null) {
            return true;
        }
        
        // Rate limiting check
        if (!corePlugin.getSecurityManager().canExecuteCommand(player.getUniqueId())) {
            return false;
        }
        
        // Input validation for all arguments
        for (String arg : args) {
            if (!corePlugin.getSecurityManager().isValidInput(arg)) {
                sendMessage(player, "invalid_input");
                playSound(player, "error");
                return false;
            }
        }
        
        return true;
    }

    /**
     * Abstract method that for subclasses to implement their specific command logic.
     */
    protected abstract boolean executeCommand(CommandSender sender, String[] args);

    /**
     * Gets cached homes for a player synchronously.
     */
    protected List<Home> getCachedHomes(@NotNull Player player) {
        List<Home> homes = homeService.getCachedHomes(player.getUniqueId());
        return homes != null ? homes : Collections.emptyList();
    }

    /**
     * Gets homes for a player asynchronously.
     */
    protected CompletableFuture<List<Home>> getHomesAsync(@NotNull Player player) {
        return homeService.getHomesAsync(player.getUniqueId());
    }

    /**
     * Finds a specific home asynchronously.
     */
    protected CompletableFuture<Home> findHomeAsync(@NotNull Player player, @NotNull String homeName) {
        return homeService.findHomeAsync(player.getUniqueId(), homeName);
    }

    /**
     * Handles validation result.
     */
    protected boolean handleValidationResult(@NotNull CommandSender sender, @NotNull ValidationResult result) {
        if (result.isSuccess()) {
            return true;
        }

        if (result.getReplacements() != null && result.getReplacements().length > 0) {
            messageConfig.sendMessage(sender, result.getErrorMessageKey(), result.getReplacements());
        } else {
            messageConfig.sendMessage(sender, result.getErrorMessageKey());
        }

        homeModule.playHomeSound(sender, homeConfig.getSound("error"));
        return false;
    }

    /**
     * Provides tab completion for home names for the current player.
     */
    protected List<String> getHomeNameCompletions(@NotNull Player player, @NotNull String currentArg) {
        List<Home> homes = getCachedHomes(player);
        if (homes.isEmpty()) {
            return Collections.emptyList();
        }

        return homes.stream()
                .map(Home::getHomeName)
                .filter(name -> name.toLowerCase().startsWith(currentArg.toLowerCase()))
                .sorted()
                .collect(Collectors.toList());
    }

    /**
     * Executes an operation asynchronously with security tracking and handles the result on the main thread.
     */
    protected <T> void executeAsync(@NotNull CompletableFuture<T> operation, 
                                   @NotNull java.util.function.Consumer<T> onSuccess,
                                   @NotNull java.util.function.Consumer<Throwable> onError) {
        CommandSender sender = getCurrentSender();
        Player player = sender instanceof Player ? (Player) sender : null;
        
        // Track async operation
        if (player != null && corePlugin.getSecurityManager() != null) {
            if (!corePlugin.getSecurityManager().canStartAsyncOperation(player.getUniqueId())) {
                sendMessage(player, "too_many_operations");
                playSound(player, "error");
                return;
            }
        }
        
        operation
            .thenAccept(result -> {
                corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                    try {
                        onSuccess.accept(result);
                    } finally {
                        // Cleanup async operation tracking
                        if (player != null && corePlugin.getSecurityManager() != null) {
                            corePlugin.getSecurityManager().finishAsyncOperation(player.getUniqueId());
                        }
                    }
                });
            })
            .exceptionally(throwable -> {
                corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                    try {
                        onError.accept(throwable);
                    } finally {
                        // Cleanup async operation tracking
                        if (player != null && corePlugin.getSecurityManager() != null) {
                            corePlugin.getSecurityManager().finishAsyncOperation(player.getUniqueId());
                        }
                    }
                });
                return null;
            });
    }

    /**
     * Executes an operation asynchronously.
     */
    protected <T> void executeAsync(@NotNull CompletableFuture<T> operation, 
                                   @NotNull java.util.function.Consumer<T> onSuccess) {
        executeAsync(operation, onSuccess, throwable -> {
            corePlugin.getLogger().log(Level.WARNING, "Error in async operation: " + throwable.getMessage(), throwable);
            CommandSender sender = getCurrentSender();
            if (sender != null) {
                sendMessage(sender, "internal_error");
                playSound(sender, "error");
            }
        });
    }

    /**
     * Gets the current command sender.
     */
    @Nullable
    protected CommandSender getCurrentSender() {
        return currentSender.get();
    }

    /**
     * Gets the current home count for a player.
     */
    protected int getHomeCount(@NotNull Player player) {
        return homeService.getHomeCount(player.getUniqueId());
    }

    /**
     * Plays a home-related sound to the command sender.
     */
    protected void playSound(@NotNull CommandSender sender, @NotNull String soundKey) {
        homeModule.playHomeSound(sender, soundKey);
    }

    /**
     * Sends a message with replacements to the command sender.
     */
    protected void sendMessage(@NotNull CommandSender sender, @NotNull String messageKey, String... replacements) {
        messageConfig.sendMessage(sender, messageKey, replacements);
    }

    /**
     * Checks if a player has homes.
     */
    protected boolean checkHasHomes(@NotNull Player player, @NotNull List<Home> homes) {
        if (homes.isEmpty()) {
            sendMessage(player, "no_homes_set");
            playSound(player, "error");
            return false;
        }
        return true;
    }

    /**
     * Validates input using security manager.
     */
    protected boolean isValidInput(@NotNull String input) {
        if (corePlugin.getSecurityManager() != null) {
            return corePlugin.getSecurityManager().isValidInput(input);
        }
        return input != null && !input.isEmpty() && input.length() < 100;
    }
} 