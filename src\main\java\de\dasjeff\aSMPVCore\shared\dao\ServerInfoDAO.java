package de.dasjeff.aSMPVCore.shared.dao;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.DatabaseManager;
import de.dasjeff.aSMPVCore.shared.models.ServerInfo;
import org.jetbrains.annotations.NotNull;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Data Access Object for ServerInfo operations.
 * Handles all database interactions for the vcore_servers table.
 */
public class ServerInfoDAO {
    
    private final ASMPVCore plugin;
    private final DatabaseManager databaseManager;
    
    public ServerInfoDAO(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
        this.databaseManager = plugin.getDatabaseManager();
    }

    /**
     * Registers or updates a server in the database.
     */
    @NotNull
    public CompletableFuture<Boolean> upsertServerInfo(@NotNull ServerInfo serverInfo) {
        return CompletableFuture.supplyAsync(() -> {
            if (!serverInfo.isValid()) {
                plugin.getLogger().warn("Invalid server info provided for upsert: {}", serverInfo);
                return false;
            }

            String sql = """
                INSERT INTO vcore_servers (
                    server_id, server_name, server_type, status, player_count, max_players, last_heartbeat, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    server_name = VALUES(server_name),
                    status = VALUES(status),
                    player_count = VALUES(player_count),
                    max_players = VALUES(max_players),
                    last_heartbeat = VALUES(last_heartbeat)
                """;

            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, serverInfo.getServerId());
                statement.setString(2, serverInfo.getServerName());
                statement.setString(3, serverInfo.getServerType().name());
                statement.setString(4, serverInfo.getStatus().name());
                statement.setInt(5, serverInfo.getPlayerCount());
                statement.setInt(6, serverInfo.getMaxPlayers());
                statement.setTimestamp(7, serverInfo.getLastHeartbeat());
                statement.setTimestamp(8, serverInfo.getCreatedAt());
                
                int rowsAffected = statement.executeUpdate();
                
                plugin.getLogger().debug("Upserted server info for {}: {} rows affected", 
                                       serverInfo.getServerId(), rowsAffected);
                return rowsAffected > 0;
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to upsert server info for {}", 
                                       serverInfo.getServerId(), e);
                return false;
            }
        });
    }

    /**
     * Retrieves server information by server ID.
     */
    @NotNull
    public CompletableFuture<ServerInfo> getServerInfo(@NotNull String serverId) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT * FROM vcore_servers WHERE server_id = ?";
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, serverId);
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    if (resultSet.next()) {
                        return mapResultSetToServerInfo(resultSet);
                    }
                }
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to get server info for ID {}", serverId, e);
            }
            
            return null;
        });
    }

    /**
     * Gets all registered servers.
     */
    @NotNull
    public CompletableFuture<List<ServerInfo>> getAllServers() {
        return CompletableFuture.supplyAsync(() -> {
            List<ServerInfo> servers = new ArrayList<>();
            String sql = "SELECT * FROM vcore_servers ORDER BY server_name";
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql);
                 ResultSet resultSet = statement.executeQuery()) {
                
                while (resultSet.next()) {
                    servers.add(mapResultSetToServerInfo(resultSet));
                }
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to get all servers", e);
            }
            
            return servers;
        });
    }

    /**
     * Gets servers by type.
     */
    @NotNull
    public CompletableFuture<List<ServerInfo>> getServersByType(@NotNull ServerInfo.ServerType serverType) {
        return CompletableFuture.supplyAsync(() -> {
            List<ServerInfo> servers = new ArrayList<>();
            String sql = "SELECT * FROM vcore_servers WHERE server_type = ? ORDER BY server_name";
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, serverType.name());
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    while (resultSet.next()) {
                        servers.add(mapResultSetToServerInfo(resultSet));
                    }
                }
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to get servers by type {}", serverType, e);
            }
            
            return servers;
        });
    }

    /**
     * Gets online servers (based on heartbeat timeout).
     */
    @NotNull
    public CompletableFuture<List<ServerInfo>> getOnlineServers(long heartbeatTimeoutMillis) {
        return CompletableFuture.supplyAsync(() -> {
            List<ServerInfo> servers = new ArrayList<>();
            String sql = """
                SELECT * FROM vcore_servers 
                WHERE status != 'OFFLINE' 
                AND last_heartbeat > DATE_SUB(NOW(), INTERVAL ? SECOND)
                ORDER BY server_name
                """;
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setLong(1, heartbeatTimeoutMillis / 1000); // Convert to seconds
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    while (resultSet.next()) {
                        servers.add(mapResultSetToServerInfo(resultSet));
                    }
                }
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to get online servers", e);
            }
            
            return servers;
        });
    }

    /**
     * Updates server heartbeat.
     */
    @NotNull
    public CompletableFuture<Boolean> updateHeartbeat(@NotNull String serverId, 
                                                     @NotNull ServerInfo.ServerStatus status,
                                                     int playerCount, 
                                                     int maxPlayers) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                UPDATE vcore_servers 
                SET status = ?, player_count = ?, max_players = ?, last_heartbeat = NOW()
                WHERE server_id = ?
                """;
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, status.name());
                statement.setInt(2, playerCount);
                statement.setInt(3, maxPlayers);
                statement.setString(4, serverId);
                
                int rowsAffected = statement.executeUpdate();
                return rowsAffected > 0;
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to update heartbeat for server {}", serverId, e);
                return false;
            }
        });
    }

    /**
     * Marks servers as offline if they haven't sent heartbeat within timeout.
     */
    @NotNull
    public CompletableFuture<Integer> markStaleServersOffline(long heartbeatTimeoutMillis) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                UPDATE vcore_servers 
                SET status = 'OFFLINE', player_count = 0
                WHERE status != 'OFFLINE' 
                AND last_heartbeat < DATE_SUB(NOW(), INTERVAL ? SECOND)
                """;
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setLong(1, heartbeatTimeoutMillis / 1000); // Convert to seconds
                
                int rowsAffected = statement.executeUpdate();
                
                if (rowsAffected > 0) {
                    plugin.getLogger().info("Marked {} stale servers as offline", rowsAffected);
                }
                
                return rowsAffected;
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to mark stale servers offline", e);
                return 0;
            }
        });
    }

    /**
     * Removes a server from the registry.
     */
    @NotNull
    public CompletableFuture<Boolean> removeServer(@NotNull String serverId) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "DELETE FROM vcore_servers WHERE server_id = ?";
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, serverId);
                
                int rowsAffected = statement.executeUpdate();
                
                plugin.getLogger().info("Removed server {} from registry", serverId);
                return rowsAffected > 0;
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to remove server {}", serverId, e);
                return false;
            }
        });
    }

    /**
     * Maps a ResultSet row to a ServerInfo object.
     */
    @NotNull
    private ServerInfo mapResultSetToServerInfo(@NotNull ResultSet resultSet) throws SQLException {
        return new ServerInfo(
            resultSet.getString("server_id"),
            resultSet.getString("server_name"),
            ServerInfo.ServerType.valueOf(resultSet.getString("server_type")),
            ServerInfo.ServerStatus.valueOf(resultSet.getString("status")),
            resultSet.getInt("player_count"),
            resultSet.getInt("max_players"),
            resultSet.getTimestamp("last_heartbeat"),
            resultSet.getTimestamp("created_at"),
            null, // TPS - will be added later for Paper servers
            null, // Memory used - will be added later
            null, // Memory max - will be added later
            null  // Version - will be added later
        );
    }
}
