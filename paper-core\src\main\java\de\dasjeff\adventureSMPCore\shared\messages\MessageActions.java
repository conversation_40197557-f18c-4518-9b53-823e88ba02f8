package de.dasjeff.adventureSMPCore.shared.messages;

/**
 * Constants for message actions used in VCore communication.
 * These must match the VCore MessageActions exactly.
 */
public final class MessageActions {

    // Generic actions
    public static final String REQUEST = "request";
    public static final String RESPONSE = "response";
    public static final String UPDATE = "update";
    public static final String CREATE = "create";
    public static final String DELETE = "delete";
    public static final String SYNC = "sync";

    // Server actions
    public static final String HEARTBEAT = "heartbeat";
    public static final String REGISTER_SERVER = "register-server";
    public static final String UNREGISTER_SERVER = "unregister-server";
    public static final String UPDATE_SERVER = "update-server";
    public static final String LIST_SERVERS = "list-servers";

    // Player actions
    public static final String JOIN_PLAYER = "join-player";
    public static final String QUIT_PLAYER = "quit-player";
    public static final String SWITCH_SERVER = "switch-server";
    public static final String UPDATE_PLAYER = "update-player";
    public static final String SEARCH_PLAYER = "search-player";

    // Session actions
    public static final String START_SESSION = "start-session";
    public static final String END_SESSION = "end-session";
    public static final String UPDATE_SESSION = "update-session";

    // Punishment actions
    public static final String BAN_PLAYER = "ban-player";
    public static final String UNBAN_PLAYER = "unban-player";
    public static final String MUTE_PLAYER = "mute-player";
    public static final String UNMUTE_PLAYER = "unmute-player";
    public static final String KICK_PLAYER = "kick-player";
    public static final String WARN_PLAYER = "warn-player";
    public static final String CHECK_PUNISHMENT = "check-punishment";

    // Report actions
    public static final String CREATE_REPORT = "create-report";
    public static final String UPDATE_REPORT = "update-report";
    public static final String CLOSE_REPORT = "close-report";
    public static final String ASSIGN_REPORT = "assign-report";
    public static final String LIST_REPORTS = "list-reports";

    // Chat actions
    public static final String SEND_MESSAGE = "send-message";
    public static final String BROADCAST_MESSAGE = "broadcast-message";
    public static final String PRIVATE_MESSAGE = "private-message";

    // Cache actions
    public static final String INVALIDATE_CACHE = "invalidate-cache";
    public static final String UPDATE_CACHE = "update-cache";
    public static final String SYNC_CACHE = "sync-cache";

    // System actions
    public static final String SHUTDOWN = "shutdown";
    public static final String MAINTENANCE = "maintenance";
    public static final String RELOAD = "reload";
    public static final String STATUS = "status";

    private MessageActions() {
        // Utility class
    }
}
