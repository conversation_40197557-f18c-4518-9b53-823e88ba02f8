#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=你已經連接到這個伺服器\!
velocity.error.already-connected-proxy=您已經連接到此代理伺服器了！
velocity.error.already-connecting=您已經在嘗試連接伺服器了！
velocity.error.cant-connect=無法法連接至 {0}：{1}
velocity.error.connecting-server-error=無法將您連接至 {0}，請稍后再試。
velocity.error.connected-server-error=你與 {0} 的連線出現問題
velocity.error.internal-server-connection-error=出現內部伺服器連線問題
velocity.error.logging-in-too-fast=你登錄太快，請稍後再試
velocity.error.online-mode-only=你沒有登入你的Minecraft賬號。如果你已經登入，請嘗試重啟你的Minecraft用戶端。
velocity.error.player-connection-error=你的連線發生內部錯誤
velocity.error.modern-forwarding-needs-new-client=此伺服器只兼容Minecraft版本1.13以上
velocity.error.modern-forwarding-failed=你的伺服器未向代理伺服器發送轉發請求，請確保你的伺服器已配置Velocity轉發
velocity.error.moved-to-new-server=你已被踢出{0}\:{1}
velocity.error.no-available-servers=當前沒有可以連接你的伺服器，請稍後再試或聯繫管理員
velocity.error.illegal-chat-characters=Illegal characters in chat
# Commands
velocity.command.generic-error=執行命令時發生錯誤
velocity.command.command-does-not-exist=此命令不存在
velocity.command.players-only=只有玩家能執行此命令
velocity.command.server-does-not-exist=指定的伺服器 {0} 不存在
velocity.command.player-not-found=The specified player {0} does not exist.
velocity.command.server-current-server=您已連接至 {0}。
velocity.command.server-too-many=設置的伺服器國多，請使用 Tab 鍵補全以查看所有可用的服务器。
velocity.command.server-available=可用的伺服器：
velocity.command.server-tooltip-player-online={0} 個玩家在線
velocity.command.server-tooltip-players-online={0} 個玩家在線
velocity.command.server-tooltip-current-server=當前已連接至此伺服器
velocity.command.server-tooltip-offer-connect-server=點擊連接至此伺服器
velocity.command.glist-player-singular=共有 {0} 個玩家已連接至此代理伺服器。
velocity.command.glist-player-plural=共有 {0} 個玩家已連接至此代理伺服器。
velocity.command.glist-view-all=使用 /glist all 命令來查看所有伺服器的玩家列表。
velocity.command.reload-success=Velocity 配置重新加載完成。
velocity.command.reload-failure=無法重新加載 Velocity 配置，請查看控制台了解詳情。
velocity.command.version-copyright=Copyright 2018-2023 {0} （ {1} 的授權條款爲： GNU 通用公共授權條款第三版）
velocity.command.no-plugins=目前未有安裝任何 Velocity 插件。
velocity.command.plugins-list=插件： {0}
velocity.command.plugin-tooltip-website=網站： {0}
velocity.command.plugin-tooltip-author=作者： {0}
velocity.command.plugin-tooltip-authors=作者： {0}
velocity.command.dump-uploading=正在收集並上載 Velocity 設定資料...
velocity.command.dump-send-error=與 Velocity 伺服器通信時發生錯誤，伺服器可能暫時不可用或您的網路設置存在問題。您可在 Velocity 伺服器日志或控制台中了解詳情。
velocity.command.dump-success=已創建關於此代理的匿名反饋數據。若開發人員需要，您可與其分享以下鏈接：
velocity.command.dump-will-expire=此鏈接將於幾天後后過期。
velocity.command.dump-server-error=Velocity 伺服器發生錯誤，無法完成轉儲數據。請聯繫 Velocity 開發人員並從 Velocity 控制台或日志中提供詳細的錯誤信息。
velocity.command.dump-offline=可能原因：系统 DNS 設置無效或無網絡連接
velocity.command.send-usage=/send <player> <server>
# Kick
velocity.kick.shutdown=Proxy shutting down.