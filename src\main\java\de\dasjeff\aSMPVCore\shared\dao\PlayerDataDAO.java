package de.dasjeff.aSMPVCore.shared.dao;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.DatabaseManager;
import de.dasjeff.aSMPVCore.shared.models.PlayerData;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Data Access Object for PlayerData operations.
 * Handles all database interactions for the central players table.
 */
public class PlayerDataDAO {
    
    private final ASMPVCore plugin;
    private final DatabaseManager databaseManager;
    
    public PlayerDataDAO(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
        this.databaseManager = plugin.getDatabaseManager();
    }

    /**
     * Creates or updates a player's data.
     */
    @NotNull
    public CompletableFuture<Boolean> upsertPlayerData(@NotNull PlayerData playerData) {
        return CompletableFuture.supplyAsync(() -> {
            if (!playerData.isValid()) {
                plugin.getLogger().warn("Invalid player data provided for upsert: {}", playerData);
                return false;
            }

            String sql = """
                INSERT INTO players (
                    player_uuid, last_known_name, first_seen, last_seen,
                    last_server, current_server, total_playtime, session_count,
                    last_ip, last_country, is_vpn_user, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    last_known_name = VALUES(last_known_name),
                    last_seen = VALUES(last_seen),
                    last_server = VALUES(last_server),
                    current_server = VALUES(current_server),
                    total_playtime = VALUES(total_playtime),
                    session_count = VALUES(session_count),
                    last_ip = VALUES(last_ip),
                    last_country = VALUES(last_country),
                    is_vpn_user = VALUES(is_vpn_user),
                    updated_at = VALUES(updated_at)
                """;

            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                setPlayerDataParameters(statement, playerData);
                int rowsAffected = statement.executeUpdate();
                
                plugin.getLogger().debug("Upserted player data for {}: {} rows affected", 
                                       playerData.getLastKnownName(), rowsAffected);
                return rowsAffected > 0;
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to upsert player data for {}", 
                                       playerData.getLastKnownName(), e);
                return false;
            }
        });
    }

    /**
     * Retrieves player data by UUID.
     */
    @NotNull
    public CompletableFuture<PlayerData> getPlayerData(@NotNull UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT * FROM players WHERE player_uuid = ?";
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, playerUuid.toString());
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    if (resultSet.next()) {
                        return mapResultSetToPlayerData(resultSet);
                    }
                }
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to get player data for UUID {}", playerUuid, e);
            }
            
            return null;
        });
    }

    /**
     * Retrieves player data by name (most recent).
     */
    @NotNull
    public CompletableFuture<PlayerData> getPlayerDataByName(@NotNull String playerName) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT * FROM players WHERE last_known_name = ? ORDER BY last_seen DESC LIMIT 1";
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, playerName);
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    if (resultSet.next()) {
                        return mapResultSetToPlayerData(resultSet);
                    }
                }
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to get player data for name {}", playerName, e);
            }
            
            return null;
        });
    }

    /**
     * Searches for players by name prefix.
     */
    @NotNull
    public CompletableFuture<List<PlayerData>> searchPlayersByName(@NotNull String namePrefix, int limit) {
        return CompletableFuture.supplyAsync(() -> {
            List<PlayerData> players = new ArrayList<>();
            String sql = "SELECT * FROM players WHERE last_known_name LIKE ? ORDER BY last_seen DESC LIMIT ?";
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, namePrefix + "%");
                statement.setInt(2, Math.min(limit, 100)); // Cap at 100 results
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    while (resultSet.next()) {
                        players.add(mapResultSetToPlayerData(resultSet));
                    }
                }
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to search players by name prefix {}", namePrefix, e);
            }
            
            return players;
        });
    }

    /**
     * Gets players by IP address.
     */
    @NotNull
    public CompletableFuture<List<PlayerData>> getPlayersByIp(@NotNull String ipAddress) {
        return CompletableFuture.supplyAsync(() -> {
            List<PlayerData> players = new ArrayList<>();
            String sql = "SELECT * FROM players WHERE last_ip = ? ORDER BY last_seen DESC";
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, ipAddress);
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    while (resultSet.next()) {
                        players.add(mapResultSetToPlayerData(resultSet));
                    }
                }
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to get players by IP {}", ipAddress, e);
            }
            
            return players;
        });
    }

    /**
     * Updates player's current server.
     */
    @NotNull
    public CompletableFuture<Boolean> updatePlayerServer(@NotNull UUID playerUuid, 
                                                        @Nullable String serverId) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                UPDATE players 
                SET current_server = ?, last_server = COALESCE(current_server, last_server), updated_at = NOW()
                WHERE player_uuid = ?
                """;
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, serverId);
                statement.setString(2, playerUuid.toString());
                
                int rowsAffected = statement.executeUpdate();
                return rowsAffected > 0;
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to update player server for UUID {}", playerUuid, e);
                return false;
            }
        });
    }

    /**
     * Adds playtime to a player's total.
     */
    @NotNull
    public CompletableFuture<Boolean> addPlaytime(@NotNull UUID playerUuid, long additionalSeconds) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                UPDATE players 
                SET total_playtime = total_playtime + ?, session_count = session_count + 1, updated_at = NOW()
                WHERE player_uuid = ?
                """;
            
            try (Connection connection = databaseManager.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setLong(1, Math.max(0, additionalSeconds));
                statement.setString(2, playerUuid.toString());
                
                int rowsAffected = statement.executeUpdate();
                return rowsAffected > 0;
                
            } catch (SQLException e) {
                plugin.getLogger().error("Failed to add playtime for UUID {}", playerUuid, e);
                return false;
            }
        });
    }

    /**
     * Maps a ResultSet row to a PlayerData object.
     */
    @NotNull
    private PlayerData mapResultSetToPlayerData(@NotNull ResultSet resultSet) throws SQLException {
        return new PlayerData(
            UUID.fromString(resultSet.getString("player_uuid")),
            resultSet.getString("last_known_name"),
            resultSet.getTimestamp("first_seen"),
            resultSet.getTimestamp("last_seen"),
            resultSet.getString("last_server"),
            resultSet.getString("current_server"),
            resultSet.getLong("total_playtime"),
            resultSet.getInt("session_count"),
            resultSet.getString("last_ip"),
            resultSet.getString("last_country"),
            resultSet.getBoolean("is_vpn_user"),
            resultSet.getTimestamp("created_at"),
            resultSet.getTimestamp("updated_at")
        );
    }

    /**
     * Sets parameters for a PlayerData PreparedStatement.
     */
    private void setPlayerDataParameters(@NotNull PreparedStatement statement, 
                                       @NotNull PlayerData playerData) throws SQLException {
        statement.setString(1, playerData.getPlayerUuid().toString());
        statement.setString(2, playerData.getLastKnownName());
        statement.setTimestamp(3, playerData.getFirstSeen());
        statement.setTimestamp(4, playerData.getLastSeen());
        statement.setString(5, playerData.getLastServer());
        statement.setString(6, playerData.getCurrentServer());
        statement.setLong(7, playerData.getTotalPlaytime());
        statement.setInt(8, playerData.getSessionCount());
        statement.setString(9, playerData.getLastIp());
        statement.setString(10, playerData.getLastCountry());
        statement.setBoolean(11, playerData.isVpnUser());
        statement.setTimestamp(12, playerData.getCreatedAt());
        statement.setTimestamp(13, playerData.getUpdatedAt());
    }
}
