package de.dasjeff.adventureSMPCore.managers;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import org.bukkit.configuration.InvalidConfigurationException;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.logging.Level;

public class ConfigManager {

    private final AdventureSMPCore corePlugin;
    private FileConfiguration mainConfig;
    private File mainConfigFile;

    public ConfigManager(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
        setupMainConfig();
    }

    private void setupMainConfig() {
        mainConfigFile = new File(corePlugin.getDataFolder(), "config.yml");
        if (!mainConfigFile.exists()) {
            corePlugin.saveResource("config.yml", false);
        }
        mainConfig = YamlConfiguration.loadConfiguration(mainConfigFile);
        loadDefaultMainConfig();
    }

    private void loadDefaultMainConfig() {
        InputStream defaultConfigStream = corePlugin.getResource("config.yml");
        if (defaultConfigStream != null) {
            YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defaultConfigStream, StandardCharsets.UTF_8));
            mainConfig.setDefaults(defaultConfig);
            mainConfig.options().copyDefaults(true);
            saveMainConfig();
        }
    }

    public FileConfiguration getMainConfig() {
        return mainConfig;
    }

    public void saveMainConfig() {
        try {
            mainConfig.save(mainConfigFile);
        } catch (IOException e) {
            corePlugin.getLogger().log(Level.SEVERE, "Could not save main config to " + mainConfigFile, e);
        }
    }

    public void reloadMainConfig() {
        mainConfig = YamlConfiguration.loadConfiguration(mainConfigFile);
        loadDefaultMainConfig();
    }

    /**
     * Helper method to construct the File object for a custom config.
     */
    private File getCustomConfigFile(@Nullable String moduleName, @NotNull String fileName) {
        File parentDir = corePlugin.getDataFolder();
        if (moduleName != null && !moduleName.isEmpty()) {
            parentDir = new File(parentDir, moduleName);
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
        }
        return new File(parentDir, fileName);
    }

    /**
     * Loads a custom YAML configuration file from the plugin's data folder.
     * If the file doesn't exist and a default version is present in the JAR, it will be copied.
     *
     * @param fileName The name of the configuration file (e.g., "homes.yml").
     * @return The FileConfiguration object, or null if an error occurred.
     */
    @Nullable
    public FileConfiguration loadCustomConfig(@NotNull String fileName) {
        return loadCustomConfig(null, fileName, fileName);
    }

    /**
     * Loads a custom YAML configuration file from a module subdirectory.
     *
     * @param moduleName The name of the module, used as a subdirectory. If null or empty, uses plugin's main data folder.
     * @param fileName The name of the configuration file (e.g., "config.yml", "messages.yml").
     * @param resourcePath The path to the default config in the JAR's resources (e.g., "HomeSystem/config.yml" or just "homes_config.yml").
     *                     If the module system stores defaults in module-specific folders in resources, this needs to reflect that.
     * @return The FileConfiguration object, or null if an error occurred.
     */
    @Nullable
    public FileConfiguration loadCustomConfig(@Nullable String moduleName, @NotNull String fileName, @NotNull String resourcePath) {
        File configFile = getCustomConfigFile(moduleName, fileName);
        corePlugin.getLogger().info("[ConfigManager-DEBUG] Attempting to load: " + configFile.getAbsolutePath());
        corePlugin.getLogger().info("[ConfigManager-DEBUG] Resource path in JAR: " + resourcePath);

        if (!configFile.exists()) {
            corePlugin.getLogger().info("[ConfigManager-DEBUG] File " + configFile.getName() + " does not exist. Attempting to save from JAR: " + resourcePath);
            try {
                corePlugin.saveResource(resourcePath, false);
                if (configFile.exists()) {
                    corePlugin.getLogger().info("[ConfigManager-DEBUG] Successfully saved " + resourcePath + " to " + configFile.getAbsolutePath());
                } else {
                    corePlugin.getLogger().warning("[ConfigManager-DEBUG] saveResource called for " + resourcePath + " but file STILL does not exist: " + configFile.getAbsolutePath());
                }
            } catch (IllegalArgumentException e) {
                corePlugin.getLogger().log(Level.SEVERE, "[ConfigManager-DEBUG] IllegalArgumentException when saving resource " + resourcePath + ". Is it in the JAR correctly?", e);
            }
        } else {
            corePlugin.getLogger().info("[ConfigManager-DEBUG] File " + configFile.getName() + " already exists.");
        }

        YamlConfiguration customConfig = new YamlConfiguration();
        try {
            customConfig.load(configFile);

            // Load defaults from JAR for this custom config
            InputStream defaultConfigStream = corePlugin.getResource(resourcePath);
            if (defaultConfigStream != null) {
                YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defaultConfigStream, StandardCharsets.UTF_8));
                customConfig.setDefaults(defaultConfig);
                customConfig.options().copyDefaults(true); // Copy defaults only if keys are missing
                customConfig.save(configFile);
            }
            return customConfig;
        } catch (IOException | InvalidConfigurationException e) {
            corePlugin.getLogger().log(Level.SEVERE, "Could not load custom config " + configFile.getPath(), e);
            return null;
        }
    }


    /**
     * Loads a custom YAML configuration file from the plugin's data folder.
     * If the file doesn't exist and a default version is present in the JAR, it will be copied.
     *
     * @param moduleName The name of the module, used as a subdirectory. If null or empty, uses plugin's main data folder.
     * @param fileName The name of the configuration file (e.g., "homes.yml").
     * @return The FileConfiguration object, or null if an error occurred.
     */
    @Nullable
    public FileConfiguration loadCustomConfig(@Nullable String moduleName, @NotNull String fileName) {
        String resourceInJarPath = (moduleName != null && !moduleName.isEmpty() ? moduleName + "/" : "") + fileName;
        return loadCustomConfig(moduleName, fileName, resourceInJarPath);
    }


    /**
     * Saves a custom YAML configuration file.
     *
     * @param config The FileConfiguration object to save.
     * @param fileName The name of the file to save to (e.g., "homes.yml").
     */
    public void saveCustomConfig(@NotNull FileConfiguration config, @NotNull String fileName) {
        saveCustomConfig(config, null, fileName);
    }

    /**
     * Saves a custom YAML configuration file to a module subdirectory.
     *
     * @param config The FileConfiguration object to save.
     * @param moduleName The name of the module, used as a subdirectory. If null or empty, uses plugin's main data folder.
     * @param fileName The name of the file to save to (e.g., "homes.yml").
     */
    public void saveCustomConfig(@NotNull FileConfiguration config, @Nullable String moduleName, @NotNull String fileName) {
        File configFile = getCustomConfigFile(moduleName, fileName);
        try {
            config.save(configFile);
        } catch (IOException e) {
            corePlugin.getLogger().log(Level.SEVERE, "Could not save custom config to " + configFile, e);
        }
    }
} 