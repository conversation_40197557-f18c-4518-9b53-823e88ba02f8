package de.dasjeff.aSMPVCore.shared.models;

import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Objects;

/**
 * Shared ServerInfo model for ASMP-VCore system.
 * Used for communication between Velocity and Paper servers.
 * Contains comprehensive server information for monitoring and management.
 */
public class ServerInfo {
    
    public enum ServerType {
        PAPER, VELOCITY
    }
    
    public enum ServerStatus {
        ONLINE, OFFLINE, MAINTENANCE
    }
    
    @SerializedName("server_id")
    private final String serverId;
    
    @SerializedName("server_name")
    private final String serverName;
    
    @SerializedName("server_type")
    private final ServerType serverType;
    
    @SerializedName("status")
    private final ServerStatus status;
    
    @SerializedName("player_count")
    private final int playerCount;
    
    @SerializedName("max_players")
    private final int maxPlayers;
    
    @SerializedName("last_heartbeat")
    private final Timestamp lastHeartbeat;
    
    @SerializedName("created_at")
    private final Timestamp createdAt;
    
    // Optional performance metrics
    @SerializedName("tps")
    private final Double tps; // Only for Paper servers
    
    @SerializedName("memory_used")
    private final Long memoryUsed; // in MB
    
    @SerializedName("memory_max")
    private final Long memoryMax; // in MB
    
    @SerializedName("version")
    private final String version;

    /**
     * Full constructor for complete server information.
     */
    public ServerInfo(@NotNull String serverId,
                     @NotNull String serverName,
                     @NotNull ServerType serverType,
                     @NotNull ServerStatus status,
                     int playerCount,
                     int maxPlayers,
                     @NotNull Timestamp lastHeartbeat,
                     @NotNull Timestamp createdAt,
                     @Nullable Double tps,
                     @Nullable Long memoryUsed,
                     @Nullable Long memoryMax,
                     @Nullable String version) {
        this.serverId = Objects.requireNonNull(serverId, "serverId cannot be null");
        this.serverName = Objects.requireNonNull(serverName, "serverName cannot be null");
        this.serverType = Objects.requireNonNull(serverType, "serverType cannot be null");
        this.status = Objects.requireNonNull(status, "status cannot be null");
        this.playerCount = Math.max(0, playerCount);
        this.maxPlayers = Math.max(0, maxPlayers);
        this.lastHeartbeat = Objects.requireNonNull(lastHeartbeat, "lastHeartbeat cannot be null");
        this.createdAt = Objects.requireNonNull(createdAt, "createdAt cannot be null");
        this.tps = tps;
        this.memoryUsed = memoryUsed;
        this.memoryMax = memoryMax;
        this.version = version;
    }

    /**
     * Basic constructor for server registration.
     */
    public ServerInfo(@NotNull String serverId,
                     @NotNull String serverName,
                     @NotNull ServerType serverType) {
        this(serverId, serverName, serverType, ServerStatus.OFFLINE, 0, 0,
             Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
             null, null, null, null);
    }

    /**
     * Constructor for heartbeat updates.
     */
    public ServerInfo(@NotNull String serverId,
                     @NotNull String serverName,
                     @NotNull ServerType serverType,
                     @NotNull ServerStatus status,
                     int playerCount,
                     int maxPlayers) {
        this(serverId, serverName, serverType, status, playerCount, maxPlayers,
             Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
             null, null, null, null);
    }

    // Getters
    @NotNull
    public String getServerId() {
        return serverId;
    }

    @NotNull
    public String getServerName() {
        return serverName;
    }

    @NotNull
    public ServerType getServerType() {
        return serverType;
    }

    @NotNull
    public ServerStatus getStatus() {
        return status;
    }

    public int getPlayerCount() {
        return playerCount;
    }

    public int getMaxPlayers() {
        return maxPlayers;
    }

    @NotNull
    public Timestamp getLastHeartbeat() {
        return lastHeartbeat;
    }

    @NotNull
    public Timestamp getCreatedAt() {
        return createdAt;
    }

    @Nullable
    public Double getTps() {
        return tps;
    }

    @Nullable
    public Long getMemoryUsed() {
        return memoryUsed;
    }

    @Nullable
    public Long getMemoryMax() {
        return memoryMax;
    }

    @Nullable
    public String getVersion() {
        return version;
    }

    // Utility methods

    /**
     * Creates a copy with updated heartbeat timestamp.
     */
    @NotNull
    public ServerInfo withHeartbeatUpdate() {
        return new ServerInfo(serverId, serverName, serverType, status, playerCount, maxPlayers,
                            Timestamp.from(Instant.now()), createdAt, tps, memoryUsed, memoryMax, version);
    }

    /**
     * Creates a copy with updated status.
     */
    @NotNull
    public ServerInfo withStatus(@NotNull ServerStatus newStatus) {
        return new ServerInfo(serverId, serverName, serverType, newStatus, playerCount, maxPlayers,
                            Timestamp.from(Instant.now()), createdAt, tps, memoryUsed, memoryMax, version);
    }

    /**
     * Creates a copy with updated player count.
     */
    @NotNull
    public ServerInfo withPlayerCount(int newPlayerCount, int newMaxPlayers) {
        return new ServerInfo(serverId, serverName, serverType, status, newPlayerCount, newMaxPlayers,
                            Timestamp.from(Instant.now()), createdAt, tps, memoryUsed, memoryMax, version);
    }

    /**
     * Creates a copy with updated performance metrics.
     */
    @NotNull
    public ServerInfo withPerformanceMetrics(@Nullable Double newTps, 
                                           @Nullable Long newMemoryUsed, 
                                           @Nullable Long newMemoryMax) {
        return new ServerInfo(serverId, serverName, serverType, status, playerCount, maxPlayers,
                            Timestamp.from(Instant.now()), createdAt, newTps, newMemoryUsed, newMemoryMax, version);
    }

    /**
     * Checks if the server is considered online based on heartbeat.
     */
    public boolean isOnline(long timeoutMillis) {
        if (status == ServerStatus.OFFLINE) {
            return false;
        }
        long timeSinceHeartbeat = System.currentTimeMillis() - lastHeartbeat.getTime();
        return timeSinceHeartbeat <= timeoutMillis;
    }

    /**
     * Gets the memory usage percentage (0-100).
     */
    public double getMemoryUsagePercentage() {
        if (memoryUsed == null || memoryMax == null || memoryMax == 0) {
            return 0.0;
        }
        return (double) memoryUsed / memoryMax * 100.0;
    }

    /**
     * Validates the server information.
     */
    public boolean isValid() {
        return serverId != null && !serverId.trim().isEmpty() &&
               serverName != null && !serverName.trim().isEmpty() &&
               serverType != null && status != null &&
               playerCount >= 0 && maxPlayers >= 0 &&
               lastHeartbeat != null && createdAt != null &&
               (memoryUsed == null || memoryUsed >= 0) &&
               (memoryMax == null || memoryMax >= 0) &&
               (tps == null || tps >= 0.0);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ServerInfo that = (ServerInfo) o;
        return serverId.equals(that.serverId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(serverId);
    }

    @Override
    public String toString() {
        return "ServerInfo{" +
                "serverId='" + serverId + '\'' +
                ", serverName='" + serverName + '\'' +
                ", serverType=" + serverType +
                ", status=" + status +
                ", playerCount=" + playerCount +
                ", maxPlayers=" + maxPlayers +
                ", lastHeartbeat=" + lastHeartbeat +
                (tps != null ? ", tps=" + tps : "") +
                (memoryUsed != null ? ", memoryUsed=" + memoryUsed + "MB" : "") +
                '}';
    }
}
