package de.dasjeff.aSMPVCore.managers;

import com.google.gson.JsonObject;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;

import java.sql.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;

/**
 * DatabaseManager für das ASMP-VCore Plugin.
 * Verwaltet Datenbankverbindungen mit HikariCP und bietet ein Migrations-System.
 */
public class DatabaseManager {

    private final ASMPVCore plugin;
    private HikariDataSource dataSource;
    private final ExecutorService executorService;

    // Database configuration
    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private final String password;
    private final boolean useSSL;
    private final int poolSize;
    private final long connectionTimeout;
    private final long idleTimeout;
    private final long maxLifetime;

    public DatabaseManager(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;

        JsonObject config = plugin.getConfigManager().getMainConfig();
        JsonObject dbConfig = config.getAsJsonObject("database");

        this.host = dbConfig.get("host").getAsString();
        this.port = dbConfig.get("port").getAsInt();
        this.database = dbConfig.get("database").getAsString();
        this.username = dbConfig.get("username").getAsString();
        this.password = dbConfig.get("password").getAsString();
        this.useSSL = dbConfig.get("useSSL").getAsBoolean();
        this.poolSize = dbConfig.get("poolSize").getAsInt();
        this.connectionTimeout = dbConfig.get("connectionTimeout").getAsLong();
        this.idleTimeout = dbConfig.get("idleTimeout").getAsLong();
        this.maxLifetime = dbConfig.get("maxLifetime").getAsLong();

        this.executorService = Executors.newFixedThreadPool(4, r -> {
            Thread thread = new Thread(r, "ASMP-VCore-Database");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * Initialisiert die Datenbankverbindung und prüft die Konfiguration.
     */
    public boolean initialize() {
        // Check for default configuration
        if (isDefaultConfiguration()) {
            plugin.getLogger().error("==================================================");
            plugin.getLogger().error("ASMP-VCore DETECTED DEFAULT DATABASE CONFIGURATION!");
            plugin.getLogger().error("Please configure your database settings in config.json");
            plugin.getLogger().error("Ensure the database server is running before enabling the plugin.");
            plugin.getLogger().error("Plugin will not enable until configured.");
            plugin.getLogger().error("==================================================");
            return false;
        }

        try {
            setupDataSource();

            if (isConnected()) {
                runMigrations();
                plugin.getLogger().info("DatabaseManager initialized successfully.");
                return true;
            } else {
                plugin.getLogger().error("Failed to establish database connection.");
                return false;
            }

        } catch (Exception e) {
            plugin.getLogger().error("Failed to initialize DatabaseManager!", e);
            return false;
        }
    }

    /**
     * Prüft, ob die Standardkonfiguration verwendet wird.
     */
    private boolean isDefaultConfiguration() {
        return "localhost".equals(host) &&
               port == 3306 &&
               "asmp_vcore".equals(database) &&
               "root".equals(username) &&
               "your_password".equals(password);
    }

    /**
     * Richtet die HikariCP-Datenquelle ein.
     */
    private void setupDataSource() {
        HikariConfig hikariConfig = new HikariConfig();

        // Driver configuration
        String driverClassName = "de.dasjeff.aSMPVCore.libs.mariadb.Driver";
        try {
            Class.forName(driverClassName, true, getClass().getClassLoader());
        } catch (Exception e) {
            plugin.getLogger().error("Failed to load driver class " + driverClassName, e);
        }
        hikariConfig.setDriverClassName(driverClassName);
        hikariConfig.setJdbcUrl(String.format("****************************************************",
                host, port, database, useSSL));
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);

        // Pool configuration
        hikariConfig.setMaximumPoolSize(poolSize);
        hikariConfig.setConnectionTimeout(connectionTimeout);
        hikariConfig.setIdleTimeout(idleTimeout);
        hikariConfig.setMaxLifetime(maxLifetime);
        hikariConfig.setLeakDetectionThreshold(60000);

        // Performance optimizations
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");
        hikariConfig.addDataSourceProperty("useLocalSessionState", "true");
        hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true");
        hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true");
        hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true");
        hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true");
        hikariConfig.addDataSourceProperty("maintainTimeStats", "false");

        this.dataSource = new HikariDataSource(hikariConfig);

        plugin.getLogger().info("Database connection pool established to {}:{}/{}", host, port, database);
    }

    /**
     * Prüft, ob eine Verbindung zur Datenbank besteht.
     */
    public boolean isConnected() {
        if (dataSource == null) {
            return false;
        }

        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (SQLException e) {
            plugin.getLogger().error("Database connection test failed!", e);
            return false;
        }
    }

    /**
     * Führt Datenbank-Migrationen aus.
     */
    private void runMigrations() {
        try {
            createMigrationsTable();

            // Run core migrations
            runMigration("001_create_core_tables", this::createCoreTables);
            runMigration("002_create_players_table", this::createPlayersTable);
            runMigration("003_create_player_sessions_table", this::createPlayerSessionsTable);
            runMigration("004_create_punishment_tables", this::createPunishmentTables);
            runMigration("005_create_player_lookup_tables", this::createPlayerLookupTables);
            runMigration("006_create_report_tables", this::createReportTables);

            plugin.getLogger().info("Database migrations completed successfully.");

        } catch (SQLException e) {
            plugin.getLogger().error("Failed to run database migrations!", e);
            throw new RuntimeException("Migration failure", e);
        }
    }

    /**
     * Erstellt die Migrations-Tabelle.
     */
    private void createMigrationsTable() throws SQLException {
        String sql = """
            CREATE TABLE IF NOT EXISTS vcore_migrations (
                id VARCHAR(255) PRIMARY KEY,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_executed_at (executed_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """;

        executeUpdate(sql);
    }

    /**
     * Führt eine Migration aus, falls sie noch nicht ausgeführt wurde.
     */
    private void runMigration(String migrationId, MigrationTask task) throws SQLException {
        if (isMigrationExecuted(migrationId)) {
            return;
        }

        plugin.getLogger().info("Running migration: {}", migrationId);

        try (Connection connection = getConnection()) {
            connection.setAutoCommit(false);

            try {
                task.execute(connection);
                markMigrationExecuted(connection, migrationId);
                connection.commit();

                plugin.getLogger().info("Migration completed: {}", migrationId);

            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }
        }
    }

    /**
     * Prüft, ob eine Migration bereits ausgeführt wurde.
     */
    private boolean isMigrationExecuted(String migrationId) throws SQLException {
        String sql = "SELECT COUNT(*) FROM vcore_migrations WHERE id = ?";

        try (Connection connection = getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {

            stmt.setString(1, migrationId);

            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next() && rs.getInt(1) > 0;
            }
        }
    }

    /**
     * Markiert eine Migration als ausgeführt.
     */
    private void markMigrationExecuted(Connection connection, String migrationId) throws SQLException {
        String sql = "INSERT INTO vcore_migrations (id) VALUES (?)";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, migrationId);
            stmt.executeUpdate();
        }
    }

    /**
     * Erstellt die Core-Tabellen.
     */
    private void createCoreTables(Connection connection) throws SQLException {
        // Server registry table
        String serverRegistrySQL = """
            CREATE TABLE IF NOT EXISTS vcore_servers (
                server_id VARCHAR(64) PRIMARY KEY,
                server_name VARCHAR(255) NOT NULL,
                server_type ENUM('PAPER', 'VELOCITY') NOT NULL,
                last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                status ENUM('ONLINE', 'OFFLINE', 'MAINTENANCE') DEFAULT 'OFFLINE',
                player_count INT DEFAULT 0,
                max_players INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_server_type (server_type),
                INDEX idx_status (status),
                INDEX idx_last_heartbeat (last_heartbeat)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """;

        executeUpdate(connection, serverRegistrySQL);
    }

    /**
     * Erstellt die zentrale Players-Tabelle.
     */
    private void createPlayersTable(Connection connection) throws SQLException {
        String playersTableSQL = """
            CREATE TABLE IF NOT EXISTS players (
                player_uuid CHAR(36) PRIMARY KEY,
                last_known_name VARCHAR(16) NOT NULL,
                first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_server VARCHAR(64) NULL,
                current_server VARCHAR(64) NULL,
                total_playtime BIGINT DEFAULT 0,
                session_count INT DEFAULT 0,
                last_ip VARCHAR(45) NULL,
                last_country VARCHAR(2) NULL,
                is_vpn_user BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_last_known_name (last_known_name),
                INDEX idx_last_seen (last_seen),
                INDEX idx_last_server (last_server),
                INDEX idx_current_server (current_server),
                INDEX idx_last_ip (last_ip),
                INDEX idx_last_country (last_country),
                INDEX idx_is_vpn_user (is_vpn_user),
                INDEX idx_created_at (created_at),
                INDEX idx_updated_at (updated_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """;

        executeUpdate(connection, playersTableSQL);
        plugin.getLogger().info("Players table created successfully");
    }

    /**
     * Erstellt die Player-Sessions-Tabelle.
     */
    private void createPlayerSessionsTable(Connection connection) throws SQLException {
        String sessionsTableSQL = """
            CREATE TABLE IF NOT EXISTS player_sessions (
                session_id CHAR(36) PRIMARY KEY,
                player_uuid CHAR(36) NOT NULL,
                player_name VARCHAR(16) NOT NULL,
                server_id VARCHAR(64) NOT NULL,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP NULL,
                duration_seconds BIGINT DEFAULT 0,
                status ENUM('ACTIVE', 'COMPLETED', 'DISCONNECTED') DEFAULT 'ACTIVE',
                join_ip VARCHAR(45) NULL,
                join_country VARCHAR(2) NULL,
                disconnect_reason TEXT NULL,
                INDEX idx_player_uuid (player_uuid),
                INDEX idx_player_name (player_name),
                INDEX idx_server_id (server_id),
                INDEX idx_start_time (start_time),
                INDEX idx_status (status),
                INDEX idx_join_ip (join_ip),
                INDEX idx_join_country (join_country),
                FOREIGN KEY (player_uuid) REFERENCES players(player_uuid) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """;

        executeUpdate(connection, sessionsTableSQL);
        plugin.getLogger().info("Player sessions table created successfully");
    }

    /**
     * Erstellt die Punishment-Tabellen (Platzhalter für Phase 3).
     */
    private void createPunishmentTables(Connection connection) throws SQLException {
        // Placeholder - will be implemented in Phase 3
        plugin.getLogger().info("Punishment tables migration placeholder - will be implemented in Phase 3");
    }

    /**
     * Erstellt die Player-Lookup-Tabellen (Platzhalter für Phase 3).
     */
    private void createPlayerLookupTables(Connection connection) throws SQLException {
        // Placeholder - will be implemented in Phase 3
        plugin.getLogger().info("Player lookup tables migration placeholder - will be implemented in Phase 3");
    }

    /**
     * Erstellt die Report-Tabellen (Platzhalter für Phase 3).
     */
    private void createReportTables(Connection connection) throws SQLException {
        // Placeholder - will be implemented in Phase 3
        plugin.getLogger().info("Report tables migration placeholder - will be implemented in Phase 3");
    }

    /**
     * Gibt eine Datenbankverbindung zurück.
     */
    @NotNull
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("DataSource is not initialized");
        }
        return dataSource.getConnection();
    }

    /**
     * Führt ein SQL-Update aus.
     */
    public int executeUpdate(@NotNull String sql, Object... params) throws SQLException {
        try (Connection connection = getConnection()) {
            return executeUpdate(connection, sql, params);
        }
    }

    /**
     * Führt ein SQL-Update mit einer bestehenden Verbindung aus.
     */
    public int executeUpdate(@NotNull Connection connection, @NotNull String sql, Object... params) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            return stmt.executeUpdate();
        }
    }

    /**
     * Führt eine asynchrone Datenbankoperation aus.
     */
    public <T> CompletableFuture<T> executeAsync(@NotNull Function<Connection, T> operation) {
        return CompletableFuture.supplyAsync(() -> {
            try (Connection connection = getConnection()) {
                return operation.apply(connection);
            } catch (SQLException e) {
                plugin.getLogger().error("Async database operation failed!", e);
                throw new RuntimeException(e);
            }
        }, executorService);
    }

    /**
     * Beendet den DatabaseManager.
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }

        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            plugin.getLogger().info("Database connection pool closed.");
        }
    }

    @FunctionalInterface
    private interface MigrationTask {
        void execute(Connection connection) throws SQLException;
    }
}
