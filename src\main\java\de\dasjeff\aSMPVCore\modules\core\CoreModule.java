package de.dasjeff.aSMPVCore.modules.core;

import com.google.gson.JsonObject;
import de.dasjeff.aSMPVCore.events.EventHandler;
import de.dasjeff.aSMPVCore.events.EventPriority;
import de.dasjeff.aSMPVCore.modules.AbstractModule;

/**
 * CoreModule - Grundlegendes Modul für Systemfunktionen.
 * Verwaltet nur grundlegende Event-System-Funktionen.
 * Server-Management wurde in das ServerManagementModule ausgelagert.
 */
public class CoreModule extends AbstractModule {

    public CoreModule() {
        super("Core", "1.0.0", "Core system module for ASMP-VCore - Basic functionality only");
    }

    @Override
    protected void doLoad() throws Exception {
        info("Loading Core module...");

        // Register event listeners
        if (plugin.getEventManager() != null) {
            plugin.getEventManager().registerListener(this);
            debug("Event listeners registered");
        }

        info("Core module loaded successfully");
    }

    @Override
    protected void doEnable() throws Exception {
        info("Enabling Core module...");

        info("Core module enabled successfully");
    }

    @Override
    protected void doDisable() throws Exception {
        info("Disabling Core module...");

        // Unregister event listeners
        if (plugin.getEventManager() != null) {
            plugin.getEventManager().unregisterListener(this);
            debug("Event listeners unregistered");
        }

        info("Core module disabled successfully");
    }

    @Override
    protected void doReload() throws Exception {
        info("Reloading Core module...");

        info("Core module reloaded successfully");
    }

    /**
     * Event-Handler für Modul-Events.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onModuleEvent(de.dasjeff.aSMPVCore.managers.ModuleManager.ModuleEvent event) {
        debug("Module event received: {}", event);
    }

    /**
     * Gibt grundlegende Core-Statistiken zurück.
     */
    public JsonObject getCoreStats() {
        JsonObject stats = new JsonObject();
        stats.addProperty("uptime", System.currentTimeMillis());

        // Add module statistics
        if (plugin.getModuleManager() != null) {
            stats.addProperty("totalModules", plugin.getModuleManager().getModuleCount());
            stats.addProperty("enabledModules", plugin.getModuleManager().getEnabledModuleCount());
        }

        return stats;
    }
}
