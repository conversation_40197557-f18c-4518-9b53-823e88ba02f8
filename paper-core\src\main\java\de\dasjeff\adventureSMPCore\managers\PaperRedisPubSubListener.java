package de.dasjeff.adventureSMPCore.managers;

import redis.clients.jedis.JedisPubSub;
import org.jetbrains.annotations.NotNull;

/**
 * Redis Pub/Sub listener for Paper Core.
 * Handles incoming messages from VCore and other Paper servers.
 */
public class PaperRedisPubSubListener extends JedisPubSub {

    private final RedisManager redisManager;

    public PaperRedisPubSubListener(@NotNull RedisManager redisManager) {
        this.redisManager = redisManager;
    }

    @Override
    public void onMessage(String channel, String message) {
        try {
            redisManager.getPlugin().getLogger().fine("Received Redis message on channel: " + channel);
            redisManager.handleMessage(channel, message);
        } catch (Exception e) {
            redisManager.getPlugin().getLogger().warning("Error processing Redis message on channel " + 
                    channel + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onSubscribe(String channel, int subscribedChannels) {
        redisManager.getPlugin().getLogger().info("Subscribed to Redis channel: " + channel + 
                " (total: " + subscribedChannels + ")");
    }

    @Override
    public void onUnsubscribe(String channel, int subscribedChannels) {
        redisManager.getPlugin().getLogger().info("Unsubscribed from Redis channel: " + channel + 
                " (remaining: " + subscribedChannels + ")");
    }

    @Override
    public void onPSubscribe(String pattern, int subscribedChannels) {
        redisManager.getPlugin().getLogger().info("Subscribed to Redis pattern: " + pattern + 
                " (total: " + subscribedChannels + ")");
    }

    @Override
    public void onPUnsubscribe(String pattern, int subscribedChannels) {
        redisManager.getPlugin().getLogger().info("Unsubscribed from Redis pattern: " + pattern + 
                " (remaining: " + subscribedChannels + ")");
    }

    @Override
    public void onPMessage(String pattern, String channel, String message) {
        try {
            redisManager.getPlugin().getLogger().fine("Received Redis pattern message: " + pattern + 
                    " on channel: " + channel);
            redisManager.handleMessage(channel, message);
        } catch (Exception e) {
            redisManager.getPlugin().getLogger().warning("Error processing Redis pattern message on channel " + 
                    channel + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
}
