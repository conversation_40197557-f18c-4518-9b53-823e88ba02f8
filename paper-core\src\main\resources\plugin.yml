name: AdventureSMP-Core
version: '${version}'
main: de.dasjeff.adventureSMPCore.AdventureSMPCore
api-version: '1.21'
author: DasJeff
description: 'Core-Plugin für AdventureSMP mit modularer System-Architektur'

# Command definitions
commands:
  # Core Commands
  core:
    description: 'Core-Management-Befehl für AdventureSMP-Core'
    usage: '/core <status|reload> [component]'
    permission: adventuresmp.core.admin
    permission-message: '§cKein Zugriff auf diesen Befehl!'
    aliases: [advcore]

  # Home System Commands
  home:
    description: 'Teleportiert dich zu einem deiner Homes oder öffnet das Home-GUI'
    usage: '/home [HomeName]'
    permission: adventuresmp.core.homes.home
    permission-message: '§cKein Zugriff auf diesen Befehl!'
    aliases: [h]

  sethome:
    description: 'Setzt ein Home an deiner aktuellen Position'
    usage: '/sethome <HomeName>'
    permission: adventuresmp.core.homes.sethome
    permission-message: '§cKein Zugriff auf diesen Befehl!'
    aliases: [createhome]

  delhome:
    description: 'Löscht eines deiner Homes'
    usage: '/delhome <HomeName>'
    permission: adventuresmp.core.homes.delhome
    permission-message: '§cKein Zugriff auf diesen Befehl!'
    aliases: [removehome]

  adminhome:
    description: 'Verwaltet Homes anderer Spieler'
    usage: '/adminhome <set|delete|deleteall|tp|import> <player> ...'
    permission: adventuresmp.core.homes.admin
    permission-message: '§cKein Zugriff auf diesen Befehl!'
    aliases: [ahome]

# Permission definitions
permissions:
  # Core Permissions
  adventuresmp.core.*:
    description: 'Zugriff auf alle Core-Features'
    children:
      adventuresmp.core.admin.*: true
      adventuresmp.core.homes.*: true

  # Admin Permissions
  adventuresmp.core.admin.*:
    description: 'Zugriff auf alle administrativen Core-Funktionen'
    default: op
    children:
      adventuresmp.core.admin.status: true
      adventuresmp.core.admin.reload: true
      adventuresmp.core.homes.admin: true

  adventuresmp.core.admin.status:
    description: 'Erlaubt die Nutzung des Status-Befehls (/core status)'
    default: op

  adventuresmp.core.admin.reload:
    description: 'Erlaubt das Neuladen von Modulen (/core reload)'
    default: op

  # Home System Permissions
  adventuresmp.core.homes.*:
    description: 'Zugriff auf alle Home-Features'
    default: true
    children:
      adventuresmp.core.homes.home: true
      adventuresmp.core.homes.sethome: true
      adventuresmp.core.homes.delhome: true

  adventuresmp.core.homes.home:
    description: 'Erlaubt das Teleportieren zu Homes'
    default: true

  adventuresmp.core.homes.sethome:
    description: 'Erlaubt das Setzen von Homes'
    default: true

  adventuresmp.core.homes.delhome:
    description: 'Erlaubt das Löschen von Homes'
    default: true

  adventuresmp.core.homes.admin:
    description: 'Erlaubt das Verwalten von Homes anderer Spieler'
    default: op

  adventuresmp.core.homes.admin.set:
    description: 'Erlaubt das Setzen von Homes für andere Spieler'
    default: op

  adventuresmp.core.homes.admin.delete:
    description: 'Erlaubt das Löschen von Homes für andere Spieler'
    default: op

  adventuresmp.core.homes.admin.deleteall:
    description: 'Erlaubt das Löschen aller Homes für einen Spieler'
    default: op

  adventuresmp.core.homes.admin.tp:
    description: 'Erlaubt das Teleportieren zu Homes anderer Spieler'
    default: op

  adventuresmp.core.homes.admin.import:
    description: 'Erlaubt das Importieren von Spielerdaten'
    default: op

  adventuresmp.core.homes.bypass.blacklist:
    description: 'Bypass für Homes in blacklisted Welten'
    default: op

  adventuresmp.core.homes.bypass.cooldown:
    description: 'Bypass für Teleport-Cooldown'
    default: op

  adventuresmp.core.homes.bypass.delay:
    description: 'Bypass für Teleport-Verzögerung'
    default: op

  adventuresmp.core.homes.bypass.movecheck:
    description: 'Bypass für Bewegung während Teleportation'
    default: op

  adventuresmp.core.homes.limit.unlimited:
    description: 'Erlaubt unbegrenzte Anzahl an Homes'
    default: false