package de.dasjeff.adventureSMPCore.modules.homesystem.config;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class HomeConfig {

    private final AdventureSMPCore corePlugin;
    private FileConfiguration config;

    private int defaultMaxHomes;
    private String defaultLimitPermissionPrefix;
    private String unlimitedPermissionNode;
    private int teleportDelaySeconds;
    private int teleportCooldownSeconds;
    private boolean cancelTeleportOnMove;
    private List<String> blacklistedWorlds;
    private Map<String, String> sounds;
    private boolean teleportToOnlyHome;
    private int minHomeNameLength;
    private int maxHomeNameLength;

    public HomeConfig(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
        loadConfig();
    }

    private void loadConfig() {
        setDefaults();
        
        config = corePlugin.getConfigManager().loadCustomConfig(HomeModule.MODULE_NAME, "homes_config.yml");
        if (config == null) {
            corePlugin.getLogger().severe("[" + HomeModule.MODULE_NAME + "] homes_config.yml could not be loaded! Using default values.");
            return;
        }

        // Load config values
        this.defaultMaxHomes = config.getInt("limits.default_max_homes", this.defaultMaxHomes);
        this.defaultLimitPermissionPrefix = config.getString("limits.permission_prefix", this.defaultLimitPermissionPrefix);
        this.unlimitedPermissionNode = config.getString("limits.unlimited_node", this.unlimitedPermissionNode);

        this.teleportDelaySeconds = config.getInt("teleport.delay_seconds", this.teleportDelaySeconds);
        this.teleportCooldownSeconds = config.getInt("teleport.cooldown_seconds", this.teleportCooldownSeconds);
        this.cancelTeleportOnMove = config.getBoolean("teleport.cancel_on_move", this.cancelTeleportOnMove);

        this.blacklistedWorlds = loadStringList(this.blacklistedWorlds);
        this.teleportToOnlyHome = config.getBoolean("teleport_settings.teleport_to_only_home_on_empty_command", this.teleportToOnlyHome);

        // Home name restrictions
        this.minHomeNameLength = config.getInt("home_names.min_length", this.minHomeNameLength);
        this.maxHomeNameLength = config.getInt("home_names.max_length", this.maxHomeNameLength);
    }
    
    private void setDefaults() {
        this.defaultMaxHomes = 1;
        this.defaultLimitPermissionPrefix = "adventuresmp.core.homes.limit.";
        this.unlimitedPermissionNode = "adventuresmp.core.homes.limit.unlimited";
        this.teleportDelaySeconds = 3;
        this.teleportCooldownSeconds = 0;
        this.cancelTeleportOnMove = true;
        this.blacklistedWorlds = Collections.emptyList();
        this.teleportToOnlyHome = true;
        this.minHomeNameLength = 3;
        this.maxHomeNameLength = 25;
    }
    
    /**
     * Helper method to load a String list from config
     */
    private List<String> loadStringList(List<String> defaultList) {
        List<String> configList = config.getStringList("restrictions.blacklisted_worlds");
        return configList.isEmpty() ? defaultList : configList;
    }

    public void reloadConfig() {
        corePlugin.getConfigManager().loadCustomConfig(HomeModule.MODULE_NAME, "homes_config.yml");
        loadConfig();
    }

    // Getters
    public int getDefaultMaxHomes() {
        return defaultMaxHomes;
    }

    public String getDefaultLimitPermissionPrefix() {
        return defaultLimitPermissionPrefix;
    }

    public String getUnlimitedPermissionNode() {
        return unlimitedPermissionNode;
    }

    public int getTeleportDelaySeconds() {
        return teleportDelaySeconds;
    }

    public int getTeleportCooldownSeconds() {
        return teleportCooldownSeconds;
    }

    public boolean isCancelTeleportOnMove() {
        return cancelTeleportOnMove;
    }

    public List<String> getBlacklistedWorlds() {
        return blacklistedWorlds;
    }

    public String getSound(String key) {
        return config != null ? config.getString("sounds." + key, "") : "";
    }

    public Map<String, String> getSounds() {
        return sounds;
    }

    public boolean isTeleportToOnlyHome() {
        return teleportToOnlyHome;
    }

    public int getMinHomeNameLength() {
        return minHomeNameLength;
    }

    public int getMaxHomeNameLength() {
        return maxHomeNameLength;
    }
} 