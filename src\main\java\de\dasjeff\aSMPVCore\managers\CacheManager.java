package de.dasjeff.aSMPVCore.managers;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.gson.JsonObject;
import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * CacheManager für das ASMP-VCore Plugin.
 * Verwaltet zweistufiges Caching mit Caffeine (lokal) und Redis (verteilt).
 */
public class CacheManager {

    private final ASMPVCore plugin;
    private final ConcurrentMap<String, Cache<Object, Object>> caches = new ConcurrentHashMap<>();
    
    // Configuration
    private final boolean enableStatistics;
    private final long defaultMaxSize;
    private final long defaultExpireMinutes;

    public CacheManager(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
        
        JsonObject config = plugin.getConfigManager().getMainConfig();
        JsonObject cacheConfig = config.getAsJsonObject("cache");
        
        this.enableStatistics = cacheConfig.get("enableStatistics").getAsBoolean();
        this.defaultMaxSize = cacheConfig.get("defaultMaxSize").getAsLong();
        this.defaultExpireMinutes = cacheConfig.get("defaultExpireMinutes").getAsLong();
        
        plugin.getLogger().info("CacheManager initialized with statistics: {}", enableStatistics);
    }

    /**
     * Erstellt einen neuen Caffeine-Cache mit den angegebenen Parametern.
     */
    @SuppressWarnings("unchecked")
    public <K, V> Cache<K, V> createCache(@NotNull String cacheName, long maximumSize, 
                                         long expireAfterAccess, @NotNull TimeUnit timeUnit) {
        if (caches.containsKey(cacheName)) {
            throw new IllegalArgumentException("Cache with name '" + cacheName + "' already exists.");
        }

        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(expireAfterAccess, timeUnit);
        
        if (enableStatistics) {
            builder.recordStats();
        }
        
        Cache<Object, Object> newCache = builder.build();
        caches.put(cacheName, newCache);
        
        plugin.getLogger().debug("Created cache '{}' with max size {} and expire after access {} {}", 
                cacheName, maximumSize, expireAfterAccess, timeUnit);
        
        return (Cache<K, V>) newCache;
    }

    /**
     * Erstellt einen neuen Caffeine-Cache mit Standardwerten.
     */
    public <K, V> Cache<K, V> createCache(@NotNull String cacheName) {
        return createCache(cacheName, defaultMaxSize, defaultExpireMinutes, TimeUnit.MINUTES);
    }

    /**
     * Erstellt einen Loading-Cache mit automatischer Wertberechnung.
     */
    @SuppressWarnings("unchecked")
    public <K, V> LoadingCache<K, V> createLoadingCache(@NotNull String cacheName, long maximumSize, 
                                                       long expireAfterAccess, @NotNull TimeUnit timeUnit,
                                                       @NotNull CacheLoader<K, V> loader) {
        if (caches.containsKey(cacheName)) {
            throw new IllegalArgumentException("Cache with name '" + cacheName + "' already exists.");
        }

        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(expireAfterAccess, timeUnit);
        
        if (enableStatistics) {
            builder.recordStats();
        }
        
        LoadingCache<K, V> newCache = (LoadingCache<K, V>) builder.build((CacheLoader<Object, Object>) loader);
        caches.put(cacheName, (Cache<Object, Object>) newCache);
        
        plugin.getLogger().debug("Created loading cache '{}' with max size {} and expire after access {} {}", 
                cacheName, maximumSize, expireAfterAccess, timeUnit);
        
        return newCache;
    }

    /**
     * Erstellt einen Loading-Cache mit Standardwerten.
     */
    public <K, V> LoadingCache<K, V> createLoadingCache(@NotNull String cacheName, @NotNull CacheLoader<K, V> loader) {
        return createLoadingCache(cacheName, defaultMaxSize, defaultExpireMinutes, TimeUnit.MINUTES, loader);
    }

    /**
     * Ruft einen existierenden Cache anhand seines Namens ab.
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public <K, V> Cache<K, V> getCache(@NotNull String cacheName) {
        return (Cache<K, V>) caches.get(cacheName);
    }

    /**
     * Ruft sicher einen Wert aus dem angegebenen Cache ab und berechnet ihn bei Bedarf.
     */
    @Nullable
    public <K, V> V get(@NotNull String cacheName, @NotNull K key, @NotNull Function<? super K, ? extends V> mappingFunction) {
        try {
            Cache<K, V> cache = getCache(cacheName);
            if (cache == null) {
                plugin.getLogger().warn("Attempted to access non-existent cache: {}", cacheName);
                return mappingFunction.apply(key); // Fallback to direct computation
            }
            return cache.get(key, mappingFunction);
        } catch (Exception e) {
            plugin.getLogger().warn("Error accessing cache '{}' for key: {}", cacheName, key, e);
            return mappingFunction.apply(key); // Fallback to direct computation
        }
    }

    /**
     * Ruft sicher einen Wert aus dem angegebenen Cache ab.
     */
    @Nullable
    public <K, V> V getIfPresent(@NotNull String cacheName, @NotNull K key) {
        try {
            Cache<K, V> cache = getCache(cacheName);
            if (cache == null) {
                plugin.getLogger().warn("Attempted to access non-existent cache: {}", cacheName);
                return null;
            }
            return cache.getIfPresent(key);
        } catch (Exception e) {
            plugin.getLogger().warn("Error accessing cache '{}' for key: {}", cacheName, key, e);
            return null;
        }
    }

    /**
     * Speichert sicher einen Wert in den angegebenen Cache.
     */
    public <K, V> void put(@NotNull String cacheName, @NotNull K key, @NotNull V value) {
        try {
            Cache<K, V> cache = getCache(cacheName);
            if (cache == null) {
                plugin.getLogger().warn("Attempted to put into non-existent cache: {}", cacheName);
                return;
            }
            cache.put(key, value);
        } catch (Exception e) {
            plugin.getLogger().warn("Error putting value into cache '{}' for key: {}", cacheName, key, e);
        }
    }

    /**
     * Entfernt einen Wert aus dem angegebenen Cache.
     */
    public <K> void invalidate(@NotNull String cacheName, @NotNull K key) {
        try {
            Cache<K, ?> cache = getCache(cacheName);
            if (cache == null) {
                plugin.getLogger().warn("Attempted to invalidate from non-existent cache: {}", cacheName);
                return;
            }
            cache.invalidate(key);
        } catch (Exception e) {
            plugin.getLogger().warn("Error invalidating cache '{}' for key: {}", cacheName, key, e);
        }
    }

    /**
     * Leert den angegebenen Cache vollständig.
     */
    public void invalidateAll(@NotNull String cacheName) {
        try {
            Cache<?, ?> cache = getCache(cacheName);
            if (cache == null) {
                plugin.getLogger().warn("Attempted to invalidate non-existent cache: {}", cacheName);
                return;
            }
            cache.invalidateAll();
            plugin.getLogger().debug("Invalidated all entries in cache: {}", cacheName);
        } catch (Exception e) {
            plugin.getLogger().warn("Error invalidating all entries in cache: {}", cacheName, e);
        }
    }

    /**
     * Distributed Cache Operations - Integration mit Redis
     */

    /**
     * Speichert einen Wert sowohl im lokalen Cache als auch in Redis.
     */
    public <K, V> void putDistributed(@NotNull String cacheName, @NotNull K key, @NotNull V value, int ttlSeconds) {
        // Store in local cache
        put(cacheName, key, value);
        
        // Store in Redis if available
        if (plugin.getRedisManager() != null) {
            try {
                String redisKey = buildRedisKey(cacheName, key.toString());
                String serializedValue = serializeValue(value);
                plugin.getRedisManager().setex(redisKey, ttlSeconds, serializedValue);
            } catch (Exception e) {
                plugin.getLogger().warn("Failed to store value in Redis for cache '{}' key '{}'", cacheName, key, e);
            }
        }
    }

    /**
     * Ruft einen Wert aus dem lokalen Cache ab, falls nicht vorhanden aus Redis.
     */
    @Nullable
    public <K, V> V getDistributed(@NotNull String cacheName, @NotNull K key, @NotNull Class<V> valueClass) {
        // Try local cache first
        V localValue = getIfPresent(cacheName, key);
        if (localValue != null) {
            return localValue;
        }
        
        // Try Redis if available
        if (plugin.getRedisManager() != null) {
            try {
                String redisKey = buildRedisKey(cacheName, key.toString());
                String serializedValue = plugin.getRedisManager().get(redisKey);
                
                if (serializedValue != null) {
                    V value = deserializeValue(serializedValue, valueClass);
                    if (value != null) {
                        // Store back in local cache
                        put(cacheName, key, value);
                        return value;
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warn("Failed to retrieve value from Redis for cache '{}' key '{}'", cacheName, key, e);
            }
        }
        
        return null;
    }

    /**
     * Invalidiert einen Wert sowohl im lokalen Cache als auch in Redis.
     */
    public <K> void invalidateDistributed(@NotNull String cacheName, @NotNull K key) {
        // Invalidate local cache
        invalidate(cacheName, key);
        
        // Invalidate Redis if available
        if (plugin.getRedisManager() != null) {
            try {
                String redisKey = buildRedisKey(cacheName, key.toString());
                plugin.getRedisManager().del(redisKey);
            } catch (Exception e) {
                plugin.getLogger().warn("Failed to invalidate Redis key for cache '{}' key '{}'", cacheName, key, e);
            }
        }
    }

    /**
     * Erstellt einen Redis-Schlüssel für Cache-Einträge.
     */
    @NotNull
    private String buildRedisKey(@NotNull String cacheName, @NotNull String key) {
        return String.format("asmp:cache:%s:%s", cacheName, key);
    }

    /**
     * Serialisiert einen Wert für Redis-Speicherung.
     */
    @NotNull
    private String serializeValue(@NotNull Object value) {
        // Simple JSON serialization - can be enhanced based on needs
        if (value instanceof String) {
            return (String) value;
        } else if (value instanceof Number || value instanceof Boolean) {
            return value.toString();
        } else {
            // Use Gson for complex objects
            return plugin.getConfigManager().getMainConfig().toString(); // Placeholder
        }
    }

    /**
     * Deserialisiert einen Wert aus Redis.
     */
    @Nullable
    private <V> V deserializeValue(@NotNull String serializedValue, @NotNull Class<V> valueClass) {
        try {
            if (valueClass == String.class) {
                return valueClass.cast(serializedValue);
            } else if (valueClass == Integer.class) {
                return valueClass.cast(Integer.valueOf(serializedValue));
            } else if (valueClass == Long.class) {
                return valueClass.cast(Long.valueOf(serializedValue));
            } else if (valueClass == Boolean.class) {
                return valueClass.cast(Boolean.valueOf(serializedValue));
            }
            // Add more type handling as needed
            return null;
        } catch (Exception e) {
            plugin.getLogger().warn("Failed to deserialize value: {}", serializedValue, e);
            return null;
        }
    }

    /**
     * Gibt Cache-Statistiken zurück, falls aktiviert.
     */
    @Nullable
    public String getCacheStats(@NotNull String cacheName) {
        if (!enableStatistics) {
            return "Statistics are disabled. Enable with cache.enableStatistics=true in config.json";
        }
        
        Cache<?, ?> cache = getCache(cacheName);
        if (cache == null) {
            return "Cache '" + cacheName + "' does not exist.";
        }
        
        return cache.stats().toString();
    }

    /**
     * Prüft, ob ein Cache existiert.
     */
    public boolean cacheExists(@NotNull String cacheName) {
        return caches.containsKey(cacheName);
    }

    /**
     * Gibt die Größe eines Caches zurück.
     */
    public long getCacheSize(@NotNull String cacheName) {
        Cache<?, ?> cache = getCache(cacheName);
        return cache != null ? cache.estimatedSize() : 0;
    }

    /**
     * Gibt alle Cache-Namen zurück.
     */
    @NotNull
    public String[] getCacheNames() {
        return caches.keySet().toArray(new String[0]);
    }

    /**
     * Bereinigt alle Caches.
     */
    public void cleanupAllCaches() {
        for (String cacheName : caches.keySet()) {
            Cache<?, ?> cache = caches.get(cacheName);
            if (cache != null) {
                cache.cleanUp();
            }
        }
        plugin.getLogger().debug("Cleaned up all caches");
    }

    /**
     * Beendet den CacheManager.
     */
    public void shutdown() {
        // Cleanup all caches
        cleanupAllCaches();
        
        // Clear cache registry
        caches.clear();
        
        plugin.getLogger().info("CacheManager shut down.");
    }
}
