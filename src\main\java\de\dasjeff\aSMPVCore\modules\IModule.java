package de.dasjeff.aSMPVCore.modules;

import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;

/**
 * Interface für alle Module im ASMP-VCore Plugin.
 * Definiert den Lebenszyklus und die grundlegenden Funktionen eines Moduls.
 */
public interface IModule {

    /**
     * Gibt den eindeutigen Namen des Moduls zurück.
     * @return Der Name des Moduls.
     */
    @NotNull
    String getName();

    /**
     * Gibt die Version des Moduls zurück.
     * @return Die Version des Moduls.
     */
    @NotNull
    default String getVersion() {
        return "1.0.0";
    }

    /**
     * Gibt eine Beschreibung des Moduls zurück.
     * @return Die Beschreibung des Moduls.
     */
    @NotNull
    default String getDescription() {
        return "No description provided";
    }

    /**
     * Gibt die Abhängigkeiten des Moduls zurück.
     * @return Array von Modulnamen, von denen dieses Modul abhängt.
     */
    @NotNull
    default String[] getDependencies() {
        return new String[0];
    }

    /**
     * Wird aufgerufen, wenn das Plugin geladen wird.
     * Hier sollten grundlegende Initialisierungen durchgeführt werden.
     * @param plugin Die Hauptplugin-Instanz.
     */
    void onLoad(@NotNull ASMPVCore plugin);

    /**
     * Wird aufgerufen, wenn das Modul aktiviert wird.
     * Hier sollten Event-Handler registriert und Services gestartet werden.
     * @param plugin Die Hauptplugin-Instanz.
     */
    void onEnable(@NotNull ASMPVCore plugin);

    /**
     * Wird aufgerufen, wenn das Modul deaktiviert wird.
     * Hier sollten Ressourcen freigegeben und Services gestoppt werden.
     */
    void onDisable();

    /**
     * Wird aufgerufen, wenn das Modul neu geladen wird.
     * Standardimplementierung ruft onDisable() und dann onEnable() auf.
     * @param plugin Die Hauptplugin-Instanz.
     */
    default void onReload(@NotNull ASMPVCore plugin) {
        onDisable();
        onEnable(plugin);
    }

    /**
     * Prüft, ob das Modul aktiviert ist.
     * @return true, wenn das Modul aktiviert ist.
     */
    boolean isEnabled();

    /**
     * Prüft, ob das Modul geladen ist.
     * @return true, wenn das Modul geladen ist.
     */
    boolean isLoaded();
}
