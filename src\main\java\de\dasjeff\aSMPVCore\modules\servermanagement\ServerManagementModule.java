package de.dasjeff.aSMPVCore.modules.servermanagement;

import com.google.gson.JsonObject;
import com.velocitypowered.api.scheduler.ScheduledTask;
import de.dasjeff.aSMPVCore.communication.NetworkMessage;
import de.dasjeff.aSMPVCore.modules.AbstractModule;
import de.dasjeff.aSMPVCore.shared.dao.ServerInfoDAO;
import de.dasjeff.aSMPVCore.shared.models.ServerInfo;
import de.dasjeff.aSMPVCore.shared.messages.MessageTypes;
import de.dasjeff.aSMPVCore.shared.messages.MessageActions;

import java.util.concurrent.TimeUnit;

/**
 * ServerManagementModule - Verwaltet Server-Registrierung, Heartbeat und Monitoring.
 * Zuständig für alle Server-Management-Funktionen im ASMP-VCore System.
 */
public class ServerManagementModule extends AbstractModule {

    private ScheduledTask heartbeatTask;
    private ServerInfoDAO serverInfoDAO;
    private final String serverId;
    private final String serverName;
    private final long heartbeatInterval;

    public ServerManagementModule() {
        super("ServerManagement", "1.0.0", "Server management and monitoring module");

        // These will be set during load phase
        this.serverId = "velocity-proxy";
        this.serverName = "ASMP Network";
        this.heartbeatInterval = 30; // seconds
    }

    @Override
    protected void doLoad() throws Exception {
        info("Loading ServerManagement module...");

        // Initialize ServerInfoDAO
        this.serverInfoDAO = new ServerInfoDAO(plugin);
        debug("ServerInfoDAO initialized");

        // Register message handlers for server management
        registerMessageHandlers();

        info("ServerManagement module loaded successfully");
    }

    @Override
    protected void doEnable() throws Exception {
        info("Enabling ServerManagement module...");

        // Register this server in the database
        registerServer();

        // Start heartbeat task
        startHeartbeat();

        // Send initial server status
        sendServerStatus("ONLINE");

        info("ServerManagement module enabled successfully");
    }

    @Override
    protected void doDisable() throws Exception {
        info("Disabling ServerManagement module...");

        // Stop heartbeat task
        stopHeartbeat();

        // Send offline status and mark as offline
        sendServerStatus("OFFLINE");
        markServerOffline();

        info("ServerManagement module disabled successfully");
    }

    @Override
    protected void doReload() throws Exception {
        info("Reloading ServerManagement module...");

        // Restart heartbeat with potentially new configuration
        stopHeartbeat();
        startHeartbeat();

        info("ServerManagement module reloaded successfully");
    }

    /**
     * Registriert Message-Handler für Server-Management.
     */
    private void registerMessageHandlers() {
        if (plugin.getMessageManager() != null) {
            plugin.getMessageManager().registerMessageHandler(MessageTypes.SYSTEM_HEARTBEAT, this::handleEnhancedHeartbeat);
            plugin.getMessageManager().registerMessageHandler(MessageTypes.SERVER_UPDATE, this::handleServerUpdate);
            plugin.getMessageManager().registerMessageHandler(MessageTypes.SERVER_REGISTER, this::handleServerRegister);
            plugin.getMessageManager().registerMessageHandler(MessageTypes.SERVER_LIST_REQUEST, this::handleServerListRequest);
            
            debug("Server management message handlers registered");
        }
    }

    /**
     * Startet die Heartbeat-Task.
     */
    private void startHeartbeat() {
        if (plugin.getProxyServer() != null) {
            heartbeatTask = plugin.getProxyServer().getScheduler()
                    .buildTask(plugin, this::sendHeartbeat)
                    .repeat(heartbeatInterval, TimeUnit.SECONDS)
                    .schedule();

            debug("Heartbeat task started with interval: {} seconds", heartbeatInterval);
        }
    }

    /**
     * Stoppt die Heartbeat-Task.
     */
    private void stopHeartbeat() {
        if (heartbeatTask != null) {
            heartbeatTask.cancel();
            heartbeatTask = null;
            debug("Heartbeat task stopped");
        }
    }

    /**
     * Sendet einen Heartbeat mit erweiterten Server-Informationen.
     */
    private void sendHeartbeat() {
        try {
            // Update database with current server status
            updateServerHeartbeat();

            // Send Redis broadcast for real-time updates
            JsonObject payload = new JsonObject();
            payload.addProperty("serverId", serverId);
            payload.addProperty("serverName", serverName);
            payload.addProperty("serverType", "VELOCITY");
            payload.addProperty("status", "ONLINE");
            payload.addProperty("timestamp", System.currentTimeMillis());

            if (plugin.getProxyServer() != null) {
                int playerCount = plugin.getProxyServer().getPlayerCount();
                int maxPlayers = plugin.getProxyServer().getConfiguration().getShowMaxPlayers();
                
                payload.addProperty("playerCount", playerCount);
                payload.addProperty("maxPlayers", maxPlayers);
                payload.addProperty("version", plugin.getProxyServer().getVersion().getVersion());
            }

            if (plugin.getMessageManager() != null) {
                plugin.getMessageManager().broadcastMessage(MessageTypes.SYSTEM_HEARTBEAT, MessageActions.HEARTBEAT, payload);
                debug("Enhanced heartbeat sent with server info");
            }

        } catch (Exception e) {
            error("Failed to send heartbeat", e);
        }
    }

    /**
     * Sendet Server-Status-Updates.
     */
    private void sendServerStatus(String status) {
        try {
            JsonObject payload = new JsonObject();
            payload.addProperty("serverId", serverId);
            payload.addProperty("serverName", serverName);
            payload.addProperty("serverType", "VELOCITY");
            payload.addProperty("status", status);
            payload.addProperty("timestamp", System.currentTimeMillis());

            if (plugin.getProxyServer() != null) {
                payload.addProperty("playerCount", plugin.getProxyServer().getPlayerCount());
                payload.addProperty("maxPlayers", plugin.getProxyServer().getConfiguration().getShowMaxPlayers());
                payload.addProperty("version", plugin.getProxyServer().getVersion().getVersion());
            }

            if (plugin.getMessageManager() != null) {
                plugin.getMessageManager().broadcastMessage(MessageTypes.SERVER_UPDATE, MessageActions.UPDATE_SERVER, payload);
                debug("Enhanced server status sent: {}", status);
            }

        } catch (Exception e) {
            error("Failed to send server status", e);
        }
    }

    /**
     * Registriert diesen Server in der Datenbank.
     */
    private void registerServer() {
        try {
            if (plugin.getProxyServer() != null) {
                ServerInfo serverInfo = new ServerInfo(
                    serverId,
                    serverName,
                    ServerInfo.ServerType.VELOCITY,
                    ServerInfo.ServerStatus.ONLINE,
                    plugin.getProxyServer().getPlayerCount(),
                    plugin.getProxyServer().getConfiguration().getShowMaxPlayers()
                );

                serverInfoDAO.upsertServerInfo(serverInfo).thenAccept(success -> {
                    if (success) {
                        debug("Server registered successfully in database");
                    } else {
                        warn("Failed to register server in database");
                    }
                }).exceptionally(throwable -> {
                    error("Error registering server", throwable);
                    return null;
                });
            }
        } catch (Exception e) {
            error("Failed to register server", e);
        }
    }

    /**
     * Aktualisiert den Server-Heartbeat in der Datenbank.
     */
    private void updateServerHeartbeat() {
        try {
            if (plugin.getProxyServer() != null) {
                int playerCount = plugin.getProxyServer().getPlayerCount();
                int maxPlayers = plugin.getProxyServer().getConfiguration().getShowMaxPlayers();

                serverInfoDAO.updateHeartbeat(serverId, ServerInfo.ServerStatus.ONLINE, playerCount, maxPlayers)
                    .thenAccept(success -> {
                        if (!success) {
                            debug("Failed to update heartbeat in database");
                        }
                    }).exceptionally(throwable -> {
                        error("Error updating heartbeat", throwable);
                        return null;
                    });
            }
        } catch (Exception e) {
            error("Failed to update heartbeat", e);
        }
    }

    /**
     * Markiert den Server als offline in der Datenbank.
     */
    private void markServerOffline() {
        try {
            serverInfoDAO.updateHeartbeat(serverId, ServerInfo.ServerStatus.OFFLINE, 0, 0)
                .thenAccept(success -> {
                    if (success) {
                        debug("Server marked as offline in database");
                    } else {
                        warn("Failed to mark server as offline in database");
                    }
                }).exceptionally(throwable -> {
                    error("Error marking server offline", throwable);
                    return null;
                });
        } catch (Exception e) {
            error("Failed to mark server offline", e);
        }
    }

    // Message Handlers

    /**
     * Behandelt erweiterte Heartbeat-Nachrichten von anderen Servern.
     */
    public void handleEnhancedHeartbeat(NetworkMessage message) {
        debug("Received enhanced heartbeat from server: {}", message.getSourceServer());
        
        try {
            String serverId = message.getString("serverId");
            String serverName = message.getString("serverName");
            String serverType = message.getString("serverType");
            String status = message.getString("status");
            Integer playerCount = message.getInt("playerCount");
            Integer maxPlayers = message.getInt("maxPlayers");
            
            if (serverId != null && serverName != null && serverType != null) {
                ServerInfo serverInfo = new ServerInfo(
                    serverId,
                    serverName,
                    ServerInfo.ServerType.valueOf(serverType.toUpperCase()),
                    status != null ? ServerInfo.ServerStatus.valueOf(status.toUpperCase()) : ServerInfo.ServerStatus.ONLINE,
                    playerCount != null ? playerCount : 0,
                    maxPlayers != null ? maxPlayers : 0
                );
                
                serverInfoDAO.upsertServerInfo(serverInfo).thenAccept(success -> {
                    if (success) {
                        debug("Updated enhanced server info for: {}", serverId);
                    } else {
                        warn("Failed to update enhanced server info for: {}", serverId);
                    }
                }).exceptionally(throwable -> {
                    error("Error updating enhanced server info", throwable);
                    return null;
                });
            }
        } catch (Exception e) {
            error("Error processing enhanced heartbeat message", e);
        }
    }

    /**
     * Behandelt Server-Update-Nachrichten.
     */
    public void handleServerUpdate(NetworkMessage message) {
        debug("Received server update from: {}", message.getSourceServer());
        handleEnhancedHeartbeat(message); // Same logic as enhanced heartbeat
    }

    /**
     * Behandelt Server-Registrierung-Nachrichten.
     */
    public void handleServerRegister(NetworkMessage message) {
        info("Server registration received from: {}", message.getSourceServer());
        handleEnhancedHeartbeat(message); // Same logic as enhanced heartbeat
    }

    /**
     * Behandelt Server-List-Anfragen.
     */
    public void handleServerListRequest(NetworkMessage message) {
        debug("Server list request from: {}", message.getSourceServer());
        
        serverInfoDAO.getAllServers().thenAccept(servers -> {
            JsonObject payload = new JsonObject();
            payload.addProperty("serverCount", servers.size());
            // Add server list data here
            
            if (plugin.getMessageManager() != null) {
                plugin.getMessageManager().sendMessage(
                    message.getSourceServer(),
                    MessageTypes.SERVER_LIST_RESPONSE,
                    MessageActions.RESPONSE,
                    payload
                );
            }
        }).exceptionally(throwable -> {
            error("Error handling server list request", throwable);
            return null;
        });
    }

    /**
     * Gibt Server-Statistiken zurück.
     */
    public JsonObject getServerStats() {
        JsonObject stats = new JsonObject();
        stats.addProperty("serverId", serverId);
        stats.addProperty("serverName", serverName);
        stats.addProperty("uptime", System.currentTimeMillis());

        if (plugin.getProxyServer() != null) {
            stats.addProperty("playerCount", plugin.getProxyServer().getPlayerCount());
            stats.addProperty("version", plugin.getProxyServer().getVersion().getVersion());
        }

        return stats;
    }
}
