package de.dasjeff.adventureSMPCore.commands;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.List;

public abstract class AbstractCommand implements CommandExecutor, TabCompleter {

    protected final AdventureSMPCore corePlugin;
    private final String commandName;
    private final String permission;
    private final boolean playerOnly;
    private final String description;
    private final String usageMessage;
    private final List<String> aliases;

    /**
     * Constructor for a new AbstractCommand.
     * @param corePlugin The main plugin instance.
     * @param commandName The name of the command.
     * @param permission The permission required to use this command (can be null if no permission is needed).
     * @param playerOnly If true, only players can execute this command.
     * @param description A short description of what the command does.
     * @param usageMessage How to use the command, e.g., /<command> <required> [optional].
     * @param aliases A list of aliases for this command (can be null or empty).
     */
    public AbstractCommand(@NotNull AdventureSMPCore corePlugin, @NotNull String commandName, @Nullable String permission, 
                           boolean playerOnly, @NotNull String description, @NotNull String usageMessage, @Nullable List<String> aliases) {
        this.corePlugin = corePlugin;
        this.commandName = commandName;
        this.permission = permission;
        this.playerOnly = playerOnly;
        this.description = description;
        this.usageMessage = usageMessage;
        this.aliases = aliases == null ? Collections.emptyList() : aliases;
    }

    /**
     * Executes the given command, returning its success.
     * @param sender Source of the command.
     * @param command Command which was executed.
     * @param label Alias of the command which was used.
     * @param args Passed command arguments.
     * @return true if a valid command, otherwise false.
     */
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String @NotNull [] args) {
        if (playerOnly && !(sender instanceof Player)) {
            // Potentially use a ChatUtil or MessageManager for localized messages
            sender.sendMessage("This command can only be executed by a player.");
            return true;
        }

        if (permission != null && !sender.hasPermission(permission)) {
            // Potentially use a ChatUtil or MessageManager for localized messages
            sender.sendMessage("You do not have permission to use this command.");
            return true;
        }

        return execute(sender, args);
    }

    /**
     * Requests a list of possible completions for a command argument.
     * @param sender Source of the command. Fo BungeeCord, this can be a ProxiedPlayer or ConsoleCommandSender.
     * @param command Command which was executed.
     * @param alias Alias of the command which was used.
     * @param args The arguments passed to the command, including final partial argument to be completed and command label.
     * @return A List of possible completions for the final argument, or null to default to normal player name completion.
     */
    @Override
    public @Nullable List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, @NotNull String alias, @NotNull String @NotNull [] args) {
        if (permission != null && !sender.hasPermission(permission)) {
            return Collections.emptyList();
        }
        return tabComplete(sender, args);
    }

    /**
     * The actual command logic to be implemented by subclasses.
     * @param sender The command sender.
     * @param args The command arguments.
     * @return true if the command was handled successfully, false otherwise (which might trigger a usage message).
     */
    public abstract boolean execute(CommandSender sender, String[] args);

    /**
     * The tab completion logic to be implemented by subclasses.
     * @param sender The command sender.
     * @param args The command arguments.
     * @return A list of tab completions.
     */
    public abstract List<String> tabComplete(CommandSender sender, String[] args);

    // Getters
    public String getCommandName() {
        return commandName;
    }

    public String getPermission() {
        return permission;
    }

    public boolean isPlayerOnly() {
        return playerOnly;
    }

    public String getDescription() {
        return description;
    }

    public String getUsageMessage() {
        return usageMessage;
    }

    public List<String> getAliases() {
        return aliases;
    }
} 