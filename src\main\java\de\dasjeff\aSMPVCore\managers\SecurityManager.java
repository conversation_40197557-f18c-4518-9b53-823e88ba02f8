package de.dasjeff.aSMPVCore.managers;

import com.google.gson.JsonObject;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.communication.NetworkMessage;
import org.jetbrains.annotations.NotNull;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * SecurityManager für das ASMP-VCore Plugin.
 * Verwaltet Token-basierte Authentifizierung, HMAC-Signierung und Input-Validierung.
 */
public class SecurityManager {

    private final ASMPVCore plugin;
    private final ScheduledExecutorService scheduler;
    
    // Configuration
    private String serverToken;
    private String hmacSecret;
    private final long tokenRotationHours;
    private final int maxMessageSize;
    
    // Security state
    private final SecureRandom secureRandom;
    private final ConcurrentMap<String, Long> messageIds = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, Integer> rateLimitMap = new ConcurrentHashMap<>();
    
    // Constants
    private static final String HMAC_ALGORITHM = "HmacSHA256";
    private static final long MESSAGE_ID_CLEANUP_INTERVAL = TimeUnit.HOURS.toMillis(1);
    private static final long MESSAGE_ID_MAX_AGE = TimeUnit.HOURS.toMillis(24);
    private static final int MAX_RATE_LIMIT_VIOLATIONS = 10;

    public SecurityManager(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
        
        JsonObject config = plugin.getConfigManager().getMainConfig();
        JsonObject securityConfig = config.getAsJsonObject("security");
        
        this.serverToken = securityConfig.get("serverToken").getAsString();
        this.hmacSecret = securityConfig.get("hmacSecret").getAsString();
        this.tokenRotationHours = securityConfig.get("tokenRotationHours").getAsLong();
        this.maxMessageSize = securityConfig.get("maxMessageSize").getAsInt();
        
        this.secureRandom = new SecureRandom();
        this.scheduler = Executors.newScheduledThreadPool(1, r -> {
            Thread thread = new Thread(r, "ASMP-VCore-Security");
            thread.setDaemon(true);
            return thread;
        });
        
        // Check for default security configuration
        checkSecurityConfiguration();
        
        // Start security tasks
        startSecurityTasks();
    }

    /**
     * Prüft die Sicherheitskonfiguration auf Standardwerte.
     */
    private void checkSecurityConfiguration() {
        if ("change-this-token".equals(serverToken) || "change-this-secret".equals(hmacSecret)) {
            plugin.getLogger().error("==================================================");
            plugin.getLogger().error("ASMP-VCore DETECTED DEFAULT SECURITY CONFIGURATION!");
            plugin.getLogger().error("Please change the serverToken and hmacSecret in config.json");
            plugin.getLogger().error("Using default security values is a serious security risk!");
            plugin.getLogger().error("==================================================");
        }
        
        if (serverToken.length() < 32 || hmacSecret.length() < 32) {
            plugin.getLogger().warn("Security tokens should be at least 32 characters long for optimal security.");
        }
    }

    /**
     * Startet Sicherheits-Tasks.
     */
    private void startSecurityTasks() {
        // Token rotation task
        if (tokenRotationHours > 0) {
            scheduler.scheduleAtFixedRate(this::rotateTokens, 
                    tokenRotationHours, tokenRotationHours, TimeUnit.HOURS);
        }
        
        // Message ID cleanup task
        scheduler.scheduleAtFixedRate(this::cleanupMessageIds, 
                MESSAGE_ID_CLEANUP_INTERVAL, MESSAGE_ID_CLEANUP_INTERVAL, TimeUnit.MILLISECONDS);
        
        // Rate limit cleanup task
        scheduler.scheduleAtFixedRate(this::cleanupRateLimits, 
                TimeUnit.MINUTES.toMillis(5), TimeUnit.MINUTES.toMillis(5), TimeUnit.MILLISECONDS);
    }

    /**
     * Signiert eine NetworkMessage mit HMAC.
     */
    public void signMessage(@NotNull NetworkMessage message) {
        try {
            String messageContent = createSignatureContent(message);
            String signature = generateHMAC(messageContent, hmacSecret);
            message.setSignature(signature);
            
        } catch (Exception e) {
            plugin.getLogger().error("Failed to sign message: {}", message.getMessageId(), e);
            throw new RuntimeException("Message signing failed", e);
        }
    }

    /**
     * Verifiziert die Signatur einer NetworkMessage.
     */
    public boolean verifyMessageSignature(@NotNull NetworkMessage message) {
        try {
            String signature = message.getSignature();
            if (signature == null || signature.isEmpty()) {
                plugin.getLogger().warn("Message {} has no signature", message.getMessageId());
                return false;
            }
            
            // Check message size
            if (message.serialize().length() > maxMessageSize) {
                plugin.getLogger().warn("Message {} exceeds maximum size limit", message.getMessageId());
                return false;
            }
            
            // Check for replay attacks
            if (isReplayAttack(message)) {
                plugin.getLogger().warn("Potential replay attack detected for message {}", message.getMessageId());
                return false;
            }
            
            // Verify HMAC signature
            String messageContent = createSignatureContent(message);
            String expectedSignature = generateHMAC(messageContent, hmacSecret);
            
            boolean valid = constantTimeEquals(signature, expectedSignature);
            
            if (valid) {
                // Store message ID to prevent replay attacks
                messageIds.put(message.getMessageId(), message.getTimestamp());
            } else {
                plugin.getLogger().warn("Invalid signature for message {} from server {}", 
                        message.getMessageId(), message.getSourceServer());
            }
            
            return valid;
            
        } catch (Exception e) {
            plugin.getLogger().error("Failed to verify message signature: {}", message.getMessageId(), e);
            return false;
        }
    }

    /**
     * Erstellt den Inhalt für die Signatur.
     */
    @NotNull
    private String createSignatureContent(@NotNull NetworkMessage message) {
        return String.format("%s|%s|%s|%s|%s|%d|%s",
                message.getMessageId(),
                message.getSourceServer(),
                message.getTargetServer(),
                message.getMessageType(),
                message.getAction(),
                message.getTimestamp(),
                message.getPayload().toString());
    }

    /**
     * Generiert einen HMAC-Hash.
     */
    @NotNull
    private String generateHMAC(@NotNull String data, @NotNull String secret) 
            throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance(HMAC_ALGORITHM);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), HMAC_ALGORITHM);
        mac.init(secretKeySpec);
        
        byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hash);
    }

    /**
     * Constant-time String-Vergleich zur Vermeidung von Timing-Angriffen.
     */
    private boolean constantTimeEquals(@NotNull String a, @NotNull String b) {
        if (a.length() != b.length()) {
            return false;
        }
        
        int result = 0;
        for (int i = 0; i < a.length(); i++) {
            result |= a.charAt(i) ^ b.charAt(i);
        }
        
        return result == 0;
    }

    /**
     * Prüft auf Replay-Angriffe.
     */
    private boolean isReplayAttack(@NotNull NetworkMessage message) {
        String messageId = message.getMessageId();
        long timestamp = message.getTimestamp();
        long currentTime = System.currentTimeMillis();
        
        // Check if message is too old
        if (currentTime - timestamp > MESSAGE_ID_MAX_AGE) {
            return true;
        }
        
        // Check if message ID was already seen
        return messageIds.containsKey(messageId);
    }

    /**
     * Validiert Input-Strings gegen häufige Injection-Patterns.
     */
    public boolean isValidInput(@NotNull String input) {
        if (input.length() > 1000) {
            return false;
        }
        
        // Check for common injection patterns
        String lower = input.toLowerCase();
        String[] blacklistedPatterns = {
            "drop table", "delete from", "insert into", "update set",
            "union select", "script>", "<iframe", "javascript:",
            "onload=", "onerror=", "onclick=", "eval(", "exec(",
            "../", "..\\", "file://", "data:", "vbscript:"
        };
        
        for (String pattern : blacklistedPatterns) {
            if (lower.contains(pattern)) {
                plugin.getLogger().warn("Potentially malicious input detected: {}", 
                        input.substring(0, Math.min(50, input.length())));
                return false;
            }
        }
        
        return true;
    }

    /**
     * Prüft Rate-Limiting für eine bestimmte Quelle.
     */
    public boolean checkRateLimit(@NotNull String source) {
        int violations = rateLimitMap.getOrDefault(source, 0);
        
        if (violations >= MAX_RATE_LIMIT_VIOLATIONS) {
            plugin.getLogger().warn("Rate limit exceeded for source: {}", source);
            return false;
        }
        
        return true;
    }

    /**
     * Registriert eine Rate-Limit-Verletzung.
     */
    public void recordRateLimitViolation(@NotNull String source) {
        rateLimitMap.merge(source, 1, Integer::sum);
    }

    /**
     * Generiert einen sicheren Token.
     */
    @NotNull
    public String generateSecureToken(int length) {
        byte[] tokenBytes = new byte[length];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }

    /**
     * Rotiert die Sicherheits-Tokens.
     */
    private void rotateTokens() {
        plugin.getLogger().info("Rotating security tokens...");
        
        // Generate new tokens
        String newServerToken = generateSecureToken(32);
        String newHmacSecret = generateSecureToken(32);
        
        // Update configuration
        JsonObject config = plugin.getConfigManager().getMainConfig();
        JsonObject securityConfig = config.getAsJsonObject("security");
        securityConfig.addProperty("serverToken", newServerToken);
        securityConfig.addProperty("hmacSecret", newHmacSecret);
        
        // Save configuration
        plugin.getConfigManager().saveConfig("config.json", config);
        
        // Update internal state
        this.serverToken = newServerToken;
        this.hmacSecret = newHmacSecret;
        
        plugin.getLogger().info("Security tokens rotated successfully.");
    }

    /**
     * Bereinigt alte Message-IDs.
     */
    private void cleanupMessageIds() {
        long cutoffTime = System.currentTimeMillis() - MESSAGE_ID_MAX_AGE;
        messageIds.entrySet().removeIf(entry -> entry.getValue() < cutoffTime);
    }

    /**
     * Bereinigt Rate-Limit-Einträge.
     */
    private void cleanupRateLimits() {
        rateLimitMap.clear(); // Simple cleanup - reset all rate limits
    }

    /**
     * Beendet den SecurityManager.
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        messageIds.clear();
        rateLimitMap.clear();
        plugin.getLogger().info("SecurityManager shut down.");
    }

    // Getters
    @NotNull
    public String getServerToken() {
        return serverToken;
    }

    @NotNull
    public String getHmacSecret() {
        return hmacSecret;
    }
}
