package de.dasjeff.aSMPVCore.managers;

import com.google.gson.JsonObject;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.communication.MessageHandler;
import de.dasjeff.aSMPVCore.communication.NetworkMessage;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import redis.clients.jedis.*;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * RedisManager für das ASMP-VCore Plugin.
 * Verwaltet Redis-Verbindungen und bietet Pub/Sub-Funktionalität für die Kommunikation zwischen Servern.
 */
public class RedisManager {

    private final ASMPVCore plugin;
    private JedisPool jedisPool;
    private JedisPubSub pubSubListener;
    private final ExecutorService pubSubExecutor;
    
    // Configuration
    private final String host;
    private final int port;
    private final String password;
    private final int database;
    private final int timeout;
    private final int poolSize;
    
    // Message handlers
    private final ConcurrentMap<String, MessageHandler> messageHandlers = new ConcurrentHashMap<>();
    
    // Channels
    public static final String CHANNEL_PUNISHMENT = "asmp:punishment";
    public static final String CHANNEL_PLAYER_LOOKUP = "asmp:player-lookup";
    public static final String CHANNEL_REPORTS = "asmp:reports";
    public static final String CHANNEL_SERVER_STATUS = "asmp:server-status";
    public static final String CHANNEL_GLOBAL_CHAT = "asmp:global-chat";
    public static final String CHANNEL_SYSTEM = "asmp:system";

    public RedisManager(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
        
        JsonObject config = plugin.getConfigManager().getMainConfig();
        JsonObject redisConfig = config.getAsJsonObject("redis");
        
        this.host = redisConfig.get("host").getAsString();
        this.port = redisConfig.get("port").getAsInt();
        this.password = redisConfig.has("password") && !redisConfig.get("password").getAsString().isEmpty() 
                ? redisConfig.get("password").getAsString() : null;
        this.database = redisConfig.get("database").getAsInt();
        this.timeout = redisConfig.get("timeout").getAsInt();
        this.poolSize = redisConfig.get("poolSize").getAsInt();
        
        this.pubSubExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread thread = new Thread(r, "ASMP-VCore-Redis-PubSub");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * Initialisiert die Redis-Verbindung.
     */
    public boolean initialize() {
        try {
            setupJedisPool();
            
            if (isConnected()) {
                setupPubSub();
                plugin.getLogger().info("RedisManager initialized successfully.");
                return true;
            } else {
                plugin.getLogger().error("Failed to establish Redis connection.");
                return false;
            }
            
        } catch (Exception e) {
            plugin.getLogger().error("Failed to initialize RedisManager!", e);
            return false;
        }
    }

    /**
     * Richtet den Jedis-Pool ein.
     */
    private void setupJedisPool() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(poolSize);
        poolConfig.setMaxIdle(poolSize / 2);
        poolConfig.setMinIdle(1);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setMinEvictableIdleTimeMillis(60000);
        poolConfig.setTimeBetweenEvictionRunsMillis(30000);
        poolConfig.setNumTestsPerEvictionRun(3);
        poolConfig.setBlockWhenExhausted(true);
        poolConfig.setMaxWaitMillis(timeout);
        
        if (password != null && !password.isEmpty()) {
            this.jedisPool = new JedisPool(poolConfig, host, port, timeout, password, database);
        } else {
            this.jedisPool = new JedisPool(poolConfig, host, port, timeout, null, database);
        }
        
        plugin.getLogger().info("Redis connection pool established to {}:{}/{}", host, port, database);
    }

    /**
     * Richtet das Pub/Sub-System ein.
     */
    private void setupPubSub() {
        this.pubSubListener = new JedisPubSub() {
            @Override
            public void onMessage(String channel, String message) {
                handleIncomingMessage(channel, message);
            }

            @Override
            public void onSubscribe(String channel, int subscribedChannels) {
                plugin.getLogger().debug("Subscribed to Redis channel: {} (total: {})", channel, subscribedChannels);
            }

            @Override
            public void onUnsubscribe(String channel, int subscribedChannels) {
                plugin.getLogger().debug("Unsubscribed from Redis channel: {} (remaining: {})", channel, subscribedChannels);
            }
        };
        
        // Subscribe to all core channels
        pubSubExecutor.submit(() -> {
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.subscribe(pubSubListener, 
                    CHANNEL_PUNISHMENT,
                    CHANNEL_PLAYER_LOOKUP,
                    CHANNEL_REPORTS,
                    CHANNEL_SERVER_STATUS,
                    CHANNEL_GLOBAL_CHAT,
                    CHANNEL_SYSTEM
                );
            } catch (Exception e) {
                plugin.getLogger().error("Redis Pub/Sub connection failed!", e);
            }
        });
    }

    /**
     * Prüft, ob eine Verbindung zu Redis besteht.
     */
    public boolean isConnected() {
        if (jedisPool == null) {
            return false;
        }
        
        try (Jedis jedis = jedisPool.getResource()) {
            return "PONG".equals(jedis.ping());
        } catch (Exception e) {
            plugin.getLogger().error("Redis connection test failed!", e);
            return false;
        }
    }

    /**
     * Veröffentlicht eine Nachricht in einem Kanal.
     */
    public boolean publishMessage(@NotNull String channel, @NotNull NetworkMessage message) {
        try (Jedis jedis = jedisPool.getResource()) {
            String serializedMessage = message.serialize();
            long result = jedis.publish(channel, serializedMessage);
            
            plugin.getLogger().debug("Published message to channel {}: {} subscribers received", channel, result);
            return result >= 0;
            
        } catch (Exception e) {
            plugin.getLogger().error("Failed to publish message to channel: {}", channel, e);
            return false;
        }
    }

    /**
     * Registriert einen Message-Handler für einen bestimmten Nachrichtentyp.
     */
    public void registerMessageHandler(@NotNull String messageType, @NotNull MessageHandler handler) {
        messageHandlers.put(messageType, handler);
        plugin.getLogger().debug("Registered message handler for type: {}", messageType);
    }

    /**
     * Entfernt einen Message-Handler.
     */
    public void unregisterMessageHandler(@NotNull String messageType) {
        messageHandlers.remove(messageType);
        plugin.getLogger().debug("Unregistered message handler for type: {}", messageType);
    }

    /**
     * Behandelt eingehende Nachrichten.
     */
    private void handleIncomingMessage(@NotNull String channel, @NotNull String message) {
        try {
            NetworkMessage networkMessage = NetworkMessage.deserialize(message);
            
            if (networkMessage == null) {
                plugin.getLogger().warn("Failed to deserialize message from channel: {}", channel);
                return;
            }
            
            // Verify message signature if security manager is available
            if (plugin.getSecurityManager() != null && 
                !plugin.getSecurityManager().verifyMessageSignature(networkMessage)) {
                plugin.getLogger().warn("Message signature verification failed for channel: {}", channel);
                return;
            }
            
            // Find and execute handler
            MessageHandler handler = messageHandlers.get(networkMessage.getMessageType());
            if (handler != null) {
                try {
                    handler.handleMessage(networkMessage);
                } catch (Exception e) {
                    plugin.getLogger().error("Error handling message of type: {}", networkMessage.getMessageType(), e);
                }
            } else {
                plugin.getLogger().debug("No handler registered for message type: {}", networkMessage.getMessageType());
            }
            
        } catch (Exception e) {
            plugin.getLogger().error("Error processing incoming message from channel: {}", channel, e);
        }
    }

    /**
     * Speichert einen Wert in Redis.
     */
    public boolean set(@NotNull String key, @NotNull String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            return "OK".equals(jedis.set(key, value));
        } catch (Exception e) {
            plugin.getLogger().error("Failed to set Redis key: {}", key, e);
            return false;
        }
    }

    /**
     * Speichert einen Wert in Redis mit Ablaufzeit.
     */
    public boolean setex(@NotNull String key, int seconds, @NotNull String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            return "OK".equals(jedis.setex(key, seconds, value));
        } catch (Exception e) {
            plugin.getLogger().error("Failed to setex Redis key: {}", key, e);
            return false;
        }
    }

    /**
     * Ruft einen Wert aus Redis ab.
     */
    @Nullable
    public String get(@NotNull String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        } catch (Exception e) {
            plugin.getLogger().error("Failed to get Redis key: {}", key, e);
            return null;
        }
    }

    /**
     * Löscht einen Schlüssel aus Redis.
     */
    public boolean del(@NotNull String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.del(key) > 0;
        } catch (Exception e) {
            plugin.getLogger().error("Failed to delete Redis key: {}", key, e);
            return false;
        }
    }

    /**
     * Prüft, ob ein Schlüssel in Redis existiert.
     */
    public boolean exists(@NotNull String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.exists(key);
        } catch (Exception e) {
            plugin.getLogger().error("Failed to check Redis key existence: {}", key, e);
            return false;
        }
    }

    /**
     * Führt eine Pipeline-Operation aus.
     */
    public void executePipeline(@NotNull PipelineOperation operation) {
        try (Jedis jedis = jedisPool.getResource()) {
            Pipeline pipeline = jedis.pipelined();
            operation.execute(pipeline);
            pipeline.sync();
        } catch (Exception e) {
            plugin.getLogger().error("Failed to execute Redis pipeline operation!", e);
        }
    }

    /**
     * Beendet den RedisManager.
     */
    public void shutdown() {
        if (pubSubListener != null && pubSubListener.isSubscribed()) {
            pubSubListener.unsubscribe();
        }
        
        if (pubSubExecutor != null && !pubSubExecutor.isShutdown()) {
            pubSubExecutor.shutdown();
        }
        
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            plugin.getLogger().info("Redis connection pool closed.");
        }
    }

    @FunctionalInterface
    public interface PipelineOperation {
        void execute(Pipeline pipeline);
    }
}
