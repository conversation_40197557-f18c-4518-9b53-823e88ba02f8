package de.dasjeff.aSMPVCore.shared.messages;

/**
 * Constants for message actions used in communication between VCore and Paper Core.
 * Provides specific action types for each message category.
 */
public final class MessageActions {
    
    // Generic Actions
    public static final String CREATE = "create";
    public static final String UPDATE = "update";
    public static final String DELETE = "delete";
    public static final String GET = "get";
    public static final String LIST = "list";
    public static final String SEARCH = "search";
    public static final String REQUEST = "request";
    public static final String RESPONSE = "response";
    public static final String NOTIFY = "notify";
    public static final String SYNC = "sync";
    
    // System Actions
    public static final String PING = "ping";
    public static final String PONG = "pong";
    public static final String REGISTER = "register";
    public static final String UNREGISTER = "unregister";
    public static final String STATUS = "status";
    public static final String SHUTDOWN = "shutdown";
    public static final String RESTART = "restart";
    public static final String MAINTENANCE_START = "maintenance-start";
    public static final String MAINTENANCE_END = "maintenance-end";
    
    // Player Actions
    public static final String JOIN = "join";
    public static final String QUIT = "quit";
    public static final String SWITCH_SERVER = "switch-server";
    public static final String UPDATE_DATA = "update-data";
    public static final String GET_DATA = "get-data";
    public static final String SEARCH_PLAYERS = "search-players";
    public static final String GET_ALIASES = "get-aliases";
    public static final String GET_HISTORY = "get-history";
    
    // Session Actions
    public static final String START_SESSION = "start-session";
    public static final String END_SESSION = "end-session";
    public static final String UPDATE_SESSION = "update-session";
    public static final String GET_ACTIVE_SESSIONS = "get-active-sessions";
    
    // Punishment Actions
    public static final String BAN = "ban";
    public static final String UNBAN = "unban";
    public static final String TEMP_BAN = "temp-ban";
    public static final String IP_BAN = "ip-ban";
    public static final String MUTE = "mute";
    public static final String UNMUTE = "unmute";
    public static final String TEMP_MUTE = "temp-mute";
    public static final String KICK = "kick";
    public static final String WARN = "warn";
    public static final String CHECK_PUNISHMENT = "check-punishment";
    public static final String GET_PUNISHMENT_HISTORY = "get-punishment-history";
    public static final String APPEAL = "appeal";
    public static final String APPROVE_APPEAL = "approve-appeal";
    public static final String DENY_APPEAL = "deny-appeal";
    
    // Report Actions
    public static final String CREATE_REPORT = "create-report";
    public static final String UPDATE_REPORT = "update-report";
    public static final String CLOSE_REPORT = "close-report";
    public static final String ASSIGN_REPORT = "assign-report";
    public static final String GET_REPORTS = "get-reports";
    public static final String GET_REPORT = "get-report";
    public static final String PRIORITIZE = "prioritize";
    public static final String ADD_NOTE = "add-note";
    
    // Chat Actions
    public static final String SEND_MESSAGE = "send-message";
    public static final String BROADCAST = "broadcast";
    public static final String PRIVATE_MESSAGE = "private-message";
    public static final String FILTER_CHECK = "filter-check";
    public static final String MUTE_CHAT = "mute-chat";
    public static final String UNMUTE_CHAT = "unmute-chat";
    
    // Cache Actions
    public static final String INVALIDATE = "invalidate";
    public static final String REFRESH = "refresh";
    public static final String WARM_UP = "warm-up";
    public static final String CLEAR = "clear";
    public static final String GET_STATS = "get-stats";
    
    // Server Management Actions
    public static final String REGISTER_SERVER = "register-server";
    public static final String UNREGISTER_SERVER = "unregister-server";
    public static final String UPDATE_SERVER = "update-server";
    public static final String GET_SERVERS = "get-servers";
    public static final String GET_SERVER = "get-server";
    public static final String HEARTBEAT = "heartbeat";
    public static final String SET_MAINTENANCE = "set-maintenance";
    public static final String REMOVE_MAINTENANCE = "remove-maintenance";
    
    // Error Actions
    public static final String ERROR = "error";
    public static final String INVALID = "invalid";
    public static final String TIMEOUT = "timeout";
    public static final String UNAUTHORIZED = "unauthorized";
    public static final String NOT_FOUND = "not-found";
    public static final String CONFLICT = "conflict";
    
    private MessageActions() {
        // Utility class - prevent instantiation
    }
    
    /**
     * Checks if an action is a read-only operation.
     */
    public static boolean isReadOnlyAction(String action) {
        return action != null && (
            action.equals(GET) ||
            action.equals(LIST) ||
            action.equals(SEARCH) ||
            action.equals(REQUEST) ||
            action.equals(STATUS) ||
            action.equals(PING) ||
            action.equals(GET_DATA) ||
            action.equals(GET_ALIASES) ||
            action.equals(GET_HISTORY) ||
            action.equals(GET_ACTIVE_SESSIONS) ||
            action.equals(CHECK_PUNISHMENT) ||
            action.equals(GET_PUNISHMENT_HISTORY) ||
            action.equals(GET_REPORTS) ||
            action.equals(GET_REPORT) ||
            action.equals(GET_SERVERS) ||
            action.equals(GET_SERVER) ||
            action.equals(GET_STATS)
        );
    }
    
    /**
     * Checks if an action is a write operation.
     */
    public static boolean isWriteAction(String action) {
        return action != null && (
            action.equals(CREATE) ||
            action.equals(UPDATE) ||
            action.equals(DELETE) ||
            action.equals(BAN) ||
            action.equals(UNBAN) ||
            action.equals(MUTE) ||
            action.equals(UNMUTE) ||
            action.equals(KICK) ||
            action.equals(WARN) ||
            action.equals(CREATE_REPORT) ||
            action.equals(UPDATE_REPORT) ||
            action.equals(CLOSE_REPORT) ||
            action.equals(REGISTER_SERVER) ||
            action.equals(UNREGISTER_SERVER)
        );
    }
    
    /**
     * Checks if an action requires admin permissions.
     */
    public static boolean requiresAdminPermission(String action) {
        return action != null && (
            action.equals(SHUTDOWN) ||
            action.equals(RESTART) ||
            action.equals(MAINTENANCE_START) ||
            action.equals(MAINTENANCE_END) ||
            action.equals(BAN) ||
            action.equals(UNBAN) ||
            action.equals(IP_BAN) ||
            action.equals(REGISTER_SERVER) ||
            action.equals(UNREGISTER_SERVER) ||
            action.equals(SET_MAINTENANCE) ||
            action.equals(REMOVE_MAINTENANCE)
        );
    }
    
    /**
     * Gets the response action for a request action.
     */
    public static String getResponseAction(String requestAction) {
        if (requestAction == null) {
            return RESPONSE;
        }
        
        return switch (requestAction) {
            case REQUEST -> RESPONSE;
            case PING -> PONG;
            case GET, LIST, SEARCH -> RESPONSE;
            case GET_DATA -> "data-response";
            case GET_SERVERS -> "servers-response";
            case GET_REPORTS -> "reports-response";
            case CHECK_PUNISHMENT -> "punishment-response";
            default -> RESPONSE;
        };
    }
}
