package de.dasjeff.aSMPVCore.managers;

import com.google.gson.JsonObject;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.communication.MessageHandler;
import de.dasjeff.aSMPVCore.communication.NetworkMessage;

import de.dasjeff.aSMPVCore.shared.messages.MessageTypes;
import org.jetbrains.annotations.NotNull;
import java.util.concurrent.*;

/**
 * MessageManager verwaltet die Nachrichtenkommunikation zwischen Servern.
 * Bietet Routing, Retry-Mechanismen und Handler-Registrierung.
 */
public class MessageManager {

    private final ASMPVCore plugin;
    private final ScheduledExecutorService scheduler;
    private final String serverId;

    // Configuration
    private final int retryAttempts;
    private final long retryDelay;
    private final long messageTimeout;

    // Message tracking
    private final ConcurrentMap<String, PendingMessage> pendingMessages = new ConcurrentHashMap<>();

    public MessageManager(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;

        JsonObject config = plugin.getConfigManager().getMainConfig();
        JsonObject serverConfig = config.getAsJsonObject("server");
        JsonObject messagingConfig = config.getAsJsonObject("messaging");

        this.serverId = serverConfig.get("serverId").getAsString();
        this.retryAttempts = messagingConfig.get("retryAttempts").getAsInt();
        this.retryDelay = messagingConfig.get("retryDelay").getAsLong();
        this.messageTimeout = messagingConfig.get("messageTimeout").getAsLong();

        this.scheduler = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "ASMP-VCore-MessageManager");
            thread.setDaemon(true);
            return thread;
        });

        // Start cleanup task for expired messages
        startCleanupTask();

        // Register core message handlers
        registerCoreHandlers();
    }

    /**
     * Sendet eine Nachricht an einen bestimmten Server.
     */
    public CompletableFuture<Boolean> sendMessage(@NotNull String targetServer,
                                                @NotNull String messageType,
                                                @NotNull String action,
                                                @NotNull JsonObject payload) {
        NetworkMessage message = NetworkMessage.createTargeted(serverId, targetServer, messageType, action);
        message.setPayload(payload);

        return sendMessage(message);
    }

    /**
     * Sendet eine Broadcast-Nachricht an alle Server.
     */
    public CompletableFuture<Boolean> broadcastMessage(@NotNull String messageType,
                                                     @NotNull String action,
                                                     @NotNull JsonObject payload) {
        NetworkMessage message = NetworkMessage.createBroadcast(serverId, messageType, action);
        message.setPayload(payload);

        return sendMessage(message);
    }

    /**
     * Sendet eine NetworkMessage.
     */
    public CompletableFuture<Boolean> sendMessage(@NotNull NetworkMessage message) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Sign message if security manager is available
                if (plugin.getSecurityManager() != null) {
                    plugin.getSecurityManager().signMessage(message);
                }

                // Determine channel based on message type
                String channel = getChannelForMessageType(message.getMessageType());

                // Send via Redis
                if (plugin.getRedisManager() != null) {
                    boolean success = plugin.getRedisManager().publishMessage(channel, message);

                    if (success) {
                        plugin.getLogger().debug("Message sent successfully: {}", message);
                        return true;
                    } else {
                        plugin.getLogger().warn("Failed to send message: {}", message);
                        return false;
                    }
                } else {
                    plugin.getLogger().error("RedisManager not available for sending message: {}", message);
                    return false;
                }

            } catch (Exception e) {
                plugin.getLogger().error("Error sending message: {}", message, e);
                return false;
            }
        }, scheduler);
    }

    /**
     * Sendet eine Nachricht mit Retry-Mechanismus.
     */
    public CompletableFuture<Boolean> sendMessageWithRetry(@NotNull NetworkMessage message) {
        PendingMessage pendingMessage = new PendingMessage(message, retryAttempts);
        pendingMessages.put(message.getMessageId(), pendingMessage);

        return sendMessageWithRetryInternal(pendingMessage);
    }

    /**
     * Interne Methode für Retry-Logik.
     */
    private CompletableFuture<Boolean> sendMessageWithRetryInternal(@NotNull PendingMessage pendingMessage) {
        return sendMessage(pendingMessage.message)
                .thenCompose(success -> {
                    if (success) {
                        pendingMessages.remove(pendingMessage.message.getMessageId());
                        return CompletableFuture.completedFuture(true);
                    } else if (pendingMessage.attemptsLeft > 0) {
                        pendingMessage.attemptsLeft--;
                        plugin.getLogger().debug("Retrying message {} in {}ms (attempts left: {})",
                                pendingMessage.message.getMessageId(), retryDelay, pendingMessage.attemptsLeft);

                        return CompletableFuture
                                .supplyAsync(() -> null, CompletableFuture.delayedExecutor(retryDelay, TimeUnit.MILLISECONDS, scheduler))
                                .thenCompose(v -> sendMessageWithRetryInternal(pendingMessage));
                    } else {
                        pendingMessages.remove(pendingMessage.message.getMessageId());
                        plugin.getLogger().warn("Failed to send message after all retry attempts: {}",
                                pendingMessage.message);
                        return CompletableFuture.completedFuture(false);
                    }
                });
    }

    /**
     * Registriert einen Message-Handler für einen bestimmten Nachrichtentyp.
     */
    public void registerMessageHandler(@NotNull String messageType, @NotNull MessageHandler handler) {
        if (plugin.getRedisManager() != null) {
            plugin.getRedisManager().registerMessageHandler(messageType, handler);
            plugin.getLogger().debug("Registered message handler for type: {}", messageType);
        }
    }

    /**
     * Entfernt einen Message-Handler.
     */
    public void unregisterMessageHandler(@NotNull String messageType) {
        if (plugin.getRedisManager() != null) {
            plugin.getRedisManager().unregisterMessageHandler(messageType);
            plugin.getLogger().debug("Unregistered message handler for type: {}", messageType);
        }
    }

    /**
     * Bestimmt den Redis-Kanal basierend auf dem Nachrichtentyp.
     */
    @NotNull
    private String getChannelForMessageType(@NotNull String messageType) {
        // Use new MessageTypes constants for better consistency
        return switch (messageType.toLowerCase()) {
            // Punishment messages
            case "punishment-ban", "punishment-unban", "punishment-mute", "punishment-unmute",
                 "punishment-kick", "punishment-warn", "punishment-check", "punishment-history",
                 "punishment", "ban", "mute", "kick", "warn" -> RedisManager.CHANNEL_PUNISHMENT;

            // Player lookup and data messages
            case "player-data-request", "player-data-response", "player-search-request", "player-search-response",
                 "lookup-player-by-name", "lookup-player-by-uuid", "lookup-player-by-ip", "lookup-aliases",
                 "player-lookup", "player-search", "player-info" -> RedisManager.CHANNEL_PLAYER_LOOKUP;

            // Report messages
            case "report-create", "report-update", "report-close", "report-assign", "report-list",
                 "report" -> RedisManager.CHANNEL_REPORTS;

            // Server status and heartbeat messages
            case "system-heartbeat", "server-register", "server-unregister", "server-update",
                 "server-list-request", "server-list-response",
                 "server-status", "heartbeat", "server-info" -> RedisManager.CHANNEL_SERVER_STATUS;

            // Global chat messages
            case "chat-message", "chat-broadcast", "chat-private",
                 "global-chat" -> RedisManager.CHANNEL_GLOBAL_CHAT;

            // System messages
            case "system-shutdown", "system-maintenance", "system-status-request", "system-status-response",
                 "cache-invalidate", "cache-update", "cache-sync-request",
                 "system", "shutdown", "maintenance" -> RedisManager.CHANNEL_SYSTEM;

            default -> RedisManager.CHANNEL_SYSTEM;
        };
    }

    /**
     * Registriert Core-Message-Handler.
     */
    private void registerCoreHandlers() {
        // Legacy server status handlers (for backward compatibility)
        registerMessageHandler("server-status", this::handleServerStatus);
        registerMessageHandler("heartbeat", this::handleHeartbeat);

        // System handlers
        registerMessageHandler("system", this::handleSystemMessage);
        registerMessageHandler(MessageTypes.SYSTEM_SHUTDOWN, this::handleSystemMessage);
        registerMessageHandler(MessageTypes.SYSTEM_MAINTENANCE, this::handleSystemMessage);

        plugin.getLogger().info("Core message handlers registered (server management delegated to ServerManagementModule).");
    }

    /**
     * Behandelt Server-Status-Nachrichten.
     */
    private void handleServerStatus(@NotNull NetworkMessage message) {
        plugin.getLogger().debug("Received server status message from {}: {}",
                message.getSourceServer(), message.getAction());

        // Update server registry in database if available
        if (plugin.getDatabaseManager() != null) {
            updateServerStatus(message);
        }
    }

    /**
     * Behandelt Heartbeat-Nachrichten.
     */
    private void handleHeartbeat(@NotNull NetworkMessage message) {
        plugin.getLogger().debug("Received heartbeat from server: {}", message.getSourceServer());

        // Update last heartbeat timestamp
        if (plugin.getDatabaseManager() != null) {
            updateServerHeartbeat(message);
        }
    }

    /**
     * Behandelt System-Nachrichten.
     */
    private void handleSystemMessage(@NotNull NetworkMessage message) {
        plugin.getLogger().info("Received system message from {}: {} - {}",
                message.getSourceServer(), message.getAction(), message.getString("message"));

        switch (message.getAction().toLowerCase()) {
            case "shutdown" -> handleShutdownMessage(message);
            case "maintenance" -> handleMaintenanceMessage(message);
            case "reload" -> handleReloadMessage(message);
        }
    }

    /**
     * Behandelt Shutdown-Nachrichten.
     */
    private void handleShutdownMessage(@NotNull NetworkMessage message) {
        String reason = message.getString("reason");
        plugin.getLogger().warn("Server {} is shutting down. Reason: {}",
                message.getSourceServer(), reason != null ? reason : "Unknown");
    }

    /**
     * Behandelt Maintenance-Nachrichten.
     */
    private void handleMaintenanceMessage(@NotNull NetworkMessage message) {
        Boolean enabled = message.getBoolean("enabled");
        plugin.getLogger().info("Server {} maintenance mode: {}",
                message.getSourceServer(), enabled != null && enabled ? "ENABLED" : "DISABLED");
    }

    /**
     * Behandelt Reload-Nachrichten.
     */
    private void handleReloadMessage(@NotNull NetworkMessage message) {
        plugin.getLogger().info("Server {} performed a reload", message.getSourceServer());
    }

    /**
     * Aktualisiert den Server-Status in der Datenbank (delegiert an ServerManagementModule).
     */
    private void updateServerStatus(@NotNull NetworkMessage message) {
        plugin.getLogger().debug("Server status update delegated to ServerManagementModule for: {}",
                message.getSourceServer());
    }

    /**
     * Aktualisiert den Server-Heartbeat in der Datenbank (delegiert an ServerManagementModule).
     */
    private void updateServerHeartbeat(@NotNull NetworkMessage message) {
        plugin.getLogger().debug("Server heartbeat update delegated to ServerManagementModule for: {}",
                message.getSourceServer());
    }



    /**
     * Startet die Cleanup-Task für abgelaufene Nachrichten.
     */
    private void startCleanupTask() {
        scheduler.scheduleAtFixedRate(() -> {
            long now = System.currentTimeMillis();
            pendingMessages.entrySet().removeIf(entry -> {
                PendingMessage pending = entry.getValue();
                return (now - pending.message.getTimestamp()) > messageTimeout;
            });
        }, messageTimeout, messageTimeout, TimeUnit.MILLISECONDS);
    }

    /**
     * Beendet den MessageManager.
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        pendingMessages.clear();
        plugin.getLogger().info("MessageManager shut down.");
    }

    /**
     * Hilfklasse für ausstehende Nachrichten mit Retry-Logik.
     */
    private static class PendingMessage {
        final NetworkMessage message;
        int attemptsLeft;

        PendingMessage(NetworkMessage message, int maxAttempts) {
            this.message = message;
            this.attemptsLeft = maxAttempts;
        }
    }
}
