plugins {
    id 'java'
    id 'eclipse'
    id 'org.jetbrains.gradle.plugin.idea-ext' version '1.1.8'
    id("xyz.jpenilla.run-velocity") version "2.3.1"
    id("com.gradleup.shadow") version "8.3.0"
}

group = 'de.dasjeff'
version = '1.0'

repositories {
    mavenCentral()
    maven {
        name = "papermc-repo"
        url = "https://repo.papermc.io/repository/maven-public/"
    }
    maven {
        name = "sonatype"
        url = "https://oss.sonatype.org/content/groups/public/"
    }
}

dependencies {
    compileOnly("com.velocitypowered:velocity-api:3.4.0-SNAPSHOT")
    annotationProcessor("com.velocitypowered:velocity-api:3.4.0-SNAPSHOT")

    // Database dependencies
    implementation('com.zaxxer:HikariCP:5.1.0')
    implementation('org.mariadb.jdbc:mariadb-java-client:3.3.4')

    // Redis dependencies
    implementation('redis.clients:jedis:5.1.0')

    // Caching
    implementation('com.github.ben-manes.caffeine:caffeine:3.1.8')

    // JSON serialization
    implementation('com.google.code.gson:gson:2.10.1')

    // Logging
    implementation('org.slf4j:slf4j-api:2.0.9')

    // Annotations
    compileOnly('org.jetbrains:annotations:24.1.0')
}

tasks {
    runVelocity {
        // Configure the Velocity version for our task.
        // This is the only required configuration besides applying the plugin.
        // Your plugin's jar (or shadowJar if present) will be used automatically.
        velocityVersion("3.4.0-SNAPSHOT")
    }
}

def targetJavaVersion = 21
java {
    toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
    options.release.set(targetJavaVersion)
}

def templateSource = file('src/main/templates')
def templateDest = layout.buildDirectory.dir('generated/sources/templates')
def generateTemplates = tasks.register('generateTemplates', Copy) { task ->
    def props = ['version': project.version]
    task.inputs.properties props

    task.from templateSource
    task.into templateDest
    task.expand props
}

sourceSets.main.java.srcDir(generateTemplates.map { it.outputs })

project.idea.project.settings.taskTriggers.afterSync generateTemplates
project.eclipse.synchronizationTasks(generateTemplates)

// Shadow configuration
shadowJar {
    // Relocate dependencies to avoid conflicts
    relocate 'com.zaxxer.hikari', 'de.dasjeff.aSMPVCore.libs.hikari'
    relocate 'org.mariadb.jdbc', 'de.dasjeff.aSMPVCore.libs.mariadb'
    relocate 'redis.clients.jedis', 'de.dasjeff.aSMPVCore.libs.jedis'
    relocate 'com.github.benmanes.caffeine', 'de.dasjeff.aSMPVCore.libs.caffeine'
    relocate 'com.google.gson', 'de.dasjeff.aSMPVCore.libs.gson'

    // Exclude unnecessary files
    exclude 'META-INF/DEPENDENCIES'
    exclude 'META-INF/LICENSE*'
    exclude 'META-INF/NOTICE*'
    exclude 'META-INF/maven/**'
    exclude 'META-INF/versions/**'

    archiveClassifier.set('')
}

// Make sure shadowJar is used for builds
build.dependsOn shadowJar
