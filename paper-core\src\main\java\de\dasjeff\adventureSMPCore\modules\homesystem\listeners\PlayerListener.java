package de.dasjeff.adventureSMPCore.modules.homesystem.listeners;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.managers.TeleportManager;
import de.dasjeff.adventureSMPCore.modules.homesystem.database.HomeDataAccessor;
import de.dasjeff.adventureSMPCore.modules.homesystem.service.HomeService;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.logging.Level;

public class PlayerListener implements Listener {

    private final AdventureSMPCore corePlugin;
    private final HomeDataAccessor dataAccessor;
    private final HomeService homeService;
    private final TeleportManager teleportManager;

    public PlayerListener(AdventureSMPCore corePlugin, HomeModule homeModule) {
        this.corePlugin = corePlugin;
        this.dataAccessor = homeModule.getDataAccessor();
        this.homeService = homeModule.getHomeService();
        this.teleportManager = homeModule.getTeleportManager();
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Update player data and preload cache asynchronously
        corePlugin.getServer().getScheduler().runTaskAsynchronously(corePlugin, () -> {
            long startTime = System.currentTimeMillis();
            try {
                // Update player data in database
                dataAccessor.updatePlayerData(player.getUniqueId(), player.getName());
                
                // Preload player's homes into cache
                homeService.getHomesAsync(player.getUniqueId()).thenAccept(homes -> {
                    long duration = System.currentTimeMillis() - startTime;
                    if (corePlugin.getConfigManager().getMainConfig().getBoolean("debug_mode", false)) {
                        corePlugin.getLogger().info("Player join processing for " + player.getName() + 
                            " completed in " + duration + "ms (loaded " + homes.size() + " homes)");
                    }
                });
                
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "Error processing player join for " + player.getName(), e);
            }
        });
    }

    @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
    public void onPlayerDamage(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof Player player)) {
            return;
        }

        try {
            if (teleportManager.isTeleporting(player)) {
                teleportManager.cancelTeleportForDamage(player);
            }
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error handling damage event for player " + player.getName(), e);
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        try {
            if (teleportManager.isTeleporting(player)) {
                teleportManager.cancelTeleportForQuit(player);
            }
            
            // Clean up player-specific resources
            homeService.cleanupPlayer(player.getUniqueId());
            
            // Clean up security manager data
            if (corePlugin.getSecurityManager() != null) {
                corePlugin.getSecurityManager().cleanup(player.getUniqueId());
            }
            
            if (corePlugin.getConfigManager().getMainConfig().getBoolean("debug_mode", false)) {
                corePlugin.getLogger().fine("Successfully processed quit for player: " + player.getName());
            }
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error processing player quit for " + player.getName(), e);
        }
    }
} 