plugins {
    id 'java'
    id("com.gradleup.shadow") version "9.0.0-beta13"
    id("xyz.jpenilla.run-paper") version "2.3.1"
}

group = 'de.dasjeff'
version = '1.0'

repositories {
    mavenCentral()
    maven {
        name = "papermc-repo"
        url = "https://repo.papermc.io/repository/maven-public/"
    }
    maven {
        name = "sonatype"
        url = "https://oss.sonatype.org/content/groups/public/"
    }
}

dependencies {
    compileOnly("io.papermc.paper:paper-api:1.21.4-R0.1-SNAPSHOT")

    // Database dependencies
    implementation('com.zaxxer:HikariCP:5.1.0')
    implementation('org.mariadb.jdbc:mariadb-java-client:3.3.3')

    // Redis dependencies
    implementation('redis.clients:jedis:5.1.0')

    // Caching
    implementation('com.github.ben-manes.caffeine:caffeine:3.1.8')

    // JSON serialization
    implementation('com.google.code.gson:gson:2.10.1')

    compileOnly('net.kyori:adventure-api:4.17.0')
    compileOnly('net.kyori:adventure-platform-bukkit:4.3.2')
    compileOnly('net.kyori:adventure-text-minimessage:4.17.0')
    compileOnly('net.kyori:adventure-text-serializer-legacy:4.17.0')
    compileOnly('org.jetbrains:annotations:24.1.0')
}

tasks {
    runServer {
        minecraftVersion("1.21.4")
    }
}

def targetJavaVersion = 21
java {
    def javaVersion = JavaVersion.toVersion(targetJavaVersion)
    sourceCompatibility = javaVersion
    targetCompatibility = javaVersion
    if (JavaVersion.current() < javaVersion) {
        toolchain {
            languageVersion = JavaLanguageVersion.of(targetJavaVersion)
        }
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'

    if (targetJavaVersion >= 10 || JavaVersion.current().isJava10Compatible()) {
        options.release.set(targetJavaVersion)
    }
}

processResources {
    def props = [version: version]
    inputs.properties props
    filteringCharset 'UTF-8'
    filesMatching('plugin.yml') {
        expand props
    }
}

shadowJar {
    archiveClassifier.set('')

    relocate 'com.zaxxer.hikari', 'de.dasjeff.adventureSMPCore.lib.hikaricp'
    relocate 'org.mariadb.jdbc', 'de.dasjeff.adventureSMPCore.lib.mariadb'
    relocate 'redis.clients.jedis', 'de.dasjeff.adventureSMPCore.lib.jedis'
    relocate 'com.github.benmanes.caffeine', 'de.dasjeff.adventureSMPCore.lib.caffeine'
    relocate 'com.google.gson', 'de.dasjeff.adventureSMPCore.lib.gson'

    exclude 'org/bukkit/**'
    exclude 'com/destroystokyo/paper/**'
    exclude 'io/papermc/paper/**'
    exclude 'META-INF/LICENSE'
    exclude 'META-INF/LICENSE.txt'
    exclude 'META-INF/NOTICE'
    exclude 'META-INF/NOTICE.txt'
    exclude 'META-INF/AL2.0'
    exclude 'META-INF/LGPL2.1'
    exclude 'META-INF/DEPENDENCIES'
}

tasks.build {
    dependsOn tasks.shadowJar
}

configurations {
    compileOnly.extendsFrom annotationProcessor
}
