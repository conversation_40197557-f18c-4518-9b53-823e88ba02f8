# ASMP-VCore - Phase 2 Fortsetzung

## 📍 Aktueller Stand

**Phase 2.1 - ABGESCHLOSSEN ✅**
- Shared Models & Database Migration implementiert
- Modulare Architektur refactored (CoreModule + ServerManagementModule)
- Heartbeat-System optimiert mit ServerInfoDAO
- Zentrale `players` und `player_sessions` Tabellen erstellt
- Message Framework mit MessageTypes und MessageActions

## 🎯 Nächste Schritte: Phase 2.2 - Paper Core Redis-Integration

### **Ziel:** Bidirektionale Kommunikation zwischen VCore und Paper Core etablieren

## 📋 Phase 2.2 - Paper Core Redis-Integration

### **2.2.1 Redis-Manager zu Paper Core hinzufügen**

**Aufgaben:**
1. **RedisManager-Klasse** in Paper Core erstellen
   - Basiert auf VCore RedisManager
   - Jedis-Pool-Konfiguration
   - Pub/Sub-Funktionalität
   - Gleiche Redis-Kanäle wie VCore

2. **Konfiguration erweitern**
   - Redis-Einstellungen in Paper Core config.yml
   - Gemeinsame Redis-Server-Konfiguration mit VCore
   - Connection-Pool-Settings

**Dateien zu erstellen:**
```
paper-core/src/main/java/de/dasjeff/papercore/managers/RedisManager.java
paper-core/src/main/resources/config.yml (erweitern)
```

### **2.2.2 MessageManager zu Paper Core hinzufügen**

**Aufgaben:**
1. **MessageManager-Klasse** erstellen
   - Ähnlich wie VCore MessageManager
   - NetworkMessage-Handling
   - Message-Handler-Registrierung
   - Redis-Integration

2. **Shared Models integrieren**
   - PlayerData, ServerInfo, PlayerSession
   - SerializationHelper verwenden
   - MessageTypes und MessageActions

**Dateien zu erstellen:**
```
paper-core/src/main/java/de/dasjeff/papercore/managers/MessageManager.java
paper-core/src/main/java/de/dasjeff/papercore/communication/ (Package)
```

### **2.2.3 VCoreIntegrationModule erstellen**

**Aufgaben:**
1. **Neues Modul** in Paper Core
   - Kommunikation mit VCore
   - Player-Event-Handling (Join/Quit/Server-Switch)
   - Enhanced Heartbeat mit Paper-spezifischen Daten

2. **Paper-spezifische Server-Metriken**
   - TPS-Tracking
   - Memory-Usage
   - World-Count
   - Plugin-Count

**Dateien zu erstellen:**
```
paper-core/src/main/java/de/dasjeff/papercore/modules/vcoreintegration/VCoreIntegrationModule.java
```

### **2.2.4 PaperIntegrationModule in VCore erstellen**

**Aufgaben:**
1. **Neues Modul** in VCore
   - Empfang von Paper-Server-Daten
   - Player-Data-Synchronisation
   - Paper-Server-Monitoring

2. **Message-Handler** für Paper-Events
   - Player-Join/Quit von Paper-Servern
   - Server-Performance-Updates
   - Player-Data-Requests

**Dateien zu erstellen:**
```
src/main/java/de/dasjeff/aSMPVCore/modules/paperintegration/PaperIntegrationModule.java
```

## 📋 Phase 2.3 - Bidirektionale Kommunikation

### **2.3.1 Player-Event-Synchronisation**

**Aufgaben:**
1. **Player-Join-Events**
   - Paper Core sendet Player-Join an VCore
   - VCore aktualisiert zentrale `players` Tabelle
   - Session-Tracking starten

2. **Player-Quit-Events**
   - Paper Core sendet Player-Quit an VCore
   - Session beenden und Spielzeit berechnen
   - Player-Data aktualisieren

3. **Server-Switch-Events**
   - Velocity sendet Server-Switch-Events
   - Paper Core aktualisiert current_server
   - Session-Tracking zwischen Servern

**Message Types zu implementieren:**
```java
MessageTypes.PLAYER_JOIN
MessageTypes.PLAYER_QUIT
MessageTypes.PLAYER_SERVER_SWITCH
MessageTypes.SESSION_START
MessageTypes.SESSION_END
```

### **2.3.2 Real-time Server-Monitoring**

**Aufgaben:**
1. **Enhanced Paper Heartbeat**
   - TPS, Memory, World-Count
   - Player-Count pro Server
   - Performance-Metriken

2. **Server-Status-Synchronisation**
   - Online/Offline-Status
   - Maintenance-Mode
   - Server-Restart-Detection

**Erweiterte ServerInfo-Felder:**
```java
// Für Paper-Server zusätzlich:
- Double tps
- Long memoryUsed
- Long memoryMax
- Integer worldCount
- Integer pluginCount
```

## 📋 Phase 2.4 - HomeModule Migration

### **2.4.1 Daten-Migration**

**Aufgaben:**
1. **Migration-Script** erstellen
   - `home_playerdata` → `players` Tabelle
   - Daten-Mapping und -Validierung
   - Backup-Mechanismus

2. **HomeModule anpassen**
   - PlayerDataDAO verwenden statt direkte SQL
   - Neue `players` Tabelle nutzen
   - Rückwärtskompatibilität gewährleisten

**Migration-SQL:**
```sql
INSERT INTO players (player_uuid, last_known_name, first_seen, last_seen, created_at, updated_at)
SELECT player_uuid, last_known_name, first_seen, last_seen, first_seen, last_seen
FROM home_playerdata
ON DUPLICATE KEY UPDATE
    last_known_name = VALUES(last_known_name),
    last_seen = VALUES(last_seen);
```

### **2.4.2 HomeModule Refactoring**

**Aufgaben:**
1. **PlayerDataDAO Integration**
   - Ersetze direkte SQL-Queries
   - Asynchrone Operationen
   - Shared Models verwenden

2. **Testing und Validation**
   - Alle Home-Funktionen testen
   - Performance-Vergleich
   - Daten-Integrität prüfen

## 🔧 Technische Implementierung

### **Redis-Konfiguration (Gemeinsam):**
```yaml
redis:
  host: "your-redis-server"
  port: 6379
  password: "your-password"
  database: 0
  pool:
    maxTotal: 20
    maxIdle: 10
    minIdle: 2
```

### **Message-Flow-Beispiel:**
```
Paper Server (Player Join)
    ↓ MessageTypes.PLAYER_JOIN
Redis (asmp:player-sync)
    ↓ VCore PaperIntegrationModule
    ↓ PlayerDataDAO.upsertPlayerData()
    ↓ SessionDAO.startSession()
Database (players + player_sessions)
```

### **Enhanced Heartbeat-Flow:**
```
Paper Server (alle 30s)
    ↓ TPS, Memory, Player-Count
    ↓ MessageTypes.SYSTEM_HEARTBEAT
Redis (asmp:server-status)
    ↓ VCore ServerManagementModule
    ↓ ServerInfoDAO.upsertServerInfo()
Database (vcore_servers mit erweiterten Daten)
```

## 📊 Erwartete Ergebnisse nach Phase 2

### **Funktionalitäten:**
- ✅ **Bidirektionale Kommunikation** zwischen VCore und Paper Core
- ✅ **Real-time Player-Tracking** über alle Server
- ✅ **Enhanced Server-Monitoring** mit Performance-Metriken
- ✅ **Zentrale Player-Datenbank** für alle Module
- ✅ **Session-Tracking** für Analytics

### **Datenbank-Schema:**
- ✅ **`players`** - Zentrale Player-Daten
- ✅ **`player_sessions`** - Session-Tracking
- ✅ **`vcore_servers`** - Server-Registry mit Metriken

### **Redis-Kommunikation:**
- ✅ **asmp:server-status** - Server-Heartbeat und -Updates
- ✅ **asmp:player-sync** - Player-Join/Quit/Switch-Events
- ✅ **asmp:system** - System-weite Nachrichten

## 🚀 Vorbereitung für Phase 3

Nach Abschluss von Phase 2 ist das System bereit für:
- **PunishmentModule** - Ban/Mute/Kick-System
- **PlayerLookupModule** - Player-Suche und -Analytics
- **ReportModule** - Report-System
- **Web-Dashboard** - Real-time Monitoring

## 📝 Wichtige Hinweise

### **Kompatibilität:**
- Alle Kommentare im Code auf Englisch
- Rückwärtskompatibilität mit bestehenden Systemen
- Graceful Degradation bei Redis-Ausfällen

### **Performance:**
- Asynchrone Operationen für alle DB-Zugriffe
- Connection-Pooling für Redis und Database
- Caching für häufig abgerufene Daten

### **Security:**
- HMAC-Signierung für alle Messages
- Input-Validation für alle Daten
- Rate-Limiting für Message-Handling

## ✅ Checkliste für Phase 2 Abschluss

### **Phase 2.2 - Paper Core Redis-Integration:**
- [ ] RedisManager zu Paper Core hinzufügen
- [ ] MessageManager zu Paper Core hinzufügen
- [ ] VCoreIntegrationModule (Paper Core) erstellen
- [ ] PaperIntegrationModule (VCore) erstellen

### **Phase 2.3 - Bidirektionale Kommunikation:**
- [ ] Player-Join/Quit-Event-Synchronisation
- [ ] Server-Switch-Event-Handling
- [ ] Enhanced Paper Heartbeat mit TPS/Memory
- [ ] Real-time Server-Monitoring

### **Phase 2.4 - HomeModule Migration:**
- [ ] Daten-Migration Script (`home_playerdata` → `players`)
- [ ] HomeModule auf PlayerDataDAO umstellen
- [ ] Testing und Validation
- [ ] Performance-Optimierung

### **Abschluss:**
- [ ] Integration-Tests aller Komponenten
- [ ] Dokumentation aktualisieren
- [ ] Performance-Benchmarks
- [ ] Vorbereitung für Phase 3

**Status: Bereit für Phase 2.2 - Paper Core Redis-Integration**

## 🔗 Relevante Dateien für Fortsetzung

### **Bereits implementiert (Phase 2.1):**
- `src/main/java/de/dasjeff/aSMPVCore/shared/models/` - Alle Shared Models
- `src/main/java/de/dasjeff/aSMPVCore/shared/dao/` - PlayerDataDAO, ServerInfoDAO
- `src/main/java/de/dasjeff/aSMPVCore/shared/serialization/` - SerializationHelper
- `src/main/java/de/dasjeff/aSMPVCore/shared/messages/` - MessageTypes, MessageActions
- `src/main/java/de/dasjeff/aSMPVCore/modules/servermanagement/` - ServerManagementModule
- Database Migrations für `players` und `player_sessions` Tabellen

### **Paper Core Struktur (zu erweitern):**
- `paper-core/src/main/java/de/dasjeff/papercore/` - Hauptverzeichnis
- `paper-core/src/main/java/de/dasjeff/papercore/modules/home/<USER>
- `paper-core/src/main/resources/config.yml` - Konfiguration

### **Nächste Implementierung (Phase 2.2):**
- `paper-core/src/main/java/de/dasjeff/papercore/managers/RedisManager.java`
- `paper-core/src/main/java/de/dasjeff/papercore/managers/MessageManager.java`
- `paper-core/src/main/java/de/dasjeff/papercore/modules/vcoreintegration/`
- `src/main/java/de/dasjeff/aSMPVCore/modules/paperintegration/`
