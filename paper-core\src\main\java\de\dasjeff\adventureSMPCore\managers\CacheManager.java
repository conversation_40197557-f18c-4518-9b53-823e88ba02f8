package de.dasjeff.adventureSMPCore.managers;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.logging.Level;

public class CacheManager {

    private final AdventureSMPCore corePlugin;
    private final ConcurrentMap<String, Cache<Object, Object>> caches = new ConcurrentHashMap<>();
    private final boolean enableStatistics;

    public CacheManager(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
        this.enableStatistics = corePlugin.getConfigManager().getMainConfig().getBoolean("cache.enable_statistics", false);
    }

    /**
     * Creates a new Caffeine cache with the specified parameters and stores it.
     */
    @SuppressWarnings("unchecked")
    public <K, V> Cache<K, V> createCache(@NotNull String cacheName, long maximumSize, long expireAfterAccess, @NotNull TimeUnit timeUnit) {
        if (caches.containsKey(cacheName)) {
            throw new IllegalArgumentException("Cache with name '" + cacheName + "' already exists.");
        }

        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(expireAfterAccess, timeUnit);
        
        if (enableStatistics) {
            builder.recordStats();
        }
        
        Cache<Object, Object> newCache = builder.build();
        caches.put(cacheName, newCache);
        return (Cache<K, V>) newCache;
    }

    /**
     * Creates a loading cache with automatic value computation.
     */
    @SuppressWarnings("unchecked")
    public <K, V> LoadingCache<K, V> createLoadingCache(@NotNull String cacheName, long maximumSize, 
                                                       long expireAfterAccess, @NotNull TimeUnit timeUnit,
                                                       @NotNull CacheLoader<K, V> loader) {
        if (caches.containsKey(cacheName)) {
            throw new IllegalArgumentException("Cache with name '" + cacheName + "' already exists.");
        }

        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(expireAfterAccess, timeUnit);
        
        if (enableStatistics) {
            builder.recordStats();
        }
        
        LoadingCache<K, V> newCache = (LoadingCache<K, V>) builder.build((CacheLoader<Object, Object>) loader);
        caches.put(cacheName, (Cache<Object, Object>) newCache);
        return newCache;
    }

    /**
     * Retrieves an existing cache by its name.
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public <K, V> Cache<K, V> getCache(@NotNull String cacheName) {
        return (Cache<K, V>) caches.get(cacheName);
    }

    /**
     * Safely retrieves a value from the specified cache, computing it if absent.
     */
    @Nullable
    public <K, V> V get(@NotNull String cacheName, @NotNull K key, @NotNull Function<? super K, ? extends V> mappingFunction) {
        try {
            Cache<K, V> cache = getCache(cacheName);
            if (cache == null) {
                corePlugin.getLogger().warning("Attempted to access non-existent cache: " + cacheName);
                return mappingFunction.apply(key); // Fallback to direct computation
            }
            return cache.get(key, mappingFunction);
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error accessing cache '" + cacheName + "' for key: " + key, e);
            return mappingFunction.apply(key); // Fallback to direct computation
        }
    }

    /**
     * Safely puts a value into the specified cache.
     */
    public <K, V> void put(@NotNull String cacheName, @NotNull K key, @NotNull V value) {
        try {
            Cache<K, V> cache = getCache(cacheName);
            if (cache == null) {
                corePlugin.getLogger().warning("Attempted to put into non-existent cache: " + cacheName);
                return;
            }
            cache.put(key, value);
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error putting value into cache '" + cacheName + "' for key: " + key, e);
        }
    }

    /**
     * Safely invalidates a key from the specified cache.
     */
    public <K> void invalidate(@NotNull String cacheName, @NotNull K key) {
        try {
            Cache<K, ?> cache = getCache(cacheName);
            if (cache == null) {
                corePlugin.getLogger().warning("Attempted to invalidate from non-existent cache: " + cacheName);
                return;
            }
            cache.invalidate(key);
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error invalidating cache '" + cacheName + "' for key: " + key, e);
        }
    }

    /**
     * Safely invalidates all entries in the specified cache.
     */
    public void invalidateAll(@NotNull String cacheName) {
        try {
            Cache<?, ?> cache = getCache(cacheName);
            if (cache == null) {
                corePlugin.getLogger().warning("Attempted to invalidate non-existent cache: " + cacheName);
                return;
            }
            cache.invalidateAll();
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error invalidating all entries in cache: " + cacheName, e);
        }
    }

    /**
     * Gets cache statistics if enabled.
     */
    @Nullable
    public String getCacheStats(@NotNull String cacheName) {
        if (!enableStatistics) {
            return "Statistics are disabled. Enable with cache.enable_statistics=true in config.yml";
        }
        
        Cache<?, ?> cache = getCache(cacheName);
        if (cache == null) {
            return "Cache '" + cacheName + "' does not exist.";
        }
        
        return cache.stats().toString();
    }

    /**
     * Checks if a cache exists.
     */
    public boolean cacheExists(@NotNull String cacheName) {
        return caches.containsKey(cacheName);
    }

    /**
     * Gets the size of a cache.
     */
    public long getCacheSize(@NotNull String cacheName) {
        Cache<?, ?> cache = getCache(cacheName);
        return cache != null ? cache.estimatedSize() : 0;
    }

    /**
     * Cleans up and removes all caches. Should be called on plugin disable.
     */
    public void shutdown() {
        try {
            caches.values().forEach(cache -> {
                try {
                    cache.invalidateAll();
                    cache.cleanUp();
                } catch (Exception e) {
                    corePlugin.getLogger().log(Level.WARNING, "Error cleaning up cache during shutdown", e);
                }
            });
            caches.clear();
            corePlugin.getLogger().info("All caches have been invalidated and cleared.");
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.SEVERE, "Error during cache shutdown", e);
        }
    }
} 