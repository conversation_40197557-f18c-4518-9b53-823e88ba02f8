package de.dasjeff.aSMPVCore.modules;

import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;

/**
 * Abstrakte Basis-Klasse für Module im ASMP-VCore Plugin.
 * Bietet grundlegende Implementierungen für den Modul-Lebenszyklus.
 */
public abstract class AbstractModule implements IModule {

    protected ASMPVCore plugin;
    private boolean loaded = false;
    private boolean enabled = false;
    private final String name;
    private final String version;
    private final String description;
    private final String[] dependencies;

    /**
     * Konstruktor für ein AbstractModule.
     * @param name Der Name des Moduls.
     * @param version Die Version des Moduls.
     * @param description Die Beschreibung des Moduls.
     * @param dependencies Die Abhängigkeiten des Moduls.
     */
    protected AbstractModule(@NotNull String name, @NotNull String version, 
                           @NotNull String description, @NotNull String... dependencies) {
        this.name = name;
        this.version = version;
        this.description = description;
        this.dependencies = dependencies != null ? dependencies : new String[0];
    }

    /**
     * Konstruktor für ein AbstractModule mit Standardwerten.
     * @param name Der Name des Moduls.
     */
    protected AbstractModule(@NotNull String name) {
        this(name, "1.0.0", "No description provided");
    }

    @Override
    @NotNull
    public final String getName() {
        return name;
    }

    @Override
    @NotNull
    public final String getVersion() {
        return version;
    }

    @Override
    @NotNull
    public final String getDescription() {
        return description;
    }

    @Override
    @NotNull
    public final String[] getDependencies() {
        return dependencies.clone();
    }

    @Override
    public final void onLoad(@NotNull ASMPVCore plugin) {
        if (loaded) {
            plugin.getLogger().warn("Module {} is already loaded", name);
            return;
        }

        this.plugin = plugin;
        
        try {
            plugin.getLogger().info("Loading module: {} v{}", name, version);
            doLoad();
            loaded = true;
            plugin.getLogger().info("Module loaded successfully: {}", name);
        } catch (Exception e) {
            plugin.getLogger().error("Failed to load module: {}", name, e);
            throw new RuntimeException("Module load failed: " + name, e);
        }
    }

    @Override
    public final void onEnable(@NotNull ASMPVCore plugin) {
        if (!loaded) {
            throw new IllegalStateException("Cannot enable module " + name + " - not loaded");
        }
        
        if (enabled) {
            plugin.getLogger().warn("Module {} is already enabled", name);
            return;
        }

        try {
            plugin.getLogger().info("Enabling module: {}", name);
            doEnable();
            enabled = true;
            plugin.getLogger().info("Module enabled successfully: {}", name);
        } catch (Exception e) {
            plugin.getLogger().error("Failed to enable module: {}", name, e);
            throw new RuntimeException("Module enable failed: " + name, e);
        }
    }

    @Override
    public final void onDisable() {
        if (!enabled) {
            return;
        }

        try {
            plugin.getLogger().info("Disabling module: {}", name);
            doDisable();
            enabled = false;
            plugin.getLogger().info("Module disabled successfully: {}", name);
        } catch (Exception e) {
            plugin.getLogger().error("Failed to disable module: {}", name, e);
        }
    }

    @Override
    public final void onReload(@NotNull ASMPVCore plugin) {
        plugin.getLogger().info("Reloading module: {}", name);
        
        if (enabled) {
            onDisable();
        }
        
        try {
            doReload();
            
            if (loaded) {
                onEnable(plugin);
            }
            
            plugin.getLogger().info("Module reloaded successfully: {}", name);
        } catch (Exception e) {
            plugin.getLogger().error("Failed to reload module: {}", name, e);
        }
    }

    @Override
    public final boolean isEnabled() {
        return enabled;
    }

    @Override
    public final boolean isLoaded() {
        return loaded;
    }

    /**
     * Wird während der Load-Phase aufgerufen.
     * Hier sollten grundlegende Initialisierungen durchgeführt werden.
     */
    protected abstract void doLoad() throws Exception;

    /**
     * Wird während der Enable-Phase aufgerufen.
     * Hier sollten Event-Handler registriert und Services gestartet werden.
     */
    protected abstract void doEnable() throws Exception;

    /**
     * Wird während der Disable-Phase aufgerufen.
     * Hier sollten Ressourcen freigegeben und Services gestoppt werden.
     */
    protected abstract void doDisable() throws Exception;

    /**
     * Wird während der Reload-Phase aufgerufen.
     * Standardimplementierung macht nichts - kann überschrieben werden.
     */
    protected void doReload() throws Exception {
        // Default implementation does nothing
    }

    /**
     * Hilfsmethode zum Loggen von Debug-Nachrichten.
     */
    protected final void debug(@NotNull String message, Object... args) {
        if (plugin != null) {
            plugin.getLogger().debug("[{}] {}", name, String.format(message, args));
        }
    }

    /**
     * Hilfsmethode zum Loggen von Info-Nachrichten.
     */
    protected final void info(@NotNull String message, Object... args) {
        if (plugin != null) {
            plugin.getLogger().info("[{}] {}", name, String.format(message, args));
        }
    }

    /**
     * Hilfsmethode zum Loggen von Warn-Nachrichten.
     */
    protected final void warn(Object... args) {
        if (plugin != null) {
            plugin.getLogger().warn("[{}] {}", name, String.format("Shutdown message received from {}: {}", args));
        }
    }

    /**
     * Hilfsmethode zum Loggen von Error-Nachrichten.
     */
    protected final void error(@NotNull String message, Throwable throwable) {
        if (plugin != null) {
            plugin.getLogger().error("[{}] {}", name, message, throwable);
        }
    }

    /**
     * Hilfsmethode zum Loggen von Error-Nachrichten.
     */
    protected final void error(@NotNull String message, Object... args) {
        if (plugin != null) {
            plugin.getLogger().error("[{}] {}", name, String.format(message, args));
        }
    }

    @Override
    public String toString() {
        return String.format("%s{name='%s', version='%s', loaded=%s, enabled=%s}", 
                getClass().getSimpleName(), name, version, loaded, enabled);
    }
}
