package de.dasjeff.adventureSMPCore.communication;

import com.google.gson.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.UUID;

/**
 * NetworkMessage represents a message exchanged between Paper Core and VCore via Redis.
 * Contains header information, payload data, and security metadata.
 * 
 * This is a simplified version of the VCore NetworkMessage for Paper Core usage.
 */
public class NetworkMessage {

    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();

    // Header information
    private String messageId;
    private String sourceServer;
    private String targetServer; // "*" for broadcast
    private String messageType;
    private String action;
    private long timestamp;
    
    // Payload
    private JsonObject payload;
    
    // Security
    private String signature;

    /**
     * Constructor for a new NetworkMessage.
     */
    public NetworkMessage(@NotNull String sourceServer, @NotNull String targetServer, 
                         @NotNull String messageType, @NotNull String action) {
        this.messageId = UUID.randomUUID().toString();
        this.sourceServer = sourceServer;
        this.targetServer = targetServer;
        this.messageType = messageType;
        this.action = action;
        this.timestamp = Instant.now().toEpochMilli();
        this.payload = new JsonObject();
    }

    /**
     * Creates a targeted message to a specific server.
     */
    public static NetworkMessage createTargeted(@NotNull String sourceServer, @NotNull String targetServer,
                                              @NotNull String messageType, @NotNull String action) {
        return new NetworkMessage(sourceServer, targetServer, messageType, action);
    }

    /**
     * Creates a broadcast message to all servers.
     */
    public static NetworkMessage createBroadcast(@NotNull String sourceServer,
                                               @NotNull String messageType, @NotNull String action) {
        return new NetworkMessage(sourceServer, "*", messageType, action);
    }

    // Getters
    public String getMessageId() { return messageId; }
    public String getSourceServer() { return sourceServer; }
    public String getTargetServer() { return targetServer; }
    public String getMessageType() { return messageType; }
    public String getAction() { return action; }
    public long getTimestamp() { return timestamp; }
    public JsonObject getPayload() { return payload; }
    public String getSignature() { return signature; }

    // Setters
    public void setPayload(@NotNull JsonObject payload) { this.payload = payload; }
    public void setSignature(@Nullable String signature) { this.signature = signature; }

    /**
     * Adds a property to the payload.
     */
    public void addProperty(@NotNull String key, @Nullable String value) {
        if (value != null) {
            payload.addProperty(key, value);
        }
    }

    public void addProperty(@NotNull String key, @Nullable Number value) {
        if (value != null) {
            payload.addProperty(key, value);
        }
    }

    public void addProperty(@NotNull String key, @Nullable Boolean value) {
        if (value != null) {
            payload.addProperty(key, value);
        }
    }

    /**
     * Gets a string value from the payload.
     */
    @Nullable
    public String getString(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && !element.isJsonNull() ? element.getAsString() : null;
    }

    /**
     * Gets an integer value from the payload.
     */
    @Nullable
    public Integer getInt(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && !element.isJsonNull() ? element.getAsInt() : null;
    }

    /**
     * Gets a long value from the payload.
     */
    @Nullable
    public Long getLong(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && !element.isJsonNull() ? element.getAsLong() : null;
    }

    /**
     * Gets a double value from the payload.
     */
    @Nullable
    public Double getDouble(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && !element.isJsonNull() ? element.getAsDouble() : null;
    }

    /**
     * Gets a boolean value from the payload.
     */
    @Nullable
    public Boolean getBoolean(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && !element.isJsonNull() ? element.getAsBoolean() : null;
    }

    /**
     * Checks if the message is a broadcast message.
     */
    public boolean isBroadcast() {
        return "*".equals(targetServer);
    }

    /**
     * Checks if the message is targeted to a specific server.
     */
    public boolean isTargetedTo(@NotNull String serverId) {
        return serverId.equals(targetServer) || isBroadcast();
    }

    /**
     * Serializes the message to JSON string.
     */
    public String serialize() {
        JsonObject json = new JsonObject();
        json.addProperty("messageId", messageId);
        json.addProperty("sourceServer", sourceServer);
        json.addProperty("targetServer", targetServer);
        json.addProperty("messageType", messageType);
        json.addProperty("action", action);
        json.addProperty("timestamp", timestamp);
        json.add("payload", payload);
        
        if (signature != null) {
            json.addProperty("signature", signature);
        }
        
        return GSON.toJson(json);
    }

    /**
     * Deserializes a JSON string to NetworkMessage.
     */
    public static NetworkMessage deserialize(@NotNull String json) {
        try {
            JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
            
            NetworkMessage message = new NetworkMessage(
                jsonObject.get("sourceServer").getAsString(),
                jsonObject.get("targetServer").getAsString(),
                jsonObject.get("messageType").getAsString(),
                jsonObject.get("action").getAsString()
            );
            
            message.messageId = jsonObject.get("messageId").getAsString();
            message.timestamp = jsonObject.get("timestamp").getAsLong();
            
            if (jsonObject.has("payload") && !jsonObject.get("payload").isJsonNull()) {
                message.payload = jsonObject.getAsJsonObject("payload");
            }
            
            if (jsonObject.has("signature") && !jsonObject.get("signature").isJsonNull()) {
                message.signature = jsonObject.get("signature").getAsString();
            }
            
            return message;
            
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to deserialize NetworkMessage: " + e.getMessage(), e);
        }
    }

    @Override
    public String toString() {
        return String.format("NetworkMessage{id=%s, source=%s, target=%s, type=%s, action=%s}", 
                messageId, sourceServer, targetServer, messageType, action);
    }
}
