package de.dasjeff.adventureSMPCore.modules.homesystem.service;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.database.HomeDataAccessor;
import de.dasjeff.adventureSMPCore.modules.homesystem.model.Home;
import de.dasjeff.adventureSMPCore.modules.homesystem.validation.HomeValidator;
import de.dasjeff.adventureSMPCore.modules.homesystem.validation.ValidationResult;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Consumer;
import java.util.logging.Level;

/**
 * Service layer for home operations. Handles business logic, caching, and validation.
 */
public class HomeService {

    private final AdventureSMPCore corePlugin;
    private final HomeDataAccessor dataAccessor;
    private final HomeValidator validator;
    
    // Per-player locks to ensure thread-safety for cache operations
    private final ConcurrentHashMap<UUID, ReentrantReadWriteLock> playerLocks = new ConcurrentHashMap<>();

    public HomeService(AdventureSMPCore corePlugin, HomeDataAccessor dataAccessor, HomeValidator validator) {
        this.corePlugin = corePlugin;
        this.dataAccessor = dataAccessor;
        this.validator = validator;
    }

    /**
     * Gets or creates a lock for a specific player.
     */
    private ReentrantReadWriteLock getPlayerLock(UUID playerUuid) {
        return playerLocks.computeIfAbsent(playerUuid, k -> new ReentrantReadWriteLock());
    }

    /**
     * Gets homes immediately from cache, loads from database if not cached.
     */
    public List<Home> getHomesImmediate(@NotNull UUID playerUuid) {
        List<Home> cachedHomes = getCachedHomes(playerUuid);
        if (cachedHomes != null) {
            return cachedHomes;
        }

        CompletableFuture.runAsync(() -> {
            try {
                List<Home> homes = dataAccessor.getHomes(playerUuid);
                if (corePlugin.getCacheManager() != null) {
                    corePlugin.getCacheManager().put(HomeModule.PLAYER_HOMES_CACHE_NAME, playerUuid, homes);
                }
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "Error loading homes from database for player " + playerUuid, e);
            }
        });

        return Collections.emptyList();
    }

    /**
     * Asynchronously retrieves homes for a player.
     */
    public CompletableFuture<List<Home>> getHomesAsync(@NotNull UUID playerUuid) {
        List<Home> cachedHomes = getCachedHomes(playerUuid);
        if (cachedHomes != null) {
            return CompletableFuture.completedFuture(cachedHomes);
        }

        return CompletableFuture.supplyAsync(() -> {
            ReentrantReadWriteLock lock = getPlayerLock(playerUuid);
            lock.readLock().lock();
            try {
                List<Home> doubleCheckCache = getCachedHomes(playerUuid);
                if (doubleCheckCache != null) {
                    return doubleCheckCache;
                }

                List<Home> homes = dataAccessor.getHomes(playerUuid);
                if (corePlugin.getCacheManager() != null) {
                    corePlugin.getCacheManager().put(HomeModule.PLAYER_HOMES_CACHE_NAME, playerUuid, homes);
                }
                return new ArrayList<>(homes);
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "Error loading homes from database for player " + playerUuid, e);
                return Collections.emptyList();
            } finally {
                lock.readLock().unlock();
            }
        });
    }

    /**
     * Synchronously retrieves homes for a player from cache only.
     */
    @Nullable
    public List<Home> getCachedHomes(@NotNull UUID playerUuid) {
        if (corePlugin.getCacheManager() == null) {
            return null;
        }
        
        ReentrantReadWriteLock lock = getPlayerLock(playerUuid);
        lock.readLock().lock();
        try {
            List<Home> cachedHomes = corePlugin.getCacheManager().get(
                HomeModule.PLAYER_HOMES_CACHE_NAME,
                playerUuid,
                key -> null
            );
            return cachedHomes != null ? new ArrayList<>(cachedHomes) : null;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * Sets a home immediately in cache and validates, then saves to database asynchronously.
     */
    public ValidationResult setHomeImmediate(@NotNull Player player, @NotNull String homeName) {
        try {
            // Perform all validations synchronously
            ValidationResult validation = validator.validateSetHome(player, homeName);
            if (!validation.isSuccess()) {
                return validation;
            }

            // Create home and add to cache
            Home newHome = new Home(player.getUniqueId(), homeName, player.getLocation());
            updateCacheSafely(player.getUniqueId(), homes -> {
                homes.removeIf(h -> h.getHomeName().equalsIgnoreCase(homeName));
                homes.add(newHome);
            });

            // Save to database asynchronously
            CompletableFuture.runAsync(() -> {
                try {
                    boolean dbSuccess = dataAccessor.setHome(newHome);
                    if (!dbSuccess) {
                        corePlugin.getLogger().warning("Failed to save home to database for player " + 
                            player.getName() + ", home: " + homeName);
                    }
                    corePlugin.getCacheManager().invalidateAll("adminHomePlayersWithHomesCache");
                } catch (Exception e) {
                    corePlugin.getLogger().log(Level.SEVERE, 
                        "Error saving home to database for player " + player.getName(), e);
                }
            });

            return ValidationResult.success();
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.SEVERE, "Error setting home for player " + player.getName(), e);
            return ValidationResult.error("internal_error");
        }
    }



    /**
     * Deletes a home immediately from cache, then from database asynchronously.
     */
    public boolean deleteHomeImmediate(@NotNull UUID playerUuid, @NotNull String homeName) {
        try {
            // Check if home exists in cache
            List<Home> cachedHomes = getCachedHomes(playerUuid);
            boolean homeExists = false;
            
            if (cachedHomes != null) {
                homeExists = cachedHomes.stream()
                    .anyMatch(h -> h.getHomeName().equalsIgnoreCase(homeName));
            } else {
                // If not in cache, check database synchronously
                Home home = dataAccessor.getHome(playerUuid, homeName);
                homeExists = (home != null);
                
                // Load homes into cache if found
                if (homeExists) {
                    List<Home> allHomes = dataAccessor.getHomes(playerUuid);
                    if (corePlugin.getCacheManager() != null) {
                        corePlugin.getCacheManager().put(HomeModule.PLAYER_HOMES_CACHE_NAME, playerUuid, allHomes);
                    }
                }
            }
            
            if (!homeExists) {
                return false;
            }

            // Remove from cache
            updateCacheSafely(playerUuid, homes -> 
                homes.removeIf(h -> h.getHomeName().equalsIgnoreCase(homeName))
            );

            // Delete from database asynchronously
            CompletableFuture.runAsync(() -> {
                try {
                    boolean dbSuccess = dataAccessor.deleteHome(playerUuid, homeName);
                    if (!dbSuccess) {
                        corePlugin.getLogger().warning("Failed to delete home from database for player " + 
                            playerUuid + ", home: " + homeName);
                    }
                    corePlugin.getCacheManager().invalidateAll("adminHomePlayersWithHomesCache");
                } catch (Exception e) {
                    corePlugin.getLogger().log(Level.SEVERE, 
                        "Error deleting home from database for player " + playerUuid, e);
                }
            });

            return true;
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error deleting home for player " + playerUuid, e);
            return false;
        }
    }



    /**
     * Finds a specific home immediately from cache, falls back to database if needed.
     */
    public Home findHomeImmediate(@NotNull UUID playerUuid, @NotNull String homeName) {
        try {
            List<Home> cachedHomes = getCachedHomes(playerUuid);
            if (cachedHomes != null) {
                return cachedHomes.stream()
                    .filter(h -> h.getHomeName().equalsIgnoreCase(homeName))
                    .findFirst()
                    .orElse(null);
            }
            
            // Fallback to database and cache the result
            Home home = dataAccessor.getHome(playerUuid, homeName);
            if (home != null) {
                // Load all homes into cache
                List<Home> allHomes = dataAccessor.getHomes(playerUuid);
                if (corePlugin.getCacheManager() != null) {
                    corePlugin.getCacheManager().put(HomeModule.PLAYER_HOMES_CACHE_NAME, playerUuid, allHomes);
                }
            }
            return home;
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error finding home for player " + playerUuid, e);
            return null;
        }
    }

    /**
     * Asynchronously finds a specific home.
     */
    public CompletableFuture<Home> findHomeAsync(@NotNull UUID playerUuid, @NotNull String homeName) {
        // Try cache first for immediate return
        List<Home> cachedHomes = getCachedHomes(playerUuid);
        if (cachedHomes != null) {
            Home cachedHome = cachedHomes.stream()
                .filter(h -> h.getHomeName().equalsIgnoreCase(homeName))
                .findFirst()
                .orElse(null);
            return CompletableFuture.completedFuture(cachedHome);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                // Double-check cache
                List<Home> doubleCheckCache = getCachedHomes(playerUuid);
                if (doubleCheckCache != null) {
                    return doubleCheckCache.stream()
                        .filter(h -> h.getHomeName().equalsIgnoreCase(homeName))
                        .findFirst()
                        .orElse(null);
                }
                
                // Load from database and cache
                Home home = dataAccessor.getHome(playerUuid, homeName);
                if (home != null) {
                    List<Home> allHomes = dataAccessor.getHomes(playerUuid);
                    if (corePlugin.getCacheManager() != null) {
                        corePlugin.getCacheManager().put(HomeModule.PLAYER_HOMES_CACHE_NAME, playerUuid, allHomes);
                    }
                }
                return home;
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "Error finding home for player " + playerUuid, e);
                return null;
            }
        });
    }

    /**
     * Thread-safely updates the cache with proper locking.
     */
    private void updateCacheSafely(@NotNull UUID playerUuid, @NotNull Consumer<List<Home>> updater) {
        if (corePlugin.getCacheManager() == null) {
            return;
        }

        ReentrantReadWriteLock lock = getPlayerLock(playerUuid);
        lock.writeLock().lock();
        try {
            List<Home> homes = corePlugin.getCacheManager().get(
                HomeModule.PLAYER_HOMES_CACHE_NAME,
                playerUuid,
                key -> new ArrayList<>()
            );
            
            if (homes != null) {
                // Create a mutable copy if needed
                List<Home> mutableHomes = new ArrayList<>(homes);
                updater.accept(mutableHomes);
                corePlugin.getCacheManager().put(HomeModule.PLAYER_HOMES_CACHE_NAME, playerUuid, mutableHomes);
            }
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error updating cache for player " + playerUuid, e);
        } finally {
            lock.writeLock().unlock();
        }
    }



    /**
     * Refreshes player cache from database.
     */
    public void refreshPlayerCache(@NotNull UUID playerUuid) {
        if (corePlugin.getCacheManager() == null) {
            return;
        }

        corePlugin.getServer().getScheduler().runTaskAsynchronously(corePlugin, () -> {
            ReentrantReadWriteLock lock = getPlayerLock(playerUuid);
            lock.writeLock().lock();
            try {
                // Invalidate current cache
                corePlugin.getCacheManager().invalidate(HomeModule.PLAYER_HOMES_CACHE_NAME, playerUuid);
                
                // Load fresh data from database
                List<Home> homes = dataAccessor.getHomes(playerUuid);
                corePlugin.getCacheManager().put(HomeModule.PLAYER_HOMES_CACHE_NAME, playerUuid, homes);
                
                // Also invalidate admin command players with homes cache to ensure tab completion is updated
                if (corePlugin.getCacheManager().cacheExists("adminHomePlayersWithHomesCache")) {
                    corePlugin.getCacheManager().invalidateAll("adminHomePlayersWithHomesCache");
                }
                
                corePlugin.getLogger().fine("Refreshed cache for player " + playerUuid + " with " + homes.size() + " homes");
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "Error refreshing cache for player " + playerUuid, e);
            } finally {
                lock.writeLock().unlock();
            }
        });
    }

    /**
     * Gets the current home count for a player.
     */
    public int getHomeCount(@NotNull UUID playerUuid) {
        try {
            List<Home> cachedHomes = getCachedHomes(playerUuid);
            if (cachedHomes != null) {
                return cachedHomes.size();
            }
            return dataAccessor.getHomeCount(playerUuid);
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.WARNING, "Error getting home count for player " + playerUuid, e);
            return 0;
        }
    }

    /**
     * Cleans up resources for a player.
     */
    public void cleanupPlayer(@NotNull UUID playerUuid) {
        playerLocks.remove(playerUuid);
    }

    /**
     * Performs a bulk cache refresh for multiple players.
     */
    public CompletableFuture<Void> bulkRefreshCache(@NotNull List<UUID> playerUuids) {
        return CompletableFuture.runAsync(() -> {
            for (UUID playerUuid : playerUuids) {
                try {
                    refreshPlayerCache(playerUuid);
                } catch (Exception e) {
                    corePlugin.getLogger().log(Level.WARNING, "Error in bulk refresh for player " + playerUuid, e);
                }
            }
        });
    }
} 