package de.dasjeff.aSMPVCore.events;

import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * EventManager für das ASMP-VCore Plugin.
 * Verwaltet Event-Registrierung, -Ausführung und bietet asynchrone Event-Verarbeitung.
 */
public class EventManager {

    private final ASMPVCore plugin;
    private final ConcurrentMap<Class<? extends VCoreEvent>, List<RegisteredListener>> listeners = new ConcurrentHashMap<>();
    private final ExecutorService asyncExecutor;

    public EventManager(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
        this.asyncExecutor = Executors.newFixedThreadPool(4, r -> {
            Thread thread = new Thread(r, "ASMP-VCore-Events");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * Registriert einen Event-Listener.
     * @param listener Der Listener, der registriert werden soll.
     */
    public void registerListener(@NotNull Object listener) {
        Class<?> listenerClass = listener.getClass();
        
        for (Method method : listenerClass.getDeclaredMethods()) {
            EventHandler annotation = method.getAnnotation(EventHandler.class);
            if (annotation == null) {
                continue;
            }
            
            // Validate method signature
            if (method.getParameterCount() != 1) {
                plugin.getLogger().warn("Event handler method {} in {} must have exactly one parameter", 
                        method.getName(), listenerClass.getSimpleName());
                continue;
            }
            
            Class<?> eventClass = method.getParameterTypes()[0];
            if (!VCoreEvent.class.isAssignableFrom(eventClass)) {
                plugin.getLogger().warn("Event handler method {} in {} parameter must extend VCoreEvent", 
                        method.getName(), listenerClass.getSimpleName());
                continue;
            }
            
            @SuppressWarnings("unchecked")
            Class<? extends VCoreEvent> eventType = (Class<? extends VCoreEvent>) eventClass;
            
            RegisteredListener registeredListener = new RegisteredListener(
                    listener, method, annotation.priority(), annotation.async()
            );
            
            listeners.computeIfAbsent(eventType, k -> new ArrayList<>()).add(registeredListener);
            
            plugin.getLogger().debug("Registered event handler: {}.{} for event {}", 
                    listenerClass.getSimpleName(), method.getName(), eventType.getSimpleName());
        }
        
        // Sort listeners by priority
        for (List<RegisteredListener> listenerList : listeners.values()) {
            listenerList.sort(Comparator.comparing(l -> l.priority));
        }
    }

    /**
     * Entfernt einen Event-Listener.
     * @param listener Der Listener, der entfernt werden soll.
     */
    public void unregisterListener(@NotNull Object listener) {
        for (List<RegisteredListener> listenerList : listeners.values()) {
            listenerList.removeIf(registeredListener -> registeredListener.listener == listener);
        }
        
        plugin.getLogger().debug("Unregistered event listener: {}", listener.getClass().getSimpleName());
    }

    /**
     * Entfernt alle Event-Listener.
     */
    public void unregisterAllListeners() {
        listeners.clear();
        plugin.getLogger().debug("Unregistered all event listeners");
    }

    /**
     * Feuert ein Event synchron.
     * @param event Das Event, das gefeuert werden soll.
     * @return Das Event (möglicherweise modifiziert durch Listener).
     */
    @NotNull
    public <T extends VCoreEvent> T fireEvent(@NotNull T event) {
        List<RegisteredListener> eventListeners = listeners.get(event.getClass());
        if (eventListeners == null || eventListeners.isEmpty()) {
            return event;
        }
        
        plugin.getLogger().debug("Firing event: {} to {} listeners", 
                event.getEventName(), eventListeners.size());
        
        for (RegisteredListener listener : eventListeners) {
            if (event.isCancelled() && listener.priority != EventPriority.MONITOR) {
                continue; // Skip non-monitor listeners if event is cancelled
            }
            
            try {
                if (listener.async) {
                    // Execute async listeners in thread pool
                    asyncExecutor.submit(() -> {
                        try {
                            listener.method.invoke(listener.listener, event);
                        } catch (Exception e) {
                            plugin.getLogger().error("Error in async event handler", e);
                        }
                    });
                } else {
                    // Execute sync listeners immediately
                    listener.method.invoke(listener.listener, event);
                }
            } catch (Exception e) {
                plugin.getLogger().error("Error in event handler {}.{}", 
                        listener.listener.getClass().getSimpleName(), 
                        listener.method.getName(), e);
            }
        }
        
        return event;
    }

    /**
     * Feuert ein Event asynchron.
     * @param event Das Event, das gefeuert werden soll.
     */
    public void fireEventAsync(@NotNull VCoreEvent event) {
        asyncExecutor.submit(() -> fireEvent(event));
    }

    /**
     * Gibt die Anzahl der registrierten Listener für einen Event-Typ zurück.
     * @param eventClass Die Event-Klasse.
     * @return Die Anzahl der Listener.
     */
    public int getListenerCount(@NotNull Class<? extends VCoreEvent> eventClass) {
        List<RegisteredListener> eventListeners = listeners.get(eventClass);
        return eventListeners != null ? eventListeners.size() : 0;
    }

    /**
     * Gibt die Gesamtanzahl der registrierten Listener zurück.
     * @return Die Gesamtanzahl der Listener.
     */
    public int getTotalListenerCount() {
        return listeners.values().stream().mapToInt(List::size).sum();
    }

    /**
     * Beendet den EventManager.
     */
    public void shutdown() {
        unregisterAllListeners();
        
        if (asyncExecutor != null && !asyncExecutor.isShutdown()) {
            asyncExecutor.shutdown();
        }
        
        plugin.getLogger().info("EventManager shut down.");
    }

    /**
     * Interne Klasse für registrierte Listener.
     */
    private static class RegisteredListener {
        final Object listener;
        final Method method;
        final EventPriority priority;
        final boolean async;

        RegisteredListener(Object listener, Method method, EventPriority priority, boolean async) {
            this.listener = listener;
            this.method = method;
            this.priority = priority;
            this.async = async;
            
            // Make method accessible
            method.setAccessible(true);
        }
    }
}
