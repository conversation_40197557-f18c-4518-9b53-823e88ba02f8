package de.dasjeff.adventureSMPCore.managers;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;

import java.util.ArrayList;
import java.util.List;

public class ListenerManager {

    private final AdventureSMPCore corePlugin;
    private final List<Listener> registeredListeners = new ArrayList<>();

    public ListenerManager(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
    }

    /**
     * Registers a new event listener with the server.
     *
     * @param listener The listener to register.
     */
    public void registerListener(Listener listener) {
        corePlugin.getServer().getPluginManager().registerEvents(listener, corePlugin);
        registeredListeners.add(listener);
        corePlugin.getLogger().info("Registered listener: " + listener.getClass().getSimpleName());
    }

    /**
     * Unregisters a specific listener.
     *
     * @param listener The listener to unregister.
     */
    public void unregisterListener(Listener listener) {
        HandlerList.unregisterAll(listener);
        registeredListeners.remove(listener);
        corePlugin.getLogger().info("Unregistered listener: " + listener.getClass().getSimpleName());
    }

    /**
     * Unregisters all listeners that are registered through this manager.
     */
    public void unregisterAllListeners() {
        new ArrayList<>(registeredListeners).forEach(this::unregisterListener);
        registeredListeners.clear();
        corePlugin.getLogger().info("All listeners have been unregistered.");
    }

    public List<Listener> getRegisteredListeners() {
        return new ArrayList<>(registeredListeners);
    }
} 