package de.dasjeff.adventureSMPCore.managers;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.commands.AbstractCommand;
import org.bukkit.command.PluginCommand;

import java.util.HashMap;
import java.util.Map;

public class CommandManager {

    private final AdventureSMPCore corePlugin;
    private final Map<String, AbstractCommand> registeredCommands = new HashMap<>();

    public CommandManager(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
    }

    /**
     * Registers a new command by setting its executor and tab completer.
     *
     * @param command The AbstractCommand instance to register.
     */
    public void registerCommand(AbstractCommand command) {
        PluginCommand pluginCommand = corePlugin.getCommand(command.getCommandName());

        if (pluginCommand != null) {
            pluginCommand.setExecutor(command);
            pluginCommand.setTabCompleter(command);

            if (command.getDescription() != null) {
                pluginCommand.setDescription(command.getDescription());
            }
            if (command.getUsageMessage() != null) {
                pluginCommand.setUsage(command.getUsageMessage());
            }
            if (command.getAliases() != null) {
                pluginCommand.setAliases(command.getAliases());
            }
            if (command.getPermission() != null) {
                pluginCommand.setPermission(command.getPermission());
            }

            registeredCommands.put(command.getCommandName().toLowerCase(), command);
            corePlugin.getLogger().info("Registered command '" + command.getCommandName() + "' by setting executor/tabcompleter.");
        } else {
            corePlugin.getLogger().severe("Failed to register command '" + command.getCommandName() + ". It is not defined in plugin.yml or could not be retrieved.");
        }
    }

    /**
     * Unregisters a command by clearing its executor and tab completer.
     *
     * @param commandName The name of the command to unregister.
     */
    public void unregisterCommand(String commandName) {
        PluginCommand pluginCommand = corePlugin.getCommand(commandName);
        if (pluginCommand != null) {
            pluginCommand.setExecutor(null);
            pluginCommand.setTabCompleter(null);
            corePlugin.getLogger().info("Cleared executor for command: " + commandName);
        } else {
            corePlugin.getLogger().warning("Attempted to unregister command '" + commandName + "' but it was not found (or not registered by this plugin via PluginCommand).");
        }
        registeredCommands.remove(commandName.toLowerCase());
    }

    public void unregisterAllCommands() {
        // Create a copy of the keys to avoid ConcurrentModificationException
        new HashMap<>(registeredCommands).keySet().forEach(this::unregisterCommand);
        corePlugin.getLogger().info("All custom commands have had their executors cleared.");
    }

    public AbstractCommand getRegisteredCommand(String commandName) {
        return registeredCommands.get(commandName.toLowerCase());
    }
} 