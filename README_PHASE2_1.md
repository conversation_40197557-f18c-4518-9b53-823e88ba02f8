# ASMP-VCore - Phase 2.1 Implementation

## Übersicht

Phase 2.1 der ASMP-VCore Implementierung ist abgeschlossen! Diese Phase umfasst die **Shared Models & Database Migration** mit allen notwendigen Datenstrukturen für die Kommunikation zwischen Velocity und Paper Core.

## ✅ Implementierte Features

### 🗄️ Database Schema Erweiterung

#### Neue Tabellen:
1. **`players`** - Zentrale Spielerdatenbank
   - Ersetzt die `home_playerdata` Tabelle
   - Erweiterte Felder für Player-Lookup und Tracking
   - Optimierte Indizes für Performance

2. **`player_sessions`** - Session-Tracking
   - Detaillierte Verbindungshistorie
   - Spielzeit-Tracking pro Session
   - Disconnect-Reason-Tracking

#### Tabellen-Struktur:

```sql
-- Zentrale Players-Tabelle
CREATE TABLE IF NOT EXISTS players (
    player_uuid CHAR(36) PRIMARY KEY,
    last_known_name <PERSON><PERSON><PERSON><PERSON>(16) NOT NULL,
    first_seen TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_server VARCHAR(64) NULL,
    current_server VARCHAR(64) NULL,
    total_playtime BIGINT DEFAULT 0,
    session_count INT DEFAULT 0,
    last_ip VARCHAR(45) NULL,
    last_country VARCHAR(2) NULL,
    is_vpn_user BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- Optimierte Indizes für alle Suchfelder
    INDEX idx_last_known_name (last_known_name),
    INDEX idx_last_seen (last_seen),
    INDEX idx_last_server (last_server),
    INDEX idx_current_server (current_server),
    INDEX idx_last_ip (last_ip),
    INDEX idx_last_country (last_country),
    INDEX idx_is_vpn_user (is_vpn_user)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Player Sessions Tabelle
CREATE TABLE IF NOT EXISTS player_sessions (
    session_id CHAR(36) PRIMARY KEY,
    player_uuid CHAR(36) NOT NULL,
    player_name VARCHAR(16) NOT NULL,
    server_id VARCHAR(64) NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL,
    duration_seconds BIGINT DEFAULT 0,
    status ENUM('ACTIVE', 'COMPLETED', 'DISCONNECTED') DEFAULT 'ACTIVE',
    join_ip VARCHAR(45) NULL,
    join_country VARCHAR(2) NULL,
    disconnect_reason TEXT NULL,
    -- Performance-Indizes
    INDEX idx_player_uuid (player_uuid),
    INDEX idx_server_id (server_id),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status),
    FOREIGN KEY (player_uuid) REFERENCES players(player_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 📊 Shared Models

#### 1. **PlayerData** (`src/main/java/de/dasjeff/aSMPVCore/shared/models/PlayerData.java`)
- Erweiterte Player-Informationen
- IP-Tracking und Geolokalisierung
- Spielzeit und Session-Tracking
- VPN-Erkennung
- Immutable Design mit Builder-Pattern

#### 2. **ServerInfo** (`src/main/java/de/dasjeff/aSMPVCore/shared/models/ServerInfo.java`)
- Server-Registry-Informationen
- Heartbeat-Tracking
- Performance-Metriken (TPS, Memory)
- Status-Management (ONLINE, OFFLINE, MAINTENANCE)

#### 3. **PlayerSession** (`src/main/java/de/dasjeff/aSMPVCore/shared/models/PlayerSession.java`)
- Session-Tracking für Analytics
- Verbindungsdetails (IP, Country)
- Disconnect-Reason-Tracking
- Duration-Berechnung

### 🔄 Data Access Objects (DAOs)

#### 1. **PlayerDataDAO** (`src/main/java/de/dasjeff/aSMPVCore/shared/dao/PlayerDataDAO.java`)
- Asynchrone CRUD-Operationen
- Optimierte Suchfunktionen
- IP-basierte Player-Suche
- Playtime-Management

#### 2. **ServerInfoDAO** (`src/main/java/de/dasjeff/aSMPVCore/shared/dao/ServerInfoDAO.java`)
- Server-Registry-Management
- Heartbeat-Updates
- Online-Server-Detection
- Stale-Server-Cleanup

### 🔧 Serialization Framework

#### **SerializationHelper** (`src/main/java/de/dasjeff/aSMPVCore/shared/serialization/SerializationHelper.java`)
- Type-safe JSON-Serialisierung
- Custom Adapters für Timestamp und UUID
- Generic Collection Support
- Validation und Error Handling

### 📡 Message Framework

#### 1. **MessageTypes** (`src/main/java/de/dasjeff/aSMPVCore/shared/messages/MessageTypes.java`)
- Zentrale Definition aller Nachrichtentypen
- Kategorisierung (System, Player, Server, etc.)
- Utility-Methoden für Type-Checking

#### 2. **MessageActions** (`src/main/java/de/dasjeff/aSMPVCore/shared/messages/MessageActions.java`)
- Spezifische Aktionen für jeden Message-Type
- Permission-Level-Checking
- Request/Response-Mapping

## 🎯 Vorbereitungen für Phase 2.2

### Migration Strategy:
1. **Daten-Migration**: `home_playerdata` → `players`
2. **HomeModule Update**: Verwendung der neuen zentralen Tabelle
3. **Redis-Integration**: Paper Core Redis-Manager
4. **Bidirektionale Kommunikation**: Message-Handler auf beiden Seiten

### Kompatibilität:
- **Rückwärtskompatibel**: Bestehende HomeModule-Funktionalität bleibt erhalten
- **Erweiterte Features**: Neue Felder für Phase 3 Module vorbereitet
- **Performance-Optimiert**: Indizes für alle geplanten Suchoperationen

## 🔍 Technische Details

### Database Migrations:
- **Migration 002**: `players` Tabelle
- **Migration 003**: `player_sessions` Tabelle
- **Automatisch**: Beim Plugin-Start ausgeführt

### Performance Features:
- **Asynchrone DAO-Operationen**: Alle DB-Zugriffe non-blocking
- **Optimierte Indizes**: Für alle Suchfelder
- **Connection Pooling**: HikariCP für optimale Performance
- **Batch Operations**: Für große Datenmengen

### Security Features:
- **Input Validation**: Alle Models mit Validierung
- **SQL Injection Protection**: Prepared Statements
- **Type Safety**: Generics und Annotations

## 📋 Nächste Schritte (Phase 2.2)

1. **Redis-Integration in Paper Core**
   - RedisManager hinzufügen
   - MessageManager implementieren
   - Gemeinsame Konfiguration

2. **Bidirektionale Kommunikation**
   - PaperIntegrationModule in VCore
   - VCoreIntegrationModule in Paper Core
   - Player-Sync-Mechanismen

3. **HomeModule Migration**
   - Umstellung auf zentrale `players` Tabelle
   - Daten-Migration von `home_playerdata`
   - Testing und Validation

## 🚀 Build Status

✅ **Erfolgreich kompiliert** - Alle neuen Komponenten sind funktionsfähig
✅ **Keine Breaking Changes** - Bestehende Funktionalität bleibt erhalten
✅ **Ready for Phase 2.2** - Alle Grundlagen für Paper Core Integration gelegt

Die Shared Models und Database-Struktur bilden eine solide Grundlage für die weitere Entwicklung des ASMP-VCore Systems.
