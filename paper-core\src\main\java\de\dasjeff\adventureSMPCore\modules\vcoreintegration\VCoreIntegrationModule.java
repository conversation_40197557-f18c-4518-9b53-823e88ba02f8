package de.dasjeff.adventureSMPCore.modules.vcoreintegration;

import com.google.gson.JsonObject;
import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.communication.NetworkMessage;
import de.dasjeff.adventureSMPCore.managers.MessageManager;
import de.dasjeff.adventureSMPCore.managers.RedisManager;
import de.dasjeff.adventureSMPCore.modules.AbstractModule;
import de.dasjeff.adventureSMPCore.shared.messages.MessageActions;
import de.dasjeff.adventureSMPCore.shared.messages.MessageTypes;
import org.bukkit.Bukkit;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.util.concurrent.TimeUnit;

/**
 * VCoreIntegrationModule handles communication between Paper Core and VCore (Velocity).
 * Manages player events, server heartbeats, and data synchronization.
 */
public class VCoreIntegrationModule extends AbstractModule {

    private BukkitTask heartbeatTask;
    private int heartbeatInterval;

    public VCoreIntegrationModule() {
        super("VCoreIntegration", "1.0.0", "Handles communication with VCore (Velocity)");
    }

    @Override
    public boolean onLoad() {
        debug("VCoreIntegrationModule loading...");
        
        // Get heartbeat interval from config
        this.heartbeatInterval = plugin.getConfig().getInt("server.heartbeatInterval", 30);
        
        debug("VCoreIntegrationModule loaded successfully");
        return true;
    }

    @Override
    public boolean onEnable() {
        debug("VCoreIntegrationModule enabling...");
        
        // Check if VCore integration is enabled
        if (!plugin.getConfig().getBoolean("vcore.enabled", true)) {
            warn("VCore integration is disabled in configuration");
            return false;
        }
        
        // Check if Redis is available
        RedisManager redisManager = plugin.getRedisManager();
        if (redisManager == null || !redisManager.isEnabled()) {
            error("Redis is not available - VCore integration cannot be enabled");
            return false;
        }
        
        // Register message handlers
        registerMessageHandlers();
        
        // Register server with VCore
        registerServerWithVCore();
        
        // Start heartbeat task
        startHeartbeatTask();
        
        info("VCoreIntegrationModule enabled successfully");
        return true;
    }

    @Override
    public boolean onDisable() {
        debug("VCoreIntegrationModule disabling...");
        
        // Stop heartbeat task
        if (heartbeatTask != null && !heartbeatTask.isCancelled()) {
            heartbeatTask.cancel();
            debug("Heartbeat task stopped");
        }
        
        // Unregister server from VCore
        unregisterServerFromVCore();
        
        debug("VCoreIntegrationModule disabled successfully");
        return true;
    }

    /**
     * Registers message handlers for VCore communication.
     */
    private void registerMessageHandlers() {
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            error("MessageManager not available - cannot register handlers");
            return;
        }
        
        // Register handlers for different message types
        messageManager.registerMessageHandler(MessageTypes.SYSTEM_STATUS_REQUEST, this::handleStatusRequest);
        messageManager.registerMessageHandler(MessageTypes.SYSTEM_SHUTDOWN, this::handleShutdownRequest);
        messageManager.registerMessageHandler(MessageTypes.PLAYER_DATA_REQUEST, this::handlePlayerDataRequest);
        messageManager.registerMessageHandler(MessageTypes.CACHE_INVALIDATE, this::handleCacheInvalidate);
        
        debug("Message handlers registered");
    }

    /**
     * Registers this Paper server with VCore.
     */
    private void registerServerWithVCore() {
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            return;
        }
        
        JsonObject payload = new JsonObject();
        payload.addProperty("serverId", messageManager.getServerId());
        payload.addProperty("serverName", messageManager.getServerName());
        payload.addProperty("serverType", messageManager.getServerType());
        payload.addProperty("playerCount", Bukkit.getOnlinePlayers().size());
        payload.addProperty("maxPlayers", Bukkit.getMaxPlayers());
        payload.addProperty("version", Bukkit.getVersion());
        payload.addProperty("motd", Bukkit.getMotd());
        
        messageManager.sendToVCore(MessageTypes.SERVER_REGISTER, MessageActions.REGISTER_SERVER, payload)
                .thenAccept(success -> {
                    if (success) {
                        info("Successfully registered server with VCore");
                    } else {
                        warn("Failed to register server with VCore");
                    }
                });
    }

    /**
     * Unregisters this Paper server from VCore.
     */
    private void unregisterServerFromVCore() {
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            return;
        }
        
        JsonObject payload = new JsonObject();
        payload.addProperty("serverId", messageManager.getServerId());
        payload.addProperty("reason", "Server shutdown");
        
        messageManager.sendToVCore(MessageTypes.SERVER_UNREGISTER, MessageActions.UNREGISTER_SERVER, payload)
                .thenAccept(success -> {
                    if (success) {
                        debug("Successfully unregistered server from VCore");
                    } else {
                        warn("Failed to unregister server from VCore");
                    }
                });
    }

    /**
     * Starts the heartbeat task that sends regular status updates to VCore.
     */
    private void startHeartbeatTask() {
        heartbeatTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            try {
                sendHeartbeat();
            } catch (Exception e) {
                error("Error sending heartbeat", e);
            }
        }, 20L, heartbeatInterval * 20L); // Convert seconds to ticks
        
        info("Heartbeat task started (interval: {} seconds)", heartbeatInterval);
    }

    /**
     * Sends a heartbeat message to VCore with current server status.
     */
    private void sendHeartbeat() {
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            return;
        }
        
        JsonObject payload = createHeartbeatPayload();
        
        messageManager.sendToVCore(MessageTypes.SYSTEM_HEARTBEAT, MessageActions.HEARTBEAT, payload)
                .thenAccept(success -> {
                    if (success) {
                        debug("Heartbeat sent successfully");
                    } else {
                        warn("Failed to send heartbeat");
                    }
                });
    }

    /**
     * Creates the heartbeat payload with current server information.
     */
    private JsonObject createHeartbeatPayload() {
        JsonObject payload = new JsonObject();
        
        // Basic server info
        payload.addProperty("playerCount", Bukkit.getOnlinePlayers().size());
        payload.addProperty("maxPlayers", Bukkit.getMaxPlayers());
        payload.addProperty("tps", getTPS());
        
        // Memory information
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        
        payload.addProperty("memoryUsed", usedMemory);
        payload.addProperty("memoryMax", maxMemory);
        payload.addProperty("memoryUsagePercent", (double) usedMemory / maxMemory * 100);
        
        // Server uptime
        long uptime = ManagementFactory.getRuntimeMXBean().getUptime();
        payload.addProperty("uptime", uptime);
        
        return payload;
    }

    /**
     * Gets the current TPS (simplified version).
     */
    private double getTPS() {
        try {
            // This is a simplified TPS calculation
            // In a real implementation, you might want to use a more sophisticated method
            return Math.min(20.0, 20.0);
        } catch (Exception e) {
            return 20.0; // Default to 20 TPS if calculation fails
        }
    }

    // Event Handlers

    /**
     * Handles player join events.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        // Send player join notification to VCore
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                MessageManager messageManager = plugin.getMessageManager();
                if (messageManager == null) {
                    return;
                }
                
                JsonObject payload = new JsonObject();
                payload.addProperty("playerUuid", event.getPlayer().getUniqueId().toString());
                payload.addProperty("playerName", event.getPlayer().getName());
                payload.addProperty("joinTime", System.currentTimeMillis());
                payload.addProperty("playerCount", Bukkit.getOnlinePlayers().size());
                
                messageManager.sendToVCore(MessageTypes.PLAYER_JOIN, MessageActions.JOIN_PLAYER, payload);
                debug("Player join notification sent for: {}", event.getPlayer().getName());
                
            } catch (Exception e) {
                error("Error sending player join notification", e);
            }
        });
    }

    /**
     * Handles player quit events.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Send player quit notification to VCore
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                MessageManager messageManager = plugin.getMessageManager();
                if (messageManager == null) {
                    return;
                }
                
                JsonObject payload = new JsonObject();
                payload.addProperty("playerUuid", event.getPlayer().getUniqueId().toString());
                payload.addProperty("playerName", event.getPlayer().getName());
                payload.addProperty("quitTime", System.currentTimeMillis());
                payload.addProperty("playerCount", Bukkit.getOnlinePlayers().size() - 1); // -1 because player hasn't left yet
                
                messageManager.sendToVCore(MessageTypes.PLAYER_QUIT, MessageActions.QUIT_PLAYER, payload);
                debug("Player quit notification sent for: {}", event.getPlayer().getName());
                
            } catch (Exception e) {
                error("Error sending player quit notification", e);
            }
        });
    }

    // Message Handlers

    /**
     * Handles status request messages from VCore.
     */
    private void handleStatusRequest(@NotNull NetworkMessage message) {
        debug("Received status request from VCore");
        
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            return;
        }
        
        JsonObject payload = createHeartbeatPayload();
        payload.addProperty("requestId", message.getString("requestId"));
        
        messageManager.sendToVCore(MessageTypes.SYSTEM_STATUS_RESPONSE, MessageActions.RESPONSE, payload);
    }

    /**
     * Handles shutdown request messages from VCore.
     */
    private void handleShutdownRequest(@NotNull NetworkMessage message) {
        String reason = message.getString("reason");
        warn("Shutdown request received from VCore. Reason: {}", reason != null ? reason : "No reason provided");
        
        // In a real implementation, you might want to initiate a graceful shutdown
        // For now, just log the request
    }

    /**
     * Handles player data request messages from VCore.
     */
    private void handlePlayerDataRequest(@NotNull NetworkMessage message) {
        debug("Received player data request from VCore");
        // Implementation would depend on what player data VCore needs
        // This is a placeholder for future implementation
    }

    /**
     * Handles cache invalidation messages from VCore.
     */
    private void handleCacheInvalidate(@NotNull NetworkMessage message) {
        String cacheKey = message.getString("cacheKey");
        debug("Received cache invalidation request for key: {}", cacheKey);
        
        // Invalidate local cache if needed
        if (plugin.getCacheManager() != null && cacheKey != null) {
            // Implementation would depend on cache structure
            debug("Cache invalidation processed for key: {}", cacheKey);
        }
    }
}
