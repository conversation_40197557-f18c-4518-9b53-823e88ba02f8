package de.dasjeff.adventureSMPCore.modules.vcoreintegration;

import com.google.gson.JsonObject;
import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.communication.NetworkMessage;
import de.dasjeff.adventureSMPCore.managers.MessageManager;
import de.dasjeff.adventureSMPCore.managers.RedisManager;
import de.dasjeff.adventureSMPCore.modules.IModule;
import de.dasjeff.adventureSMPCore.shared.messages.MessageActions;
import de.dasjeff.adventureSMPCore.shared.messages.MessageTypes;
import org.bukkit.Bukkit;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.scheduler.BukkitTask;
import org.jetbrains.annotations.NotNull;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.util.concurrent.TimeUnit;

/**
 * VCoreIntegrationModule handles communication between Paper Core and VCore (Velocity).
 * Manages player events, server heartbeats, and data synchronization.
 */
public class VCoreIntegrationModule implements IModule, Listener {

    private AdventureSMPCore plugin;
    private BukkitTask heartbeatTask;
    private int heartbeatInterval;

    public VCoreIntegrationModule() {
        // No super() call needed for interface implementation
    }

    @Override
    public String getName() {
        return "VCoreIntegration";
    }

    @Override
    public void onLoad(AdventureSMPCore corePlugin) {
        this.plugin = corePlugin;
        plugin.getLogger().info("[VCoreIntegration] Loading module...");

        // Get heartbeat interval from config
        this.heartbeatInterval = plugin.getConfig().getInt("server.heartbeatInterval", 30);

        plugin.getLogger().info("[VCoreIntegration] Module loaded successfully");
    }

    @Override
    public void onEnable(AdventureSMPCore corePlugin) {
        plugin.getLogger().info("[VCoreIntegration] Enabling module...");

        // Check if VCore integration is enabled
        if (!plugin.getConfig().getBoolean("vcore.enabled", true)) {
            plugin.getLogger().warning("[VCoreIntegration] VCore integration is disabled in configuration");
            return;
        }

        // Check if Redis is available
        RedisManager redisManager = plugin.getRedisManager();
        if (redisManager == null || !redisManager.isEnabled()) {
            plugin.getLogger().severe("[VCoreIntegration] Redis is not available - VCore integration cannot be enabled");
            return;
        }

        // Register event listeners
        plugin.getServer().getPluginManager().registerEvents(this, plugin);

        // Register message handlers
        registerMessageHandlers();

        // Register server with VCore
        registerServerWithVCore();

        // Start heartbeat task
        startHeartbeatTask();

        plugin.getLogger().info("[VCoreIntegration] Module enabled successfully");
    }

    @Override
    public void onDisable() {
        plugin.getLogger().info("[VCoreIntegration] Disabling module...");

        // Stop heartbeat task
        if (heartbeatTask != null && !heartbeatTask.isCancelled()) {
            heartbeatTask.cancel();
            plugin.getLogger().info("[VCoreIntegration] Heartbeat task stopped");
        }

        // Unregister server from VCore
        unregisterServerFromVCore();

        plugin.getLogger().info("[VCoreIntegration] Module disabled successfully");
    }

    /**
     * Registers message handlers for VCore communication.
     */
    private void registerMessageHandlers() {
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            plugin.getLogger().severe("[VCoreIntegration] MessageManager not available - cannot register handlers");
            return;
        }

        // Register handlers for different message types
        messageManager.registerMessageHandler(MessageTypes.SYSTEM_STATUS_REQUEST, this::handleStatusRequest);
        messageManager.registerMessageHandler(MessageTypes.SYSTEM_SHUTDOWN, this::handleShutdownRequest);
        messageManager.registerMessageHandler(MessageTypes.PLAYER_DATA_REQUEST, this::handlePlayerDataRequest);
        messageManager.registerMessageHandler(MessageTypes.CACHE_INVALIDATE, this::handleCacheInvalidate);

        plugin.getLogger().info("[VCoreIntegration] Message handlers registered");
    }

    /**
     * Registers this Paper server with VCore.
     */
    private void registerServerWithVCore() {
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            return;
        }

        JsonObject payload = new JsonObject();
        payload.addProperty("serverId", messageManager.getServerId());
        payload.addProperty("serverName", messageManager.getServerName());
        payload.addProperty("serverType", messageManager.getServerType());
        payload.addProperty("playerCount", Bukkit.getOnlinePlayers().size());
        payload.addProperty("maxPlayers", Bukkit.getMaxPlayers());
        payload.addProperty("version", Bukkit.getVersion());
        payload.addProperty("motd", Bukkit.getMotd());

        messageManager.sendToVCore(MessageTypes.SERVER_REGISTER, MessageActions.REGISTER_SERVER, payload)
                .thenAccept(success -> {
                    if (success) {
                        plugin.getLogger().info("[VCoreIntegration] Successfully registered server with VCore");
                    } else {
                        plugin.getLogger().warning("[VCoreIntegration] Failed to register server with VCore");
                    }
                });
    }

    /**
     * Unregisters this Paper server from VCore.
     */
    private void unregisterServerFromVCore() {
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            return;
        }

        JsonObject payload = new JsonObject();
        payload.addProperty("serverId", messageManager.getServerId());
        payload.addProperty("reason", "Server shutdown");

        messageManager.sendToVCore(MessageTypes.SERVER_UNREGISTER, MessageActions.UNREGISTER_SERVER, payload)
                .thenAccept(success -> {
                    if (success) {
                        plugin.getLogger().info("[VCoreIntegration] Successfully unregistered server from VCore");
                    } else {
                        plugin.getLogger().warning("[VCoreIntegration] Failed to unregister server from VCore");
                    }
                });
    }

    /**
     * Starts the heartbeat task that sends regular status updates to VCore.
     */
    private void startHeartbeatTask() {
        heartbeatTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            try {
                sendHeartbeat();
            } catch (Exception e) {
                plugin.getLogger().severe("[VCoreIntegration] Error sending heartbeat: " + e.getMessage());
                e.printStackTrace();
            }
        }, 20L, heartbeatInterval * 20L); // Convert seconds to ticks

        plugin.getLogger().info("[VCoreIntegration] Heartbeat task started (interval: " + heartbeatInterval + " seconds)");
    }

    /**
     * Sends a heartbeat message to VCore with current server status.
     */
    private void sendHeartbeat() {
        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            return;
        }

        JsonObject payload = createHeartbeatPayload();

        messageManager.sendToVCore(MessageTypes.SYSTEM_HEARTBEAT, MessageActions.HEARTBEAT, payload)
                .thenAccept(success -> {
                    if (success) {
                        // Debug level - don't spam logs with successful heartbeats
                        if (plugin.getConfig().getBoolean("debug_mode", false)) {
                            plugin.getLogger().info("[VCoreIntegration] Heartbeat sent successfully");
                        }
                    } else {
                        plugin.getLogger().warning("[VCoreIntegration] Failed to send heartbeat");
                    }
                });
    }

    /**
     * Creates the heartbeat payload with current server information.
     */
    private JsonObject createHeartbeatPayload() {
        JsonObject payload = new JsonObject();

        // Basic server info
        payload.addProperty("playerCount", Bukkit.getOnlinePlayers().size());
        payload.addProperty("maxPlayers", Bukkit.getMaxPlayers());
        payload.addProperty("tps", getTPS());

        // Memory information
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();

        payload.addProperty("memoryUsed", usedMemory);
        payload.addProperty("memoryMax", maxMemory);
        payload.addProperty("memoryUsagePercent", (double) usedMemory / maxMemory * 100);

        // Server uptime
        long uptime = ManagementFactory.getRuntimeMXBean().getUptime();
        payload.addProperty("uptime", uptime);

        // Paper-specific metrics
        payload.addProperty("worldCount", Bukkit.getWorlds().size());
        payload.addProperty("pluginCount", Bukkit.getPluginManager().getPlugins().length);

        return payload;
    }

    /**
     * Gets the current TPS (simplified version).
     */
    private double getTPS() {
        try {
            // This is a simplified TPS calculation
            // In a real implementation, you might want to use a more sophisticated method
            return Math.min(20.0, 20.0);
        } catch (Exception e) {
            return 20.0; // Default to 20 TPS if calculation fails
        }
    }

    // Event Handlers

    /**
     * Handles player join events.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        // Send player join notification to VCore
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                MessageManager messageManager = plugin.getMessageManager();
                if (messageManager == null) {
                    return;
                }

                JsonObject payload = new JsonObject();
                payload.addProperty("playerUuid", event.getPlayer().getUniqueId().toString());
                payload.addProperty("playerName", event.getPlayer().getName());
                payload.addProperty("joinTime", System.currentTimeMillis());
                payload.addProperty("playerCount", Bukkit.getOnlinePlayers().size());

                messageManager.sendToVCore(MessageTypes.PLAYER_JOIN, MessageActions.JOIN_PLAYER, payload);
                if (plugin.getConfig().getBoolean("debug_mode", false)) {
                    plugin.getLogger().info("[VCoreIntegration] Player join notification sent for: " + event.getPlayer().getName());
                }

            } catch (Exception e) {
                plugin.getLogger().severe("[VCoreIntegration] Error sending player join notification: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * Handles player quit events.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Send player quit notification to VCore
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                MessageManager messageManager = plugin.getMessageManager();
                if (messageManager == null) {
                    return;
                }

                JsonObject payload = new JsonObject();
                payload.addProperty("playerUuid", event.getPlayer().getUniqueId().toString());
                payload.addProperty("playerName", event.getPlayer().getName());
                payload.addProperty("quitTime", System.currentTimeMillis());
                payload.addProperty("playerCount", Bukkit.getOnlinePlayers().size() - 1); // -1 because player hasn't left yet

                messageManager.sendToVCore(MessageTypes.PLAYER_QUIT, MessageActions.QUIT_PLAYER, payload);
                if (plugin.getConfig().getBoolean("debug_mode", false)) {
                    plugin.getLogger().info("[VCoreIntegration] Player quit notification sent for: " + event.getPlayer().getName());
                }

            } catch (Exception e) {
                plugin.getLogger().severe("[VCoreIntegration] Error sending player quit notification: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    // Message Handlers

    /**
     * Handles status request messages from VCore.
     */
    private void handleStatusRequest(@NotNull NetworkMessage message) {
        if (plugin.getConfig().getBoolean("debug_mode", false)) {
            plugin.getLogger().info("[VCoreIntegration] Received status request from VCore");
        }

        MessageManager messageManager = plugin.getMessageManager();
        if (messageManager == null) {
            return;
        }

        JsonObject payload = createHeartbeatPayload();
        payload.addProperty("requestId", message.getString("requestId"));

        messageManager.sendToVCore(MessageTypes.SYSTEM_STATUS_RESPONSE, MessageActions.RESPONSE, payload);
    }

    /**
     * Handles shutdown request messages from VCore.
     */
    private void handleShutdownRequest(@NotNull NetworkMessage message) {
        String reason = message.getString("reason");
        plugin.getLogger().warning("[VCoreIntegration] Shutdown request received from VCore. Reason: " +
                                  (reason != null ? reason : "No reason provided"));

        // In a real implementation, you might want to initiate a graceful shutdown
        // For now, just log the request
    }

    /**
     * Handles player data request messages from VCore.
     */
    private void handlePlayerDataRequest(@NotNull NetworkMessage message) {
        if (plugin.getConfig().getBoolean("debug_mode", false)) {
            plugin.getLogger().info("[VCoreIntegration] Received player data request from VCore");
        }
        // Implementation would depend on what player data VCore needs
        // This is a placeholder for future implementation
    }

    /**
     * Handles cache invalidation messages from VCore.
     */
    private void handleCacheInvalidate(@NotNull NetworkMessage message) {
        String cacheKey = message.getString("cacheKey");
        if (plugin.getConfig().getBoolean("debug_mode", false)) {
            plugin.getLogger().info("[VCoreIntegration] Received cache invalidation request for key: " + cacheKey);
        }

        // Invalidate local cache if needed
        if (plugin.getCacheManager() != null && cacheKey != null) {
            // Implementation would depend on cache structure
            if (plugin.getConfig().getBoolean("debug_mode", false)) {
                plugin.getLogger().info("[VCoreIntegration] Cache invalidation processed for key: " + cacheKey);
            }
        }
    }
}
