package de.dasjeff.aSMPVCore.managers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.dasjeff.aSMPVCore.ASMPVCore;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * ConfigManager für das ASMP-VCore Plugin.
 * Verwaltet YAML-ähnliche JSON-Konfigurationsdateien mit Standardwerten und Reload-Funktionalität.
 */
public class ConfigManager {

    private final ASMPVCore plugin;
    private final Path dataDirectory;
    private final Gson gson;
    private final ConcurrentMap<String, JsonObject> configs = new ConcurrentHashMap<>();
    
    private JsonObject mainConfig;
    private Path mainConfigFile;

    public ConfigManager(@NotNull ASMPVCore plugin, @NotNull Path dataDirectory) {
        this.plugin = plugin;
        this.dataDirectory = dataDirectory;
        this.gson = new GsonBuilder()
                .setPrettyPrinting()
                .disableHtmlEscaping()
                .create();
        
        initializeMainConfig();
    }

    /**
     * Initialisiert die Hauptkonfigurationsdatei.
     */
    private void initializeMainConfig() {
        try {
            // Erstelle Datenverzeichnis falls es nicht existiert
            if (!Files.exists(dataDirectory)) {
                Files.createDirectories(dataDirectory);
            }
            
            mainConfigFile = dataDirectory.resolve("config.json");
            
            if (!Files.exists(mainConfigFile)) {
                createDefaultMainConfig();
            }
            
            loadMainConfig();
            
        } catch (IOException e) {
            plugin.getLogger().error("Failed to initialize main configuration!", e);
            throw new RuntimeException("Configuration initialization failed", e);
        }
    }

    /**
     * Erstellt die Standard-Hauptkonfiguration.
     */
    private void createDefaultMainConfig() throws IOException {
        JsonObject defaultConfig = new JsonObject();
        
        // Database configuration
        JsonObject database = new JsonObject();
        database.addProperty("enabled", true);
        database.addProperty("host", "localhost");
        database.addProperty("port", 3306);
        database.addProperty("database", "asmp_vcore");
        database.addProperty("username", "root");
        database.addProperty("password", "your_password");
        database.addProperty("useSSL", false);
        database.addProperty("poolSize", 10);
        database.addProperty("connectionTimeout", 30000);
        database.addProperty("idleTimeout", 600000);
        database.addProperty("maxLifetime", 1800000);
        defaultConfig.add("database", database);
        
        // Redis configuration
        JsonObject redis = new JsonObject();
        redis.addProperty("enabled", true);
        redis.addProperty("host", "localhost");
        redis.addProperty("port", 6379);
        redis.addProperty("password", "");
        redis.addProperty("database", 0);
        redis.addProperty("timeout", 2000);
        redis.addProperty("poolSize", 8);
        defaultConfig.add("redis", redis);
        
        // Security configuration
        JsonObject security = new JsonObject();
        security.addProperty("serverToken", "change-this-token");
        security.addProperty("hmacSecret", "change-this-secret");
        security.addProperty("tokenRotationHours", 24);
        security.addProperty("maxMessageSize", 1048576); // 1MB
        defaultConfig.add("security", security);
        
        // Cache configuration
        JsonObject cache = new JsonObject();
        cache.addProperty("enableStatistics", true);
        cache.addProperty("defaultMaxSize", 1000);
        cache.addProperty("defaultExpireMinutes", 30);
        defaultConfig.add("cache", cache);
        
        // Server configuration
        JsonObject server = new JsonObject();
        server.addProperty("serverId", "velocity-proxy");
        server.addProperty("serverName", "ASMP Network");
        server.addProperty("heartbeatInterval", 30);
        defaultConfig.add("server", server);
        
        // Message configuration
        JsonObject messaging = new JsonObject();
        messaging.addProperty("retryAttempts", 3);
        messaging.addProperty("retryDelay", 1000);
        messaging.addProperty("messageTimeout", 5000);
        defaultConfig.add("messaging", messaging);
        
        // Write default config
        try (FileWriter writer = new FileWriter(mainConfigFile.toFile())) {
            gson.toJson(defaultConfig, writer);
        }
        
        plugin.getLogger().info("Created default configuration file: {}", mainConfigFile);
    }

    /**
     * Lädt die Hauptkonfiguration.
     */
    private void loadMainConfig() throws IOException {
        try (FileReader reader = new FileReader(mainConfigFile.toFile())) {
            mainConfig = JsonParser.parseReader(reader).getAsJsonObject();
            configs.put("main", mainConfig);
            plugin.getLogger().info("Main configuration loaded successfully.");
        }
    }

    /**
     * Gibt die Hauptkonfiguration zurück.
     */
    @NotNull
    public JsonObject getMainConfig() {
        return mainConfig;
    }

    /**
     * Lädt eine benutzerdefinierte Konfigurationsdatei.
     */
    @Nullable
    public JsonObject loadCustomConfig(@NotNull String fileName) {
        try {
            Path configFile = dataDirectory.resolve(fileName);
            
            if (!Files.exists(configFile)) {
                plugin.getLogger().warn("Configuration file {} does not exist.", fileName);
                return null;
            }
            
            try (FileReader reader = new FileReader(configFile.toFile())) {
                JsonObject config = JsonParser.parseReader(reader).getAsJsonObject();
                configs.put(fileName, config);
                return config;
            }
            
        } catch (IOException e) {
            plugin.getLogger().error("Failed to load configuration file: {}", fileName, e);
            return null;
        }
    }

    /**
     * Speichert eine Konfiguration in eine Datei.
     */
    public boolean saveConfig(@NotNull String fileName, @NotNull JsonObject config) {
        try {
            Path configFile = dataDirectory.resolve(fileName);
            
            try (FileWriter writer = new FileWriter(configFile.toFile())) {
                gson.toJson(config, writer);
                configs.put(fileName, config);
                plugin.getLogger().info("Configuration saved: {}", fileName);
                return true;
            }
            
        } catch (IOException e) {
            plugin.getLogger().error("Failed to save configuration file: {}", fileName, e);
            return false;
        }
    }

    /**
     * Lädt die Hauptkonfiguration neu.
     */
    public boolean reloadMainConfig() {
        try {
            loadMainConfig();
            plugin.getLogger().info("Main configuration reloaded successfully.");
            return true;
        } catch (IOException e) {
            plugin.getLogger().error("Failed to reload main configuration!", e);
            return false;
        }
    }

    /**
     * Lädt alle Konfigurationen neu.
     */
    public void reloadAllConfigs() {
        reloadMainConfig();
        
        // Reload all cached configs
        for (String fileName : configs.keySet()) {
            if (!"main".equals(fileName)) {
                loadCustomConfig(fileName);
            }
        }
    }

    /**
     * Hilfsmethode zum Abrufen von String-Werten aus der Konfiguration.
     */
    public String getString(@NotNull String path, @NotNull String defaultValue) {
        return getConfigValue(mainConfig, path, defaultValue, String.class);
    }

    /**
     * Hilfsmethode zum Abrufen von Boolean-Werten aus der Konfiguration.
     */
    public boolean getBoolean(@NotNull String path, boolean defaultValue) {
        return getConfigValue(mainConfig, path, defaultValue, Boolean.class);
    }

    /**
     * Hilfsmethode zum Abrufen von Integer-Werten aus der Konfiguration.
     */
    public int getInt(@NotNull String path, int defaultValue) {
        return getConfigValue(mainConfig, path, defaultValue, Integer.class);
    }

    /**
     * Hilfsmethode zum Abrufen von Long-Werten aus der Konfiguration.
     */
    public long getLong(@NotNull String path, long defaultValue) {
        return getConfigValue(mainConfig, path, defaultValue, Long.class);
    }

    /**
     * Generische Methode zum Abrufen von Konfigurationswerten.
     */
    @SuppressWarnings("unchecked")
    private <T> T getConfigValue(@NotNull JsonObject config, @NotNull String path, @NotNull T defaultValue, @NotNull Class<T> type) {
        try {
            String[] parts = path.split("\\.");
            JsonObject current = config;
            
            for (int i = 0; i < parts.length - 1; i++) {
                if (current.has(parts[i]) && current.get(parts[i]).isJsonObject()) {
                    current = current.getAsJsonObject(parts[i]);
                } else {
                    return defaultValue;
                }
            }
            
            String finalKey = parts[parts.length - 1];
            if (!current.has(finalKey)) {
                return defaultValue;
            }
            
            if (type == String.class) {
                return (T) current.get(finalKey).getAsString();
            } else if (type == Boolean.class) {
                return (T) Boolean.valueOf(current.get(finalKey).getAsBoolean());
            } else if (type == Integer.class) {
                return (T) Integer.valueOf(current.get(finalKey).getAsInt());
            } else if (type == Long.class) {
                return (T) Long.valueOf(current.get(finalKey).getAsLong());
            }
            
            return defaultValue;
            
        } catch (Exception e) {
            plugin.getLogger().warn("Failed to get config value for path: {}, using default: {}", path, defaultValue);
            return defaultValue;
        }
    }

    /**
     * Gibt das Datenverzeichnis zurück.
     */
    @NotNull
    public Path getDataDirectory() {
        return dataDirectory;
    }

    /**
     * Prüft, ob die Konfiguration Standardwerte enthält.
     */
    public boolean hasDefaultValues() {
        return "your_password".equals(getString("database.password", "")) ||
               "change-this-token".equals(getString("security.serverToken", "")) ||
               "change-this-secret".equals(getString("security.hmacSecret", ""));
    }
}
