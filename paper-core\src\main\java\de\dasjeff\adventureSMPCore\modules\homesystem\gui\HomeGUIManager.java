package de.dasjeff.adventureSMPCore.modules.homesystem.gui;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.model.Home;
import de.dasjeff.adventureSMPCore.modules.homesystem.service.HomeService;
import de.dasjeff.adventureSMPCore.util.ChatUtil;
import de.dasjeff.adventureSMPCore.util.ItemBuilder;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class HomeGUIManager implements Listener {

    private final AdventureSMPCore corePlugin;
    private final HomeModule homeModule;
    private final HomeService homeService;
    private final Map<UUID, Integer> openGUIPages = new ConcurrentHashMap<>();

    private final String baseGuiTitle;
    private final String baseDeleteConfirmTitle;
    private final String deleteConfirmTitleTemplate;

    private final String guiTitleTemplate;
    private final String prevPageItemName;
    private final String nextPageItemName;
    private final String deleteCancelItemName;
    private final String deleteConfirmItemNameTemplate;

    public HomeGUIManager(AdventureSMPCore corePlugin, HomeModule homeModule) {
        this.corePlugin = corePlugin;
        this.homeModule = homeModule;
        this.homeService = homeModule.getHomeService();

        this.baseGuiTitle = homeModule.getGuiConfig().getGuiMessage("gui_title")
            .replaceAll("\\s*\\{current_page}.*$", "").trim();
        this.baseDeleteConfirmTitle = homeModule.getGuiConfig().getGuiMessage("gui_delete_confirm_title")
            .replaceAll("\\s*\\{home_name}.*$", "").trim();
        this.deleteConfirmTitleTemplate = homeModule.getGuiConfig().getGuiMessage("gui_delete_confirm_title");

        this.guiTitleTemplate = homeModule.getGuiConfig().getGuiMessage("gui_title");
        this.prevPageItemName = homeModule.getGuiConfig().getGuiMessage("gui_prev_page_item_name");
        this.nextPageItemName = homeModule.getGuiConfig().getGuiMessage("gui_next_page_item_name");
        this.deleteCancelItemName = homeModule.getGuiConfig().getGuiMessage("gui_delete_cancel_item_name");
        this.deleteConfirmItemNameTemplate = homeModule.getGuiConfig().getGuiMessage("gui_delete_confirm_item_name");
    }

    public void openHomeListGUI(Player player, int pageParam) {
        List<Home> homes = homeService.getHomesImmediate(player.getUniqueId());
        
        if (!homes.isEmpty()) {
            displayHomeGUI(player, homes, pageParam);
            return;
        }

        homeService.getHomesAsync(player.getUniqueId())
            .thenAccept(loadedHomes -> {
                corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                    displayHomeGUI(player, loadedHomes, pageParam);
                });
            })
            .exceptionally(throwable -> {
                corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                    homeModule.getMessageConfig().sendMessage(player, "internal_error");
                    homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("error"));
                });
                return null;
            });
    }
    
    private void displayHomeGUI(Player player, List<Home> allHomes, int pageParam) {
        if (allHomes.isEmpty()) {
            homeModule.getMessageConfig().sendMessage(player, "no_homes_set");
            homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("error"));
            return;
        }

        int itemsPerPage = homeModule.getGuiConfig().getHomeListRows() * 9 - (homeModule.getGuiConfig().getHomeListRows() > 1 ? 9 : 0);
        if (itemsPerPage <= 0) itemsPerPage = 1;
        
        int totalPages = (int) Math.ceil((double) allHomes.size() / itemsPerPage);
        int finalPage = Math.max(1, Math.min(pageParam, totalPages));

        String guiTitleLegacy = guiTitleTemplate
            .replace("{player_name}", player.getName())
            .replace("{current_page}", String.valueOf(finalPage))
            .replace("{total_pages}", String.valueOf(totalPages));
        Component guiTitleComponent = LegacyComponentSerializer.legacySection().deserialize(guiTitleLegacy);
        Inventory gui = Bukkit.createInventory(player, homeModule.getGuiConfig().getHomeListRows() * 9, guiTitleComponent);

        int startIndex = (finalPage - 1) * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, allHomes.size());
        List<Home> homesForPage = allHomes.subList(startIndex, endIndex);

        for (int i = 0; i < homesForPage.size(); i++) {
            Home home = homesForPage.get(i);
            ItemStack item = createHomeItem(home);
            gui.setItem(i, item);
        }

        if (homeModule.getGuiConfig().isFillEmptySlots()) {
            ItemStack filler = new ItemBuilder(homeModule.getGuiConfig().getFillEmptySlotsMaterial())
                                .setName(homeModule.getGuiConfig().getFillEmptySlotsName())
                                .build();
            for (int i = homesForPage.size(); i < itemsPerPage; i++) {
                 if (gui.getItem(i) == null) gui.setItem(i, filler);
            }
        }

        if (homeModule.getGuiConfig().getHomeListRows() > 1) {
            addNavigationControls(gui, finalPage, totalPages);
        }

        player.openInventory(gui);
        openGUIPages.put(player.getUniqueId(), finalPage);
    }

    private void addNavigationControls(Inventory gui, int currentPage, int totalPages) {
        int navRowBaseSlot = (homeModule.getGuiConfig().getHomeListRows() - 1) * 9;
        
        if (currentPage > 1) {
            ItemStack prevPageItem = new ItemBuilder(homeModule.getGuiConfig().getPrevPageItemMaterial())
                .setName(prevPageItemName)
                .build();
            gui.setItem(navRowBaseSlot + homeModule.getGuiConfig().getPrevPageSlot(), prevPageItem); 
        }
        
        if (currentPage < totalPages) {
            ItemStack nextPageItem = new ItemBuilder(homeModule.getGuiConfig().getNextPageItemMaterial())
                .setName(nextPageItemName)
                .build();
            gui.setItem(navRowBaseSlot + homeModule.getGuiConfig().getNextPageSlot(), nextPageItem);
        }
        
        ItemStack closeButton = new ItemBuilder(Material.BARRIER)
                .setName("&cSchließen")
                .build();
        gui.setItem(navRowBaseSlot + 4, closeButton);
    }

    private ItemStack createHomeItem(Home home) {
        Material itemMaterial = homeModule.getGuiConfig().getHomeItemMaterial();
        if (itemMaterial == null) itemMaterial = Material.PAPER;

        ItemBuilder builder = new ItemBuilder(itemMaterial)
            .setName(homeModule.getGuiConfig().getGuiMessage("gui_item_name", "{home_name}", home.getHomeName()));

        List<String> loreStrings = new ArrayList<>();
        World world = Bukkit.getWorld(home.getWorldUuid());
        String worldName = (world != null) ? world.getName() : "Unknown World";

        for (String line : homeModule.getGuiConfig().getGuiMessageList("gui_item_lore")) {
            loreStrings.add(line
                .replace("{world_name}", worldName)
                .replace("{x}", String.format("%.1f", home.getX()))
                .replace("{y}", String.format("%.1f", home.getY()))
                .replace("{z}", String.format("%.1f", home.getZ()))
            );
        }
        builder.setLore(loreStrings);
        
        builder.addLoreLine("&0" + home.getHomeName());

        if (homeModule.getGuiConfig().isHomeItemGlow()) {
            builder.setGlowing(true);
        }
        if (homeModule.getGuiConfig().getHomeItemCustomModelData() != 0) {
             builder.setCustomModelData(homeModule.getGuiConfig().getHomeItemCustomModelData());
        }

        return builder.build();
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) return;
        
        if (!isHomeGUI(event.getView().title())) return;
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        ItemMeta meta = clickedItem.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) return;

        handleGUIClick(player, event, clickedItem, meta);
    }

    private boolean isHomeGUI(Component title) {
        String inventoryTitleLegacy = LegacyComponentSerializer.legacySection().serialize(title);
        return inventoryTitleLegacy.startsWith(baseGuiTitle);
    }

    private void handleGUIClick(Player player, InventoryClickEvent event, ItemStack clickedItem, ItemMeta meta) {
        Component displayNameComponent = meta.displayName();
        String displayNameLegacy = LegacyComponentSerializer.legacySection().serialize(displayNameComponent);

        int currentPage = openGUIPages.getOrDefault(player.getUniqueId(), 1);
        int navRowBaseSlot = (homeModule.getGuiConfig().getHomeListRows() - 1) * 9;

        if (handleNavigation(player, event.getSlot(), navRowBaseSlot, displayNameLegacy, currentPage)) {
            return;
        }

        if (clickedItem.getType() == homeModule.getGuiConfig().getHomeItemMaterial()) {
            handleHomeItemClick(player, event, meta);
        }
    }

    private boolean handleNavigation(Player player, int slot, int navRowBaseSlot, String displayName, int currentPage) {
        String closeButtonNameLegacy = ChatUtil.formatLegacy("&cClose");

        if (slot == navRowBaseSlot + homeModule.getGuiConfig().getPrevPageSlot() && displayName.equals(prevPageItemName)) {
            if (currentPage > 1) {
                openHomeListGUI(player, currentPage - 1);
                homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("gui_click"));
            }
            return true;
        }
        
        if (slot == navRowBaseSlot + homeModule.getGuiConfig().getNextPageSlot() && displayName.equals(nextPageItemName)) {
            openHomeListGUI(player, currentPage + 1);
            homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("gui_click"));
            return true;
        }
        
        if (slot == navRowBaseSlot + 4 && displayName.equals(closeButtonNameLegacy)) {
            player.closeInventory();
            homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("gui_click"));
            return true;
        }
        
        return false;
    }

    private void handleHomeItemClick(Player player, InventoryClickEvent event, ItemMeta meta) {
        List<Component> componentLore = meta.lore();
        if (componentLore == null || componentLore.isEmpty()) return;
        
        Component lastLoreLineComponent = componentLore.getLast();
        String lastLoreLineLegacy = LegacyComponentSerializer.legacySection().serialize(lastLoreLineComponent);
        String homeName = ChatUtil.stripColor(lastLoreLineLegacy);
        
        if (event.isLeftClick()) {
            handleHomeTeleport(player, homeName);
        } else if (event.isRightClick()) {
            openDeleteConfirmationGUI(player, homeName);
            homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("gui_click"));
        }
    }

    private void handleHomeTeleport(Player player, String homeName) {
        player.closeInventory();

        Home home = homeService.findHomeImmediate(player.getUniqueId(), homeName);
        
        if (home != null) {
            Location location = home.toLocation();
            if (location == null) {
                homeModule.getMessageConfig().sendMessage(player, "home_world_not_found", "{home_name}", homeName);
                homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("error"));
                return;
            }
            
            homeModule.getTeleportManager().startTeleport(
                player, 
                location, 
                homeName, 
                "teleport_success", 
                "teleport_initiated"
            );
        } else {
            homeModule.getMessageConfig().sendMessage(player, "home_not_found", "{home_name}", homeName);
            homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("error"));
        }
    }

    public void openDeleteConfirmationGUI(Player player, String homeName) {
        String titleLegacy = deleteConfirmTitleTemplate.replace("{home_name}", homeName);
        Component titleComponent = LegacyComponentSerializer.legacySection().deserialize(titleLegacy);
        Inventory confirmGui = Bukkit.createInventory(player, 9, titleComponent);

        ItemStack confirmItem = new ItemBuilder(homeModule.getGuiConfig().getDeleteConfirmItemMaterial())
            .setName(deleteConfirmItemNameTemplate.replace("{home_name}", homeName))
            .setLore(homeModule.getGuiConfig().getGuiMessageList("gui_delete_confirm_item_lore"))
            .addLoreLine("&0"+homeName)
            .setGlowing(homeModule.getGuiConfig().isDeleteConfirmItemGlow())
            .build();

        ItemStack cancelItem = new ItemBuilder(homeModule.getGuiConfig().getDeleteCancelItemMaterial())
            .setName(deleteCancelItemName)
            .setGlowing(homeModule.getGuiConfig().isDeleteCancelItemGlow())
            .build();

        confirmGui.setItem(homeModule.getGuiConfig().getDeleteConfirmSlot(), confirmItem);
        confirmGui.setItem(homeModule.getGuiConfig().getDeleteCancelSlot(), cancelItem);
        
        if (homeModule.getGuiConfig().isDeleteConfirmFillEmpty()) {
             ItemStack filler = new ItemBuilder(homeModule.getGuiConfig().getDeleteConfirmFillMaterial())
                                .setName(" ")
                                .build();
            for(int i = 0; i < confirmGui.getSize(); i++){
                if(confirmGui.getItem(i) == null) confirmGui.setItem(i, filler);
            }
        }

        player.openInventory(confirmGui);
    }

    @EventHandler
    public void onDeleteConfirmClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) return;
        
        if (!isDeleteConfirmGUI(event.getView().title())) return;
        
        event.setCancelled(true);
        handleDeleteConfirmClick(player, event);
    }

    private boolean isDeleteConfirmGUI(Component title) {
        String viewTitleLegacy = LegacyComponentSerializer.legacySection().serialize(title);
        return viewTitleLegacy.startsWith(baseDeleteConfirmTitle);
    }

    private void handleDeleteConfirmClick(Player player, InventoryClickEvent event) {
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || !clickedItem.hasItemMeta()) return;
        
        ItemMeta meta = clickedItem.getItemMeta();
        if (!meta.hasDisplayName()) return;

        String homeName = extractHomeNameFromTitle(event.getView().title());
        if (homeName.isEmpty()) return;

        Component displayNameComponent = meta.displayName();
        String displayNameLegacy = LegacyComponentSerializer.legacySection().serialize(displayNameComponent);

        String confirmNameLegacy = deleteConfirmItemNameTemplate.replace("{home_name}", homeName);

        if (displayNameLegacy.equals(confirmNameLegacy)) {
            handleDeleteConfirm(player, homeName);
        } else if (displayNameLegacy.equals(deleteCancelItemName)) {
            player.closeInventory();
            homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("gui_click"));
        }
    }

    private String extractHomeNameFromTitle(Component title) {
        String viewTitleLegacy = LegacyComponentSerializer.legacySection().serialize(title);
        int colonIndex = viewTitleLegacy.lastIndexOf(":");
        if (colonIndex != -1 && colonIndex < viewTitleLegacy.length() - 1) {
            return ChatUtil.stripColor(viewTitleLegacy.substring(colonIndex + 1)).trim();
        }
        return "";
    }

    private void handleDeleteConfirm(Player player, String homeName) {
        player.closeInventory();

        boolean success = homeService.deleteHomeImmediate(player.getUniqueId(), homeName);
        
        if (success) {
            homeModule.getMessageConfig().sendMessage(player, "home_delete_success", "{home_name}", homeName);
            homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("delete_home"));
        } else {
            homeModule.getMessageConfig().sendMessage(player, "home_delete_not_found", "{home_name}", homeName);
            homeModule.playHomeSound(player, homeModule.getHomeConfig().getSound("error"));
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player player) {
            Component viewTitleComponent = event.getView().title();
            String viewTitleLegacy = LegacyComponentSerializer.legacySection().serialize(viewTitleComponent);

            if (viewTitleLegacy.startsWith(baseGuiTitle) || viewTitleLegacy.startsWith(baseDeleteConfirmTitle)) {
                 openGUIPages.remove(player.getUniqueId());
            }
        }
    }
    

} 