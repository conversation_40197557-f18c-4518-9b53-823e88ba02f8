package de.dasjeff.adventureSMPCore.managers;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import org.bukkit.configuration.file.FileConfiguration;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.logging.Level;

public class DatabaseManager {

    private final AdventureSMPCore corePlugin;
    private HikariDataSource dataSource;
    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private final String password;
    private final boolean useSSL;
    private final int poolSize;
    private final long connectionTimeout;
    private final long idleTimeout;
    private final long maxLifetime;

    public DatabaseManager(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
        FileConfiguration config = corePlugin.getConfigManager().getMainConfig();

        this.host = config.getString("database.host", "localhost");
        this.port = config.getInt("database.port", 3306);
        this.database = config.getString("database.database", "adventuresmp_core");
        this.username = config.getString("database.username", "root");
        this.password = config.getString("database.password", "your_password");
        this.useSSL = config.getBoolean("database.useSSL", false);

        this.poolSize = config.getInt("database.poolSize", 10);
        this.connectionTimeout = config.getLong("database.connectionTimeout", 30000);
        this.idleTimeout = config.getLong("database.idleTimeout", 600000);
        this.maxLifetime = config.getLong("database.maxLifetime", 1800000);
    }

    /**
     * Initializes the data source and checks for default config values.
     * @return true if initialization succeeded and config is valid; false to disable plugin.
     */
    public boolean initialize() {
        boolean defaultConfig = host.equals("localhost")
                && port == 3306
                && database.equals("adventuresmp_core")
                && username.equals("root")
                && password.equals("your_password");
        if (defaultConfig) {
            corePlugin.getLogger().severe("==================================================");
            corePlugin.getLogger().severe("AdventureSMP-Core DETECTED DEFAULT DATABASE CONFIGURATION!");
            corePlugin.getLogger().severe("Please configure your database settings in plugins/AdventureSMP-Core/config.yml");
            corePlugin.getLogger().severe("Ensure the database server is running before enabling the plugin.");
            corePlugin.getLogger().severe("Plugin will not enable until configured.");
            corePlugin.getLogger().severe("==================================================");
            return false;
        }
        setupDataSource();
        // Verify connection pool is active
        if (isConnected()) {
            return true;
        } else {
            corePlugin.getLogger().severe("DatabaseManager failed to establish a connection pool.");
            return false;
        }
    }

    private void setupDataSource() {
        HikariConfig hikariConfig = new HikariConfig();
        String driverClassName = "de.dasjeff.adventureSMPCore.lib.mariadb.Driver";
        try {
            Class.forName(driverClassName, true, getClass().getClassLoader());
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.SEVERE, "Failed to load driver class " + driverClassName, e);
        }
        hikariConfig.setDriverClassName(driverClassName);
        hikariConfig.setJdbcUrl(String.format("****************************************************",
                host, port, database, useSSL));
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setMaximumPoolSize(poolSize);
        hikariConfig.setConnectionTimeout(connectionTimeout);
        hikariConfig.setIdleTimeout(idleTimeout);
        hikariConfig.setMaxLifetime(maxLifetime);
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");
        hikariConfig.addDataSourceProperty("useLocalSessionState", "true");
        hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true");
        hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true");
        hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true");
        hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true");
        hikariConfig.addDataSourceProperty("maintainTimeStats", "false");
        try {
            this.dataSource = new HikariDataSource(hikariConfig);
            corePlugin.getLogger().info("Successfully established database connection pool to " + host + ":" + port + "/");
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.SEVERE,
                    "Could not establish database connection pool! Plugin functionality requiring database access will be disabled.", e);
            this.dataSource = null;
        }
    }

    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("Database source is not available. Check plugin logs for connection errors.");
        }
        return dataSource.getConnection();
    }

    public boolean isConnected() {
        return dataSource != null && !dataSource.isClosed();
    }

    public void closeDataSource() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            corePlugin.getLogger().info("Database connection pool closed.");
        }
    }
} 