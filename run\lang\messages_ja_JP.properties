#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=すでにこのサーバーに接続されています。
velocity.error.already-connected-proxy=すでにこのプロキシに接続されています。
velocity.error.already-connecting=すでにサーバーに接続しようとしています！
velocity.error.cant-connect={0} に接続できません\: {1}
velocity.error.connecting-server-error={0} に接続できませんでした。後でもう一度お試しください。
velocity.error.connected-server-error={0} との接続に問題が発生しました。
velocity.error.internal-server-connection-error=内部サーバー接続エラーが発生しました。
velocity.error.logging-in-too-fast=ログイン速度が速すぎます。後でもう一度お試しください。
velocity.error.online-mode-only=無効なセッションです（ゲームとランチャーを再起動してください）
velocity.error.player-connection-error=接続中に内部エラーが発生しました。
velocity.error.modern-forwarding-needs-new-client=このサーバーは Minecraft 1.13以降のみ互換性があります。
velocity.error.modern-forwarding-failed=サーバーがプロキシに転送要求を送信しませんでした。 サーバーが Velocity 用に構成されていることを確認してください。
velocity.error.moved-to-new-server=あなたは {0} からキックされました\: {1}
velocity.error.no-available-servers=接続できるサーバーがありません。後でもう一度試すか、管理者にお問い合わせください。
velocity.error.illegal-chat-characters=Illegal characters in chat
# Commands
velocity.command.generic-error=このコマンドの実行中にエラーが発生しました。
velocity.command.command-does-not-exist=未知のコマンドです。
velocity.command.players-only=このコマンドはプレイヤーのみ実行できます。
velocity.command.server-does-not-exist=指定されたサーバー {0} は存在しません。
velocity.command.player-not-found=The specified player {0} does not exist.
velocity.command.server-current-server=現在 {0} に接続しています。
velocity.command.server-too-many=設定されているサーバー数が多すぎます。タブ補完を使い、利用可能なすべてのサーバーを表示してください。
velocity.command.server-available=利用可能なサーバー\:
velocity.command.server-tooltip-player-online={0} 人のプレイヤーがオンライン
velocity.command.server-tooltip-players-online={0} 人のプレイヤーがオンライン
velocity.command.server-tooltip-current-server=現在、このサーバーに接続しています
velocity.command.server-tooltip-offer-connect-server=クリックしてこのサーバーに接続
velocity.command.glist-player-singular={0} 人が現在プロキシに接続しています。
velocity.command.glist-player-plural={0} 人が現在プロキシに接続しています。
velocity.command.glist-view-all=サーバー上のすべてのプレイヤーを表示するには、/glist allを使用してください。
velocity.command.reload-success=Velocityの設定が再読み込みされました。
velocity.command.reload-failure=Velocityの設定を再読み込みできません。詳細はコンソールで確認してください。
velocity.command.version-copyright=Copyright 2018-2023 {0}. {1} は、GNU General Public License v3に基づいてライセンスされています。
velocity.command.no-plugins=現在インストールされているプラグインはありません。
velocity.command.plugins-list=プラグイン\: {0}
velocity.command.plugin-tooltip-website=ウェブサイト\: {0}
velocity.command.plugin-tooltip-author=作者\: {0}
velocity.command.plugin-tooltip-authors=作者\: {0}
velocity.command.dump-uploading=収集した情報をアップロードしています...
velocity.command.dump-send-error=Velocityサーバーとの通信中にエラーが発生しました。サーバーが一時的に利用できないか、ネットワーク設定に問題がある可能性があります。詳細はコンソールまたはサーバーログで確認できます。
velocity.command.dump-success=このプロキシに関する有用な情報を含む匿名化されたレポートを作成しました。開発者が要求した場合は、次のリンクを共有してください\:
velocity.command.dump-will-expire=このリンクは数日後に期限切れになります。
velocity.command.dump-server-error=Velocityサーバーでエラーが発生し、ダンプが完了できませんでした。 この問題についてはVelocityスタッフに連絡し、コンソールまたはサーバーログからこのエラーの詳細を提供してください。
velocity.command.dump-offline=考えられる原因\: システムのDNS設定が無効であるか、インターネットに接続されていません
velocity.command.send-usage=/send <player> <server>
# Kick
velocity.kick.shutdown=プロキシをシャットダウンしています。