package de.dasjeff.aSMPVCore.communication;

import org.jetbrains.annotations.NotNull;

/**
 * Interface für Message-Handler, die eingehende NetworkMessages verarbeiten.
 */
@FunctionalInterface
public interface MessageHandler {

    /**
     * Verarbeitet eine eingehende NetworkMessage.
     * 
     * @param message Die zu verarbeitende Nachricht
     * @throws Exception Falls ein Fehler bei der Verarbeitung auftritt
     */
    void handleMessage(@NotNull NetworkMessage message) throws Exception;
}
