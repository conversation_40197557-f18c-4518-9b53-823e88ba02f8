package de.dasjeff.aSMPVCore.events;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation zur Markierung von Event-Handler-Methoden.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface EventHandler {

    /**
     * Die Priorität des Event-Handlers.
     * @return Die Priorität (Standard: NORMAL).
     */
    EventPriority priority() default EventPriority.NORMAL;

    /**
     * Ob der Event-Handler asynchron ausgeführt werden soll.
     * @return true für asynchrone Ausführung (Standard: false).
     */
    boolean async() default false;
}
