[23:37:15] [main/INFO] [com.velocitypowered.proxy.VelocityServer]: Booting up Velocity 3.4.0-SNAPSHOT (git-21ecd344-b509)...
[23:37:15] [main/INFO] [com.velocitypowered.proxy.VelocityServer]: Loading localizations...
[23:37:15] [main/INFO] [com.velocitypowered.proxy.network.ConnectionManager]: Connections will use NIO channels, Java compression, Java ciphers
[23:37:15] [main/INFO] [com.velocitypowered.proxy.VelocityServer]: Loading plugins...
[23:37:15] [main/INFO] [com.velocitypowered.proxy.plugin.VelocityPluginManager]: Loaded plugin asmp-vcore 1.0 by <PERSON><PERSON><PERSON><PERSON>
[23:37:15] [main/INFO] [com.velocitypowered.proxy.VelocityServer]: Loaded 2 plugins
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Initializing ASMP-VCore v1.0
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Initializing core managers...
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Main configuration loaded successfully.
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: ConfigManager initialized.
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: SecurityManager initialized.
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: CacheManager initialized with statistics: true
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: CacheManager initialized.
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: EventManager initialized.
[23:37:15] [ASMP-VCore - Task Executor #0/INFO] [de.dasjeff.aSMPVCore.libs.hikari.HikariDataSource]: HikariPool-1 - Starting...
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [de.dasjeff.aSMPVCore.libs.hikari.pool.HikariPool]: HikariPool-1 - Added connection de.dasjeff.aSMPVCore.libs.mariadb.Connection@14bd162b
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [de.dasjeff.aSMPVCore.libs.hikari.HikariDataSource]: HikariPool-1 - Start completed.
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Database connection pool established to ************:3306/adventuresmp_core
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Running migration: 002_create_players_table
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Players table created successfully
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Migration completed: 002_create_players_table
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Running migration: 003_create_player_sessions_table
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Player sessions table created successfully
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Migration completed: 003_create_player_sessions_table
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Running migration: 004_create_punishment_tables
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Punishment tables migration placeholder - will be implemented in Phase 3
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Migration completed: 004_create_punishment_tables
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Running migration: 005_create_player_lookup_tables
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Player lookup tables migration placeholder - will be implemented in Phase 3
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Migration completed: 005_create_player_lookup_tables
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Running migration: 006_create_report_tables
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Report tables migration placeholder - will be implemented in Phase 3
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Migration completed: 006_create_report_tables
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Database migrations completed successfully.
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: DatabaseManager initialized successfully.
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: DatabaseManager initialized.
[23:37:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Redis connection pool established to ************:6379/0
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: RedisManager initialized successfully.
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: RedisManager initialized.
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Core message handlers registered (server management delegated to ServerManagementModule).
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: MessageManager initialized.
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Initializing module system...
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Registered module: Core v1.0.0
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Registered module: ServerManagement v1.0.0
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Registered module: PaperIntegration v1.0.0
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Loading all registered modules...
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Loading module: Core v1.0.0
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [Core] Loading Core module...
[23:37:17] [ASMP-VCore - Task Executor #0/WARN] [asmp-vcore]: Event handler method onModuleEvent in CoreModule parameter must extend VCoreEvent
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [Core] Core module loaded successfully
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module loaded successfully: Core
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Loading module: ServerManagement v1.0.0
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [ServerManagement] Loading ServerManagement module...
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [ServerManagement] ServerManagement module loaded successfully
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module loaded successfully: ServerManagement
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Loading module: PaperIntegration v1.0.0
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module loaded successfully: PaperIntegration
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Loaded 3/3 modules successfully.
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Enabling all loaded modules...
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Enabling module: Core
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [Core] Enabling Core module...
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [Core] Core module enabled successfully
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module enabled successfully: Core
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Enabling module: ServerManagement
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [ServerManagement] Enabling ServerManagement module...
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [ServerManagement] ServerManagement module enabled successfully
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module enabled successfully: ServerManagement
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Enabling module: PaperIntegration
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [PaperIntegration] PaperIntegrationModule enabled successfully
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module enabled successfully: PaperIntegration
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Enabled 3/3 modules successfully.
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module system initialized.
[23:37:17] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: ASMP-VCore has been successfully initialized!
[23:37:17] [Netty NIO Boss #0/INFO] [com.velocitypowered.proxy.network.ConnectionManager]: Listening on /[0:0:0:0:0:0:0:0]:25565
[23:37:17] [main/INFO] [com.velocitypowered.proxy.Velocity]: Done (2,35s)!
[23:37:17] [ASMP-VCore-Redis-PubSub/ERROR] [asmp-vcore]: Error handling message of type: system-heartbeat
java.util.UnknownFormatConversionException: Conversion = ')'
	at java.base/java.util.Formatter.parse(Formatter.java:2852) ~[?:?]
	at java.base/java.util.Formatter.format(Formatter.java:2774) ~[?:?]
	at java.base/java.util.Formatter.format(Formatter.java:2728) ~[?:?]
	at java.base/java.lang.String.format(String.java:4390) ~[?:?]
	at de.dasjeff.aSMPVCore.modules.AbstractModule.debug(AbstractModule.java:187) ~[?:?]
	at de.dasjeff.aSMPVCore.modules.paperintegration.PaperIntegrationModule.handleHeartbeat(PaperIntegrationModule.java:225) ~[?:?]
	at de.dasjeff.aSMPVCore.managers.RedisManager.handleIncomingMessage(RedisManager.java:225) ~[?:?]
	at de.dasjeff.aSMPVCore.managers.RedisManager$1.onMessage(RedisManager.java:122) ~[?:?]
	at de.dasjeff.aSMPVCore.managers.RedisManager$1.onMessage(RedisManager.java:119) ~[?:?]
	at de.dasjeff.aSMPVCore.libs.jedis.JedisPubSubBase.process(JedisPubSubBase.java:139) ~[?:?]
	at de.dasjeff.aSMPVCore.libs.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92) ~[?:?]
	at de.dasjeff.aSMPVCore.libs.jedis.Jedis.subscribe(Jedis.java:7941) ~[?:?]
	at de.dasjeff.aSMPVCore.managers.RedisManager.lambda$setupPubSub$1(RedisManager.java:139) ~[?:?]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
[23:37:47] [ASMP-VCore-Redis-PubSub/ERROR] [asmp-vcore]: Error handling message of type: system-heartbeat
java.util.UnknownFormatConversionException: Conversion = ')'
	at java.base/java.util.Formatter.parse(Formatter.java:2852) ~[?:?]
	at java.base/java.util.Formatter.format(Formatter.java:2774) ~[?:?]
	at java.base/java.util.Formatter.format(Formatter.java:2728) ~[?:?]
	at java.base/java.lang.String.format(String.java:4390) ~[?:?]
	at de.dasjeff.aSMPVCore.modules.AbstractModule.debug(AbstractModule.java:187) ~[?:?]
	at de.dasjeff.aSMPVCore.modules.paperintegration.PaperIntegrationModule.handleHeartbeat(PaperIntegrationModule.java:225) ~[?:?]
	at de.dasjeff.aSMPVCore.managers.RedisManager.handleIncomingMessage(RedisManager.java:225) ~[?:?]
	at de.dasjeff.aSMPVCore.managers.RedisManager$1.onMessage(RedisManager.java:122) ~[?:?]
	at de.dasjeff.aSMPVCore.managers.RedisManager$1.onMessage(RedisManager.java:119) ~[?:?]
	at de.dasjeff.aSMPVCore.libs.jedis.JedisPubSubBase.process(JedisPubSubBase.java:139) ~[?:?]
	at de.dasjeff.aSMPVCore.libs.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92) ~[?:?]
	at de.dasjeff.aSMPVCore.libs.jedis.Jedis.subscribe(Jedis.java:7941) ~[?:?]
	at de.dasjeff.aSMPVCore.managers.RedisManager.lambda$setupPubSub$1(RedisManager.java:139) ~[?:?]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
