[19:50:15] [main/INFO] [com.velocitypowered.proxy.VelocityServer]: Booting up Velocity 3.4.0-SNAPSHOT (git-21ecd344-b509)...
[19:50:15] [main/INFO] [com.velocitypowered.proxy.VelocityServer]: Loading localizations...
[19:50:15] [main/INFO] [com.velocitypowered.proxy.network.ConnectionManager]: Connections will use NIO channels, Java compression, Java ciphers
[19:50:15] [main/INFO] [com.velocitypowered.proxy.VelocityServer]: Loading plugins...
[19:50:15] [main/INFO] [com.velocitypowered.proxy.plugin.VelocityPluginManager]: Loaded plugin asmp-vcore 1.0 by <PERSON><PERSON><PERSON><PERSON>
[19:50:15] [main/INFO] [com.velocitypowered.proxy.VelocityServer]: Loaded 2 plugins
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Initializing ASMP-VCore v1.0
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Initializing core managers...
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Main configuration loaded successfully.
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: ConfigManager initialized.
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: SecurityManager initialized.
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: CacheManager initialized with statistics: true
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: CacheManager initialized.
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: EventManager initialized.
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [de.dasjeff.aSMPVCore.libs.hikari.HikariDataSource]: HikariPool-1 - Starting...
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [de.dasjeff.aSMPVCore.libs.hikari.pool.HikariPool]: HikariPool-1 - Added connection de.dasjeff.aSMPVCore.libs.mariadb.Connection@64036ad
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [de.dasjeff.aSMPVCore.libs.hikari.HikariDataSource]: HikariPool-1 - Start completed.
[19:50:15] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Database connection pool established to ************:3306/adventuresmp_core
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Database migrations completed successfully.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: DatabaseManager initialized successfully.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: DatabaseManager initialized.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Redis connection pool established to ************:6379/0
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: RedisManager initialized successfully.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: RedisManager initialized.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Core message handlers registered.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: MessageManager initialized.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Initializing module system...
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Registered module: Core v1.0.0
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Loading all registered modules...
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Loading module: Core v1.0.0
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [Core] Loading Core module...
[19:50:16] [ASMP-VCore - Task Executor #0/WARN] [asmp-vcore]: Event handler method onModuleEvent in CoreModule parameter must extend VCoreEvent
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [Core] Core module loaded successfully
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module loaded successfully: Core
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Loaded 1/1 modules successfully.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Enabling all loaded modules...
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Enabling module: Core
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [Core] Enabling Core module...
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: [Core] Core module enabled successfully
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module enabled successfully: Core
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Enabled 1/1 modules successfully.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: Module system initialized.
[19:50:16] [ASMP-VCore - Task Executor #0/INFO] [asmp-vcore]: ASMP-VCore has been successfully initialized!
[19:50:16] [Netty NIO Boss #0/INFO] [com.velocitypowered.proxy.network.ConnectionManager]: Listening on /[0:0:0:0:0:0:0:0]:25565
[19:50:16] [main/INFO] [com.velocitypowered.proxy.Velocity]: Done (1,56s)!
[19:50:18] [Netty NIO Worker #0/INFO] [com.velocitypowered.proxy.connection.client.AuthSessionHandler]: [connected player] DasJeff (/127.0.0.1:65530) has connected
[19:50:18] [Netty NIO Worker #0/INFO] [com.velocitypowered.proxy.connection.MinecraftConnection]: [server connection] DasJeff -> lobby has connected
[21:36:29] [Netty NIO Worker #0/ERROR] [com.velocitypowered.proxy.connection.MinecraftConnection]: [server connection] DasJeff -> lobby: exception encountered in com.velocitypowered.proxy.connection.backend.BackendPlaySessionHandler@5c3d233e
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:401) ~[?:?]
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:434) ~[?:?]
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:152) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.handle(AbstractNioChannel.java:445) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.nio.NioIoHandler$DefaultNioRegistration.handle(NioIoHandler.java:383) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.nio.NioIoHandler.processSelectedKey(NioIoHandler.java:577) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.nio.NioIoHandler.processSelectedKeysOptimized(NioIoHandler.java:552) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.nio.NioIoHandler.processSelectedKeys(NioIoHandler.java:493) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.nio.NioIoHandler.run(NioIoHandler.java:470) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.SingleThreadIoEventLoop.runIo(SingleThreadIoEventLoop.java:204) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.channel.SingleThreadIoEventLoop.run(SingleThreadIoEventLoop.java:175) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:1073) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[509.jar:3.4.0-SNAPSHOT (git-21ecd344-b509)]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
[21:36:29] [Netty NIO Worker #0/INFO] [com.velocitypowered.proxy.connection.client.ConnectedPlayer]: [connected player] DasJeff (/127.0.0.1:65530) has disconnected: Bei der Verbindung zu lobby ist ein Problem aufgetreten.
[21:36:29] [Netty NIO Worker #0/INFO] [com.velocitypowered.proxy.connection.MinecraftConnection]: [server connection] DasJeff -> lobby has disconnected
