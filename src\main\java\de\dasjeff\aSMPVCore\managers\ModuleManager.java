package de.dasjeff.aSMPVCore.managers;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.IModule;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * ModuleManager verwaltet den Lebenszyklus aller Module im ASMP-VCore Plugin.
 * Bietet Abhängigkeitsverwaltung, Lade-/Aktivierungsreihenfolge und Event-System.
 */
public class ModuleManager {

    private final ASMPVCore plugin;
    private final ConcurrentMap<String, IModule> modules = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, ModuleState> moduleStates = new ConcurrentHashMap<>();
    private final List<ModuleEventListener> eventListeners = new ArrayList<>();

    public ModuleManager(@NotNull ASMPVCore plugin) {
        this.plugin = plugin;
    }

    /**
     * Registriert ein Modul beim <PERSON>.
     * @param module Die Modul-Instanz zum Registrieren.
     */
    public void registerModule(@NotNull IModule module) {
        String moduleName = module.getName().toLowerCase();
        
        if (modules.containsKey(moduleName)) {
            plugin.getLogger().warn("Module with name '{}' is already registered. Skipping.", module.getName());
            return;
        }
        
        modules.put(moduleName, module);
        moduleStates.put(moduleName, new ModuleState(module));
        
        plugin.getLogger().info("Registered module: {} v{}", module.getName(), module.getVersion());
        fireModuleEvent(ModuleEvent.REGISTERED, module);
    }

    /**
     * Entfernt ein Modul vom ModuleManager.
     * @param moduleName Der Name des zu entfernenden Moduls.
     */
    public boolean unregisterModule(@NotNull String moduleName) {
        IModule module = modules.get(moduleName.toLowerCase());
        if (module == null) {
            return false;
        }
        
        // Disable module if enabled
        if (module.isEnabled()) {
            module.onDisable();
        }
        
        modules.remove(moduleName.toLowerCase());
        moduleStates.remove(moduleName.toLowerCase());
        
        plugin.getLogger().info("Unregistered module: {}", module.getName());
        fireModuleEvent(ModuleEvent.UNREGISTERED, module);
        
        return true;
    }

    /**
     * Lädt alle registrierten Module in der richtigen Reihenfolge.
     */
    public void loadModules() {
        plugin.getLogger().info("Loading all registered modules...");
        
        List<IModule> sortedModules = sortModulesByDependencies();
        int successCount = 0;
        
        for (IModule module : sortedModules) {
            try {
                if (!module.isLoaded()) {
                    module.onLoad(plugin);
                    successCount++;
                    fireModuleEvent(ModuleEvent.LOADED, module);
                }
            } catch (Exception e) {
                plugin.getLogger().error("Error loading module: {}", module.getName(), e);
                markModuleAsFailed(module, "Load failed: " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("Loaded {}/{} modules successfully.", successCount, modules.size());
    }

    /**
     * Aktiviert alle geladenen Module in der richtigen Reihenfolge.
     */
    public void enableModules() {
        plugin.getLogger().info("Enabling all loaded modules...");
        
        List<IModule> sortedModules = sortModulesByDependencies();
        int successCount = 0;
        
        for (IModule module : sortedModules) {
            try {
                if (module.isLoaded() && !module.isEnabled() && !isModuleFailed(module)) {
                    module.onEnable(plugin);
                    successCount++;
                    fireModuleEvent(ModuleEvent.ENABLED, module);
                }
            } catch (Exception e) {
                plugin.getLogger().error("Error enabling module: {}", module.getName(), e);
                markModuleAsFailed(module, "Enable failed: " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("Enabled {}/{} modules successfully.", successCount, getLoadedModuleCount());
    }

    /**
     * Deaktiviert alle aktivierten Module in umgekehrter Reihenfolge.
     */
    public void disableAllModules() {
        plugin.getLogger().info("Disabling all enabled modules...");
        
        List<IModule> sortedModules = sortModulesByDependencies();
        Collections.reverse(sortedModules); // Reverse order for disabling
        
        for (IModule module : sortedModules) {
            try {
                if (module.isEnabled()) {
                    module.onDisable();
                    fireModuleEvent(ModuleEvent.DISABLED, module);
                }
            } catch (Exception e) {
                plugin.getLogger().error("Error disabling module: {}", module.getName(), e);
            }
        }
        
        plugin.getLogger().info("All modules have been disabled.");
    }

    /**
     * Lädt ein bestimmtes Modul neu.
     * @param moduleName Der Name des neu zu ladenden Moduls.
     * @return true, wenn das Neuladen erfolgreich war.
     */
    public boolean reloadModule(@NotNull String moduleName) {
        IModule module = getModule(moduleName);
        if (module == null) {
            plugin.getLogger().warn("Cannot reload module '{}' - not found.", moduleName);
            return false;
        }
        
        try {
            plugin.getLogger().info("Reloading module: {}", module.getName());
            module.onReload(plugin);
            clearModuleFailure(module);
            fireModuleEvent(ModuleEvent.RELOADED, module);
            plugin.getLogger().info("Module reloaded successfully: {}", module.getName());
            return true;
        } catch (Exception e) {
            plugin.getLogger().error("Failed to reload module: {}", module.getName(), e);
            markModuleAsFailed(module, "Reload failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Lädt alle registrierten Module neu.
     * @return Die Anzahl der erfolgreich neu geladenen Module.
     */
    public int reloadAllModules() {
        plugin.getLogger().info("Reloading all registered modules...");
        int successCount = 0;
        
        for (IModule module : modules.values()) {
            if (reloadModule(module.getName())) {
                successCount++;
            }
        }
        
        plugin.getLogger().info("Reloaded {}/{} modules successfully.", successCount, modules.size());
        return successCount;
    }

    /**
     * Ruft ein Modul anhand seines Namens ab.
     * @param moduleName Der Name des Moduls.
     * @return Das Modul oder null, wenn nicht gefunden.
     */
    @Nullable
    public IModule getModule(@NotNull String moduleName) {
        return modules.get(moduleName.toLowerCase());
    }

    /**
     * Ruft ein Modul anhand seiner Klasse ab.
     * @param moduleClass Die Klasse des Moduls.
     * @return Das Modul oder null, wenn nicht gefunden.
     */
    @Nullable
    @SuppressWarnings("unchecked")
    public <T extends IModule> T getModule(@NotNull Class<T> moduleClass) {
        for (IModule module : modules.values()) {
            if (moduleClass.isInstance(module)) {
                return (T) module;
            }
        }
        return null;
    }

    /**
     * Gibt alle registrierten Module zurück.
     * @return Eine unveränderliche Collection aller Module.
     */
    @NotNull
    public Collection<IModule> getAllModules() {
        return Collections.unmodifiableCollection(modules.values());
    }

    /**
     * Gibt alle aktivierten Module zurück.
     * @return Eine Liste aller aktivierten Module.
     */
    @NotNull
    public List<IModule> getEnabledModules() {
        return modules.values().stream()
                .filter(IModule::isEnabled)
                .toList();
    }

    /**
     * Gibt alle geladenen Module zurück.
     * @return Eine Liste aller geladenen Module.
     */
    @NotNull
    public List<IModule> getLoadedModules() {
        return modules.values().stream()
                .filter(IModule::isLoaded)
                .toList();
    }

    /**
     * Gibt die Anzahl der registrierten Module zurück.
     */
    public int getModuleCount() {
        return modules.size();
    }

    /**
     * Gibt die Anzahl der geladenen Module zurück.
     */
    public int getLoadedModuleCount() {
        return (int) modules.values().stream().filter(IModule::isLoaded).count();
    }

    /**
     * Gibt die Anzahl der aktivierten Module zurück.
     */
    public int getEnabledModuleCount() {
        return (int) modules.values().stream().filter(IModule::isEnabled).count();
    }

    /**
     * Prüft, ob ein Modul registriert ist.
     * @param moduleName Der Name des Moduls.
     * @return true, wenn das Modul registriert ist.
     */
    public boolean isModuleRegistered(@NotNull String moduleName) {
        return modules.containsKey(moduleName.toLowerCase());
    }

    /**
     * Sortiert Module nach ihren Abhängigkeiten.
     */
    @NotNull
    private List<IModule> sortModulesByDependencies() {
        List<IModule> sorted = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        Set<String> visiting = new HashSet<>();
        
        for (IModule module : modules.values()) {
            if (!visited.contains(module.getName().toLowerCase())) {
                sortModuleDFS(module, visited, visiting, sorted);
            }
        }
        
        return sorted;
    }

    /**
     * Depth-First Search für Abhängigkeitssortierung.
     */
    private void sortModuleDFS(@NotNull IModule module, @NotNull Set<String> visited, 
                              @NotNull Set<String> visiting, @NotNull List<IModule> sorted) {
        String moduleName = module.getName().toLowerCase();
        
        if (visiting.contains(moduleName)) {
            plugin.getLogger().warn("Circular dependency detected involving module: {}", module.getName());
            return;
        }
        
        if (visited.contains(moduleName)) {
            return;
        }
        
        visiting.add(moduleName);
        
        // Process dependencies first
        for (String dependency : module.getDependencies()) {
            IModule dependencyModule = modules.get(dependency.toLowerCase());
            if (dependencyModule != null) {
                sortModuleDFS(dependencyModule, visited, visiting, sorted);
            } else {
                plugin.getLogger().warn("Module '{}' depends on '{}' which is not registered", 
                        module.getName(), dependency);
            }
        }
        
        visiting.remove(moduleName);
        visited.add(moduleName);
        sorted.add(module);
    }

    /**
     * Markiert ein Modul als fehlgeschlagen.
     */
    private void markModuleAsFailed(@NotNull IModule module, @NotNull String reason) {
        ModuleState state = moduleStates.get(module.getName().toLowerCase());
        if (state != null) {
            state.failed = true;
            state.failureReason = reason;
        }
    }

    /**
     * Prüft, ob ein Modul fehlgeschlagen ist.
     */
    private boolean isModuleFailed(@NotNull IModule module) {
        ModuleState state = moduleStates.get(module.getName().toLowerCase());
        return state != null && state.failed;
    }

    /**
     * Löscht den Fehlerstatus eines Moduls.
     */
    private void clearModuleFailure(@NotNull IModule module) {
        ModuleState state = moduleStates.get(module.getName().toLowerCase());
        if (state != null) {
            state.failed = false;
            state.failureReason = null;
        }
    }

    /**
     * Registriert einen Event-Listener für Modul-Events.
     */
    public void addEventListener(@NotNull ModuleEventListener listener) {
        eventListeners.add(listener);
    }

    /**
     * Entfernt einen Event-Listener.
     */
    public void removeEventListener(@NotNull ModuleEventListener listener) {
        eventListeners.remove(listener);
    }

    /**
     * Feuert ein Modul-Event.
     */
    private void fireModuleEvent(@NotNull ModuleEvent event, @NotNull IModule module) {
        for (ModuleEventListener listener : eventListeners) {
            try {
                listener.onModuleEvent(event, module);
            } catch (Exception e) {
                plugin.getLogger().error("Error in module event listener", e);
            }
        }
    }

    /**
     * Modul-State-Klasse für interne Verwaltung.
     */
    private static class ModuleState {
        final IModule module;
        boolean failed = false;
        String failureReason = null;

        ModuleState(IModule module) {
            this.module = module;
        }
    }

    /**
     * Enum für Modul-Events.
     */
    public enum ModuleEvent {
        REGISTERED, UNREGISTERED, LOADED, ENABLED, DISABLED, RELOADED
    }

    /**
     * Interface für Modul-Event-Listener.
     */
    @FunctionalInterface
    public interface ModuleEventListener {
        void onModuleEvent(@NotNull ModuleEvent event, @NotNull IModule module);
    }
}
