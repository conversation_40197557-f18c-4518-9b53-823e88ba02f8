package de.dasjeff.aSMPVCore.events;

import org.jetbrains.annotations.NotNull;

/**
 * Basis-Klasse für alle Events im ASMP-VCore Plugin.
 * Bietet grundlegende Event-Funktionalität wie Cancellation und Timing.
 */
public abstract class VCoreEvent {

    private final long timestamp;
    private boolean cancelled = false;

    /**
     * Konstruktor für ein VCoreEvent.
     */
    protected VCoreEvent() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * Gibt den Zeitstempel zurück, wann das Event erstellt wurde.
     * @return Der Zeitstempel in Millisekunden.
     */
    public final long getTimestamp() {
        return timestamp;
    }

    /**
     * Prüft, ob das Event abgebrochen wurde.
     * @return true, wenn das Event abgebrochen wurde.
     */
    public final boolean isCancelled() {
        return cancelled;
    }

    /**
     * Setzt den Abbruch-Status des Events.
     * @param cancelled true, um das Event abzubrechen.
     */
    public final void setCancelled(boolean cancelled) {
        if (!isCancellable()) {
            throw new UnsupportedOperationException("Event " + getClass().getSimpleName() + " is not cancellable");
        }
        this.cancelled = cancelled;
    }

    /**
     * Prüft, ob das Event abgebrochen werden kann.
     * Standardimplementierung gibt false zurück - muss überschrieben werden für cancellable Events.
     * @return true, wenn das Event abgebrochen werden kann.
     */
    public boolean isCancellable() {
        return false;
    }

    /**
     * Gibt den Namen des Events zurück.
     * Standardimplementierung gibt den Klassennamen zurück.
     * @return Der Name des Events.
     */
    @NotNull
    public String getEventName() {
        return getClass().getSimpleName();
    }

    @Override
    public String toString() {
        return String.format("%s{timestamp=%d, cancelled=%s}", 
                getClass().getSimpleName(), timestamp, cancelled);
    }
}
