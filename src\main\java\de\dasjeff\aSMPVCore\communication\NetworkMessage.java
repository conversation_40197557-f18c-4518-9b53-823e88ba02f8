package de.dasjeff.aSMPVCore.communication;

import com.google.gson.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.UUID;

/**
 * NetworkMessage repräsentiert eine Nachricht, die zwischen Servern über Redis ausgetauscht wird.
 * Enthält Header-Informationen, Payload-Daten und Sicherheitsmetadaten.
 */
public class NetworkMessage {

    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();

    // Header information
    private String messageId;
    private String sourceServer;
    private String targetServer; // "*" for broadcast
    private String messageType;
    private String action;
    private long timestamp;
    
    // Payload
    private JsonObject payload;
    
    // Security
    private String signature;

    /**
     * Konstruktor für eine neue NetworkMessage.
     */
    public NetworkMessage(@NotNull String sourceServer, @NotNull String targetServer, 
                         @NotNull String messageType, @NotNull String action) {
        this.messageId = UUID.randomUUID().toString();
        this.sourceServer = sourceServer;
        this.targetServer = targetServer;
        this.messageType = messageType;
        this.action = action;
        this.timestamp = Instant.now().toEpochMilli();
        this.payload = new JsonObject();
    }

    /**
     * Privater Konstruktor für Deserialisierung.
     */
    private NetworkMessage() {
        // Used for deserialization
    }

    /**
     * Erstellt eine Broadcast-Nachricht.
     */
    @NotNull
    public static NetworkMessage createBroadcast(@NotNull String sourceServer, 
                                               @NotNull String messageType, 
                                               @NotNull String action) {
        return new NetworkMessage(sourceServer, "*", messageType, action);
    }

    /**
     * Erstellt eine gezielte Nachricht an einen bestimmten Server.
     */
    @NotNull
    public static NetworkMessage createTargeted(@NotNull String sourceServer, 
                                              @NotNull String targetServer,
                                              @NotNull String messageType, 
                                              @NotNull String action) {
        return new NetworkMessage(sourceServer, targetServer, messageType, action);
    }

    /**
     * Fügt Daten zur Payload hinzu.
     */
    @NotNull
    public NetworkMessage addData(@NotNull String key, @NotNull String value) {
        payload.addProperty(key, value);
        return this;
    }

    /**
     * Fügt Daten zur Payload hinzu.
     */
    @NotNull
    public NetworkMessage addData(@NotNull String key, @NotNull Number value) {
        payload.addProperty(key, value);
        return this;
    }

    /**
     * Fügt Daten zur Payload hinzu.
     */
    @NotNull
    public NetworkMessage addData(@NotNull String key, @NotNull Boolean value) {
        payload.addProperty(key, value);
        return this;
    }

    /**
     * Fügt ein JsonObject zur Payload hinzu.
     */
    @NotNull
    public NetworkMessage addData(@NotNull String key, @NotNull JsonObject value) {
        payload.add(key, value);
        return this;
    }

    /**
     * Fügt ein JsonArray zur Payload hinzu.
     */
    @NotNull
    public NetworkMessage addData(@NotNull String key, @NotNull JsonArray value) {
        payload.add(key, value);
        return this;
    }

    /**
     * Setzt die komplette Payload.
     */
    @NotNull
    public NetworkMessage setPayload(@NotNull JsonObject payload) {
        this.payload = payload;
        return this;
    }

    /**
     * Ruft einen String-Wert aus der Payload ab.
     */
    @Nullable
    public String getString(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && element.isJsonPrimitive() ? element.getAsString() : null;
    }

    /**
     * Ruft einen Integer-Wert aus der Payload ab.
     */
    @Nullable
    public Integer getInt(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && element.isJsonPrimitive() ? element.getAsInt() : null;
    }

    /**
     * Ruft einen Long-Wert aus der Payload ab.
     */
    @Nullable
    public Long getLong(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && element.isJsonPrimitive() ? element.getAsLong() : null;
    }

    /**
     * Ruft einen Boolean-Wert aus der Payload ab.
     */
    @Nullable
    public Boolean getBoolean(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && element.isJsonPrimitive() ? element.getAsBoolean() : null;
    }

    /**
     * Ruft ein JsonObject aus der Payload ab.
     */
    @Nullable
    public JsonObject getJsonObject(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && element.isJsonObject() ? element.getAsJsonObject() : null;
    }

    /**
     * Ruft ein JsonArray aus der Payload ab.
     */
    @Nullable
    public JsonArray getJsonArray(@NotNull String key) {
        JsonElement element = payload.get(key);
        return element != null && element.isJsonArray() ? element.getAsJsonArray() : null;
    }

    /**
     * Prüft, ob die Nachricht für diesen Server bestimmt ist.
     */
    public boolean isTargetedTo(@NotNull String serverId) {
        return "*".equals(targetServer) || serverId.equals(targetServer);
    }

    /**
     * Prüft, ob die Nachricht eine Broadcast-Nachricht ist.
     */
    public boolean isBroadcast() {
        return "*".equals(targetServer);
    }

    /**
     * Prüft, ob die Nachricht noch gültig ist (nicht zu alt).
     */
    public boolean isValid(long maxAgeMillis) {
        return (Instant.now().toEpochMilli() - timestamp) <= maxAgeMillis;
    }

    /**
     * Serialisiert die Nachricht zu JSON.
     */
    @NotNull
    public String serialize() {
        JsonObject json = new JsonObject();
        
        // Header
        json.addProperty("messageId", messageId);
        json.addProperty("sourceServer", sourceServer);
        json.addProperty("targetServer", targetServer);
        json.addProperty("messageType", messageType);
        json.addProperty("action", action);
        json.addProperty("timestamp", timestamp);
        
        // Payload
        json.add("payload", payload);
        
        // Security
        if (signature != null) {
            json.addProperty("signature", signature);
        }
        
        return GSON.toJson(json);
    }

    /**
     * Deserialisiert eine Nachricht aus JSON.
     */
    @Nullable
    public static NetworkMessage deserialize(@NotNull String json) {
        try {
            JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
            
            NetworkMessage message = new NetworkMessage();
            message.messageId = jsonObject.get("messageId").getAsString();
            message.sourceServer = jsonObject.get("sourceServer").getAsString();
            message.targetServer = jsonObject.get("targetServer").getAsString();
            message.messageType = jsonObject.get("messageType").getAsString();
            message.action = jsonObject.get("action").getAsString();
            message.timestamp = jsonObject.get("timestamp").getAsLong();
            
            message.payload = jsonObject.has("payload") ? 
                jsonObject.getAsJsonObject("payload") : new JsonObject();
            
            if (jsonObject.has("signature")) {
                message.signature = jsonObject.get("signature").getAsString();
            }
            
            return message;
            
        } catch (Exception e) {
            return null;
        }
    }

    // Getters and Setters
    @NotNull
    public String getMessageId() {
        return messageId;
    }

    @NotNull
    public String getSourceServer() {
        return sourceServer;
    }

    @NotNull
    public String getTargetServer() {
        return targetServer;
    }

    @NotNull
    public String getMessageType() {
        return messageType;
    }

    @NotNull
    public String getAction() {
        return action;
    }

    public long getTimestamp() {
        return timestamp;
    }

    @NotNull
    public JsonObject getPayload() {
        return payload;
    }

    @Nullable
    public String getSignature() {
        return signature;
    }

    public void setSignature(@Nullable String signature) {
        this.signature = signature;
    }

    @Override
    public String toString() {
        return String.format("NetworkMessage{id=%s, source=%s, target=%s, type=%s, action=%s, timestamp=%d}",
                messageId, sourceServer, targetServer, messageType, action, timestamp);
    }
}
