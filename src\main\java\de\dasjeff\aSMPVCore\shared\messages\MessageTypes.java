package de.dasjeff.aSMPVCore.shared.messages;

/**
 * Constants for message types used in communication between VCore and Paper Core.
 * Provides a centralized definition of all message types for consistency.
 */
public final class MessageTypes {
    
    // System Messages
    public static final String SYSTEM_HEARTBEAT = "system-heartbeat";
    public static final String SYSTEM_SHUTDOWN = "system-shutdown";
    public static final String SYSTEM_MAINTENANCE = "system-maintenance";
    public static final String SYSTEM_STATUS_REQUEST = "system-status-request";
    public static final String SYSTEM_STATUS_RESPONSE = "system-status-response";
    
    // Server Management
    public static final String SERVER_REGISTER = "server-register";
    public static final String SERVER_UNREGISTER = "server-unregister";
    public static final String SERVER_UPDATE = "server-update";
    public static final String SERVER_LIST_REQUEST = "server-list-request";
    public static final String SERVER_LIST_RESPONSE = "server-list-response";
    
    // Player Data Synchronization
    public static final String PLAYER_JOIN = "player-join";
    public static final String PLAYER_QUIT = "player-quit";
    public static final String PLAYER_SERVER_SWITCH = "player-server-switch";
    public static final String PLAYER_DATA_UPDATE = "player-data-update";
    public static final String PLAYER_DATA_REQUEST = "player-data-request";
    public static final String PLAYER_DATA_RESPONSE = "player-data-response";
    public static final String PLAYER_SEARCH_REQUEST = "player-search-request";
    public static final String PLAYER_SEARCH_RESPONSE = "player-search-response";
    
    // Session Management
    public static final String SESSION_START = "session-start";
    public static final String SESSION_END = "session-end";
    public static final String SESSION_UPDATE = "session-update";
    
    // Punishment System (Phase 3)
    public static final String PUNISHMENT_BAN = "punishment-ban";
    public static final String PUNISHMENT_UNBAN = "punishment-unban";
    public static final String PUNISHMENT_MUTE = "punishment-mute";
    public static final String PUNISHMENT_UNMUTE = "punishment-unmute";
    public static final String PUNISHMENT_KICK = "punishment-kick";
    public static final String PUNISHMENT_WARN = "punishment-warn";
    public static final String PUNISHMENT_CHECK = "punishment-check";
    public static final String PUNISHMENT_HISTORY = "punishment-history";
    
    // Player Lookup System (Phase 3)
    public static final String LOOKUP_PLAYER_BY_NAME = "lookup-player-by-name";
    public static final String LOOKUP_PLAYER_BY_UUID = "lookup-player-by-uuid";
    public static final String LOOKUP_PLAYER_BY_IP = "lookup-player-by-ip";
    public static final String LOOKUP_ALIASES = "lookup-aliases";
    public static final String LOOKUP_HISTORY = "lookup-history";
    
    // Report System (Phase 3)
    public static final String REPORT_CREATE = "report-create";
    public static final String REPORT_UPDATE = "report-update";
    public static final String REPORT_CLOSE = "report-close";
    public static final String REPORT_ASSIGN = "report-assign";
    public static final String REPORT_LIST = "report-list";
    
    // Global Chat (Future)
    public static final String CHAT_MESSAGE = "chat-message";
    public static final String CHAT_BROADCAST = "chat-broadcast";
    public static final String CHAT_PRIVATE = "chat-private";
    
    // Cache Synchronization
    public static final String CACHE_INVALIDATE = "cache-invalidate";
    public static final String CACHE_UPDATE = "cache-update";
    public static final String CACHE_SYNC_REQUEST = "cache-sync-request";
    
    // Error Handling
    public static final String ERROR_RESPONSE = "error-response";
    public static final String INVALID_REQUEST = "invalid-request";
    
    private MessageTypes() {
        // Utility class - prevent instantiation
    }
    
    /**
     * Checks if a message type is a system message.
     */
    public static boolean isSystemMessage(String messageType) {
        return messageType != null && messageType.startsWith("system-");
    }
    
    /**
     * Checks if a message type is a player-related message.
     */
    public static boolean isPlayerMessage(String messageType) {
        return messageType != null && (
            messageType.startsWith("player-") || 
            messageType.startsWith("session-") ||
            messageType.startsWith("lookup-")
        );
    }
    
    /**
     * Checks if a message type is a server management message.
     */
    public static boolean isServerMessage(String messageType) {
        return messageType != null && messageType.startsWith("server-");
    }
    
    /**
     * Checks if a message type is a punishment-related message.
     */
    public static boolean isPunishmentMessage(String messageType) {
        return messageType != null && messageType.startsWith("punishment-");
    }
    
    /**
     * Checks if a message type is a report-related message.
     */
    public static boolean isReportMessage(String messageType) {
        return messageType != null && messageType.startsWith("report-");
    }
    
    /**
     * Checks if a message type is a chat-related message.
     */
    public static boolean isChatMessage(String messageType) {
        return messageType != null && messageType.startsWith("chat-");
    }
    
    /**
     * Checks if a message type is a cache-related message.
     */
    public static boolean isCacheMessage(String messageType) {
        return messageType != null && messageType.startsWith("cache-");
    }
    
    /**
     * Gets the category of a message type.
     */
    public static String getMessageCategory(String messageType) {
        if (messageType == null) {
            return "unknown";
        }
        
        if (isSystemMessage(messageType)) return "system";
        if (isServerMessage(messageType)) return "server";
        if (isPlayerMessage(messageType)) return "player";
        if (isPunishmentMessage(messageType)) return "punishment";
        if (isReportMessage(messageType)) return "report";
        if (isChatMessage(messageType)) return "chat";
        if (isCacheMessage(messageType)) return "cache";
        
        return "unknown";
    }
}
