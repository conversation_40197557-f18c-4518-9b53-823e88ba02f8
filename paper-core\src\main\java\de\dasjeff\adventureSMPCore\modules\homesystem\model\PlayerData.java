package de.dasjeff.adventureSMPCore.modules.homesystem.model;

import org.jetbrains.annotations.NotNull;

import java.sql.Timestamp;
import java.util.UUID;

public class PlayerData {
    private final UUID playerUuid;
    private final String lastKnownName;
    private final Timestamp lastSeen;

    public PlayerData(@NotNull UUID playerUuid, @NotNull String lastKnownName, @NotNull Timestamp lastSeen) {
        this.playerUuid = playerUuid;
        this.lastKnownName = lastKnownName;
        this.lastSeen = lastSeen;
    }
    
    public PlayerData(@NotNull UUID playerUuid, @NotNull String lastKnownName) {
        this(playerUuid, lastKnownName, new Timestamp(System.currentTimeMillis()));
    }


    @NotNull
    public UUID getPlayerUuid() {
        return playerUuid;
    }

    @NotNull
    public String getLastKnownName() {
        return lastKnownName;
    }

    @NotNull
    public Timestamp getLastSeen() {
        return lastSeen;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlayerData that = (PlayerData) o;
        return playerUuid.equals(that.playerUuid);
    }

    @Override
    public int hashCode() {
        return playerUuid.hashCode();
    }
} 