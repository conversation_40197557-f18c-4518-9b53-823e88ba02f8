package de.dasjeff.adventureSMPCore.util;

import org.bukkit.command.CommandSender;
import org.bukkit.permissions.Permissible;

public class PermissionUtil {

    // Base permission node for the plugin
    public static final String BASE_PERM = "adventuresmp.core";

    /**
     * Checks if a CommandSender has a specific permission node related to this plugin.
     *
     * @param sender The CommandSender (Player or Console) to check.
     * @param subNode The specific permission sub-node (e.g., "homes.set").
     * @return True if the sender has the permission, false otherwise.
     */
    public static boolean hasPermission(CommandSender sender, String subNode) {
        if (sender == null || subNode == null || subNode.isEmpty()) {
            return false;
        }
        return sender.hasPermission(BASE_PERM + "." + subNode);
    }

    /**
     * Checks if a CommandSender has a specific permission node, and sends a no-permission message if not.
     *
     * @param sender The CommandSender (Player or Console) to check.
     * @param subNode The specific permission sub-node (e.g., "homes.set").
     * @param noPermissionMessage The message to send if the sender lacks permission (can use ChatUtil for formatting).
     *                            If null or empty, no message is sent.
     * @return True if the sender has the permission, false otherwise.
     */
    public static boolean checkPermission(CommandSender sender, String subNode, String noPermissionMessage) {
        if (hasPermission(sender, subNode)) {
            return true;
        }
        if (noPermissionMessage != null && !noPermissionMessage.isEmpty()) {
            ChatUtil.sendMessage(sender, noPermissionMessage);
        }
        return false;
    }

    /**
     * Checks if a Permissible object has a specific permission node.
     *
     * @param permissible The Permissible object to check.
     * @param subNode The specific permission sub-node.
     * @return True if the permissible has the permission, false otherwise.
     */
    public static boolean hasPermission(Permissible permissible, String subNode) {
        if (permissible == null || subNode == null || subNode.isEmpty()) {
            return false;
        }
        return permissible.hasPermission(BASE_PERM + "." + subNode);
    }

    /**
     * Gets the full permission string for a sub-node.
     * @param subNode The specific permission sub-node.
     * @return The full permission string (e.g., "adventuresmp.core.homes.set").
     */
    public static String getFullPermission(String subNode) {
        if (subNode == null || subNode.isEmpty()) {
            return BASE_PERM;
        }
        return BASE_PERM + "." + subNode;
    }
} 