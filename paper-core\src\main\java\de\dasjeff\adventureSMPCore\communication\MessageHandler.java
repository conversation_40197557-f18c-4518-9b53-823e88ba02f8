package de.dasjeff.adventureSMPCore.communication;

import org.jetbrains.annotations.NotNull;

/**
 * Functional interface for handling network messages in Paper Core.
 * Implementations should handle specific message types from VCore.
 */
@FunctionalInterface
public interface MessageHandler {
    
    /**
     * Handles an incoming network message.
     * 
     * @param message The network message to handle
     */
    void handle(@NotNull NetworkMessage message);
}
