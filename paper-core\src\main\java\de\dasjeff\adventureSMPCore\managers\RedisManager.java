package de.dasjeff.adventureSMPCore.managers;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.communication.MessageHandler;
import de.dasjeff.adventureSMPCore.communication.NetworkMessage;
import org.bukkit.configuration.ConfigurationSection;
import org.jetbrains.annotations.NotNull;
import redis.clients.jedis.*;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * RedisManager for Paper Core - handles Redis communication with VCore.
 * Provides Pub/Sub functionality and message handling for server integration.
 */
public class RedisManager {

    // Redis channel constants (must match VCore channels)
    public static final String CHANNEL_PUNISHMENT = "asmp:punishment";
    public static final String CHANNEL_PLAYER_LOOKUP = "asmp:player-lookup";
    public static final String CHANNEL_REPORTS = "asmp:reports";
    public static final String CHANNEL_SERVER_STATUS = "asmp:server-status";
    public static final String CHANNEL_GLOBAL_CHAT = "asmp:global-chat";
    public static final String CHANNEL_SYSTEM = "asmp:system";
    public static final String CHANNEL_PLAYER_SYNC = "asmp:player-sync";

    private final AdventureSMPCore plugin;
    private final boolean enabled;
    
    // Redis connection
    private JedisPool jedisPool;
    private final String host;
    private final int port;
    private final String password;
    private final int database;
    private final int timeout;
    private final int maxTotal;
    private final int maxIdle;
    private final int minIdle;
    
    // Pub/Sub handling
    private PaperRedisPubSubListener pubSubListener;
    private ExecutorService pubSubExecutor;
    private final ConcurrentMap<String, MessageHandler> messageHandlers = new ConcurrentHashMap<>();

    public RedisManager(@NotNull AdventureSMPCore plugin) {
        this.plugin = plugin;
        
        ConfigurationSection redisConfig = plugin.getConfig().getConfigurationSection("redis");
        if (redisConfig == null) {
            this.enabled = false;
            this.host = "localhost";
            this.port = 6379;
            this.password = "";
            this.database = 0;
            this.timeout = 2000;
            this.maxTotal = 20;
            this.maxIdle = 10;
            this.minIdle = 2;
            plugin.getLogger().warning("Redis configuration not found - Redis integration disabled");
            return;
        }
        
        this.enabled = redisConfig.getBoolean("enabled", true);
        this.host = redisConfig.getString("host", "localhost");
        this.port = redisConfig.getInt("port", 6379);
        this.password = redisConfig.getString("password", "");
        this.database = redisConfig.getInt("database", 0);
        this.timeout = redisConfig.getInt("timeout", 2000);
        
        ConfigurationSection poolConfig = redisConfig.getConfigurationSection("pool");
        if (poolConfig != null) {
            this.maxTotal = poolConfig.getInt("maxTotal", 20);
            this.maxIdle = poolConfig.getInt("maxIdle", 10);
            this.minIdle = poolConfig.getInt("minIdle", 2);
        } else {
            this.maxTotal = 20;
            this.maxIdle = 10;
            this.minIdle = 2;
        }
        
        this.pubSubExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread thread = new Thread(r, "AdventureSMP-Redis-PubSub");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * Initializes the Redis connection and Pub/Sub system.
     */
    public boolean initialize() {
        if (!enabled) {
            plugin.getLogger().info("Redis integration is disabled");
            return false;
        }
        
        try {
            setupJedisPool();
            
            if (isConnected()) {
                setupPubSub();
                plugin.getLogger().info("RedisManager initialized successfully");
                return true;
            } else {
                plugin.getLogger().severe("Failed to establish Redis connection");
                return false;
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to initialize RedisManager: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Sets up the Jedis connection pool.
     */
    private void setupJedisPool() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        
        if (password != null && !password.trim().isEmpty()) {
            jedisPool = new JedisPool(poolConfig, host, port, timeout, password, database);
        } else {
            jedisPool = new JedisPool(poolConfig, host, port, timeout, null, database);
        }
        
        plugin.getLogger().info("Redis connection pool configured: " + host + ":" + port);
    }

    /**
     * Sets up the Pub/Sub listener for Redis channels.
     */
    private void setupPubSub() {
        pubSubListener = new PaperRedisPubSubListener(this);
        
        // Subscribe to all relevant channels
        pubSubExecutor.submit(() -> {
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.subscribe(pubSubListener, 
                    CHANNEL_PUNISHMENT,
                    CHANNEL_PLAYER_LOOKUP,
                    CHANNEL_REPORTS,
                    CHANNEL_SERVER_STATUS,
                    CHANNEL_GLOBAL_CHAT,
                    CHANNEL_SYSTEM,
                    CHANNEL_PLAYER_SYNC
                );
            } catch (Exception e) {
                plugin.getLogger().severe("Redis Pub/Sub connection failed: " + e.getMessage());
                e.printStackTrace();
            }
        });
        
        plugin.getLogger().info("Redis Pub/Sub listener started");
    }

    /**
     * Checks if Redis connection is available.
     */
    public boolean isConnected() {
        if (!enabled || jedisPool == null) {
            return false;
        }
        
        try (Jedis jedis = jedisPool.getResource()) {
            return "PONG".equals(jedis.ping());
        } catch (Exception e) {
            plugin.getLogger().warning("Redis connection test failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Publishes a message to a Redis channel.
     */
    public boolean publishMessage(@NotNull String channel, @NotNull NetworkMessage message) {
        if (!enabled || jedisPool == null) {
            return false;
        }
        
        try (Jedis jedis = jedisPool.getResource()) {
            String serializedMessage = message.serialize();
            Long result = jedis.publish(channel, serializedMessage);
            
            plugin.getLogger().fine("Published message to channel " + channel + 
                    " (subscribers: " + result + ")");
            return result >= 0;
            
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to publish message to channel " + channel + 
                    ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Registers a message handler for a specific message type.
     */
    public void registerMessageHandler(@NotNull String messageType, @NotNull MessageHandler handler) {
        messageHandlers.put(messageType.toLowerCase(), handler);
        plugin.getLogger().fine("Registered message handler for type: " + messageType);
    }

    /**
     * Unregisters a message handler.
     */
    public void unregisterMessageHandler(@NotNull String messageType) {
        messageHandlers.remove(messageType.toLowerCase());
        plugin.getLogger().fine("Unregistered message handler for type: " + messageType);
    }

    /**
     * Handles incoming Redis messages.
     */
    public void handleMessage(@NotNull String channel, @NotNull String message) {
        try {
            NetworkMessage networkMessage = NetworkMessage.deserialize(message);
            String messageType = networkMessage.getMessageType().toLowerCase();
            
            MessageHandler handler = messageHandlers.get(messageType);
            if (handler != null) {
                // Execute handler asynchronously to avoid blocking Redis thread
                plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
                    try {
                        handler.handle(networkMessage);
                    } catch (Exception e) {
                        plugin.getLogger().warning("Error handling message of type " + messageType + 
                                ": " + e.getMessage());
                        e.printStackTrace();
                    }
                });
            } else {
                plugin.getLogger().fine("No handler registered for message type: " + messageType);
            }
            
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to process Redis message: " + e.getMessage());
        }
    }

    /**
     * Gets a Redis value.
     */
    public String get(@NotNull String key) {
        if (!enabled || jedisPool == null) {
            return null;
        }
        
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to get Redis key " + key + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Sets a Redis value.
     */
    public boolean set(@NotNull String key, @NotNull String value) {
        if (!enabled || jedisPool == null) {
            return false;
        }
        
        try (Jedis jedis = jedisPool.getResource()) {
            return "OK".equals(jedis.set(key, value));
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to set Redis key " + key + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Sets a Redis value with expiration.
     */
    public boolean setex(@NotNull String key, int seconds, @NotNull String value) {
        if (!enabled || jedisPool == null) {
            return false;
        }
        
        try (Jedis jedis = jedisPool.getResource()) {
            return "OK".equals(jedis.setex(key, seconds, value));
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to setex Redis key " + key + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Checks if Redis is enabled.
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * Gets the plugin instance.
     */
    public AdventureSMPCore getPlugin() {
        return plugin;
    }

    /**
     * Shuts down the Redis manager.
     */
    public void shutdown() {
        if (pubSubListener != null && pubSubListener.isSubscribed()) {
            pubSubListener.unsubscribe();
        }
        
        if (pubSubExecutor != null && !pubSubExecutor.isShutdown()) {
            pubSubExecutor.shutdown();
        }
        
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            plugin.getLogger().info("Redis connection pool closed");
        }
    }
}
