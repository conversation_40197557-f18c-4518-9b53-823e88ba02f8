#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=이미 이 서버에 연결되어 있습니다\!
velocity.error.already-connected-proxy=이미 이 프록시에 연결되어 있습니다\!
velocity.error.already-connecting=이미 이 서버에 연결하는 중입니다\!
velocity.error.cant-connect={0}에 연결할 수 없습니다\: {1}
velocity.error.connecting-server-error={0}에 연결할 수 없습니다. 나중에 다시 시도하세요.
velocity.error.connected-server-error={0}에 연결하던 도중 문제가 발생했습니다.
velocity.error.internal-server-connection-error=내부 서버 연결 오류가 발생했습니다.
velocity.error.logging-in-too-fast=너무 빠르게 로그인을 시도했습니다. 나중에 다시 시도하세요.
velocity.error.online-mode-only=Minecraft 계정에 로그인하지 않았습니다. Minecraft 계정에 로그인한 경우, Minecraft 클라이언트를 다시 시작해보세요.
velocity.error.player-connection-error=연결에 내부 오류가 발생했습니다.
velocity.error.modern-forwarding-needs-new-client=이 서버는 Minecraft 1.13 이상만 지원합니다.
velocity.error.modern-forwarding-failed=서버가 프록시에 포워딩 요청을 보내지 않았습니다. 서버가 Velocity 포워딩을 사용하도록 설정되어 있는지 확인하세요.
velocity.error.moved-to-new-server={0}에서 추방됐습니다\: {1}
velocity.error.no-available-servers=연결할 수 있는 서버가 없습니다. 나중에 다시 시도하거나 관리자에게 문의하세요.
velocity.error.illegal-chat-characters=채팅에 올바르지 않은 문자가 있습니다.
# Commands
velocity.command.generic-error=명령어를 실행하는 도중 오류가 발생했습니다.
velocity.command.command-does-not-exist=존재하지 않는 명령어입니다.
velocity.command.players-only=이 명령어는 플레이어만 사용할 수 있습니다.
velocity.command.server-does-not-exist=지정한 서버 {0}이(가) 존재하지 않습니다.
velocity.command.player-not-found=지정한 플레이어 {0}이(가) 존재하지 않습니다.
velocity.command.server-current-server=현재 {0}에 연결되어 있습니다.
velocity.command.server-too-many=너무 많은 서버가 존재합니다. tab 자동완성으로 사용 가능한 모든 서버를 볼 수 있습니다.
velocity.command.server-available=사용 가능한 서버\:
velocity.command.server-tooltip-player-online=플레이어 {0}명 접속 중
velocity.command.server-tooltip-players-online=플레이어 {0}명 접속 중
velocity.command.server-tooltip-current-server=현재 이 서버에 연결되어 있습니다
velocity.command.server-tooltip-offer-connect-server=서버에 연결하려면 클릭
velocity.command.glist-player-singular=플레이어 {0}명이 현재 프록시에 연결되어 있습니다.
velocity.command.glist-player-plural=플레이어 {0}명이 현재 프록시에 연결되어 있습니다.
velocity.command.glist-view-all=서버에 있는 모든 플레이어를 보려면, /glist all을 사용하세요.
velocity.command.reload-success=Velocity 설정을 성공적으로 다시 불러왔습니다.
velocity.command.reload-failure=Velocity 설정을 다시 불러올 수 없습니다. 자세한 내용은 콘솔을 확인하세요.
velocity.command.version-copyright=Copyright 2018-2023 {0}. {1}은(는) GNU General Public License v3 라이센스의 약관을 따릅니다.
velocity.command.no-plugins=설치된 플러그인이 없습니다.
velocity.command.plugins-list=플러그인\: {0}
velocity.command.plugin-tooltip-website=웹사이트\: {0}
velocity.command.plugin-tooltip-author=제작자\: {0}
velocity.command.plugin-tooltip-authors=제작자\: {0}
velocity.command.dump-uploading=수집된 정보를 업로드하는 중...
velocity.command.dump-send-error=Velocity 서버와 통신하는 동안 오류가 발생했습니다. 서버를 일시적으로 사용할 수 없거나 네트워크 설정에 문제가 있을 수 있습니다. Velocity 서버의 로그 또는 콘솔에서 자세한 정보를 찾아볼 수 있습니다.
velocity.command.dump-success=이 프록시와 관련된 유용한 정보를 담은 익명 보고서를 만들었습니다. 만약 개발자가 요청한다면, 다음 링크를 그들에게 공유해보세요\:
velocity.command.dump-will-expire=이 링크는 며칠 후에 만료됩니다.
velocity.command.dump-server-error=Velocity 서버에 문제가 발생했으며 덤프가 완료되지 않았습니다. Velocity 스태프에게 연락해 이 문제에 대해 설명하고 Velocity 콘솔 또는 서버 로그를 제공해주세요.
velocity.command.dump-offline=가능성 높은 원인\: 잘못된 DNS 설정 또는 인터넷 연결 없음
velocity.command.send-usage=/send <플레이어> <서버>
# Kick
velocity.kick.shutdown=프록시가 종료됩니다.