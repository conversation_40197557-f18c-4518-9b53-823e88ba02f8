package de.dasjeff.aSMPVCore.shared.models;

import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

/**
 * Shared PlayerData model for communication between VCore and Paper Core.
 * This model represents the central player data structure used across the network.
 */
public class PlayerData {
    
    @SerializedName("player_uuid")
    private final UUID playerUuid;
    
    @SerializedName("last_known_name")
    private final String lastKnownName;
    
    @SerializedName("first_seen")
    private final Timestamp firstSeen;
    
    @SerializedName("last_seen")
    private final Timestamp lastSeen;
    
    @SerializedName("last_server")
    private final String lastServer;
    
    @SerializedName("current_server")
    private final String currentServer;
    
    @SerializedName("total_playtime")
    private final long totalPlaytime; // in seconds
    
    @SerializedName("session_count")
    private final int sessionCount;
    
    @SerializedName("last_ip")
    private final String lastIp;
    
    @SerializedName("last_country")
    private final String lastCountry; // ISO Country Code
    
    @SerializedName("is_vpn_user")
    private final boolean isVpnUser;
    
    @SerializedName("created_at")
    private final Timestamp createdAt;
    
    @SerializedName("updated_at")
    private final Timestamp updatedAt;

    /**
     * Full constructor for database mapping.
     */
    public PlayerData(@NotNull UUID playerUuid,
                     @NotNull String lastKnownName,
                     @NotNull Timestamp firstSeen,
                     @NotNull Timestamp lastSeen,
                     @Nullable String lastServer,
                     @Nullable String currentServer,
                     long totalPlaytime,
                     int sessionCount,
                     @Nullable String lastIp,
                     @Nullable String lastCountry,
                     boolean isVpnUser,
                     @NotNull Timestamp createdAt,
                     @NotNull Timestamp updatedAt) {
        this.playerUuid = Objects.requireNonNull(playerUuid, "playerUuid cannot be null");
        this.lastKnownName = Objects.requireNonNull(lastKnownName, "lastKnownName cannot be null");
        this.firstSeen = Objects.requireNonNull(firstSeen, "firstSeen cannot be null");
        this.lastSeen = Objects.requireNonNull(lastSeen, "lastSeen cannot be null");
        this.lastServer = lastServer;
        this.currentServer = currentServer;
        this.totalPlaytime = Math.max(0, totalPlaytime);
        this.sessionCount = Math.max(0, sessionCount);
        this.lastIp = lastIp;
        this.lastCountry = lastCountry;
        this.isVpnUser = isVpnUser;
        this.createdAt = Objects.requireNonNull(createdAt, "createdAt cannot be null");
        this.updatedAt = Objects.requireNonNull(updatedAt, "updatedAt cannot be null");
    }

    /**
     * Simplified constructor for basic player data.
     */
    public PlayerData(@NotNull UUID playerUuid, @NotNull String lastKnownName) {
        this(playerUuid, lastKnownName, 
             Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
             null, null, 0, 0, null, null, false,
             Timestamp.from(Instant.now()), Timestamp.from(Instant.now()));
    }

    /**
     * Constructor for player join/update scenarios.
     */
    public PlayerData(@NotNull UUID playerUuid,
                     @NotNull String lastKnownName,
                     @Nullable String currentServer,
                     @Nullable String lastIp) {
        this(playerUuid, lastKnownName,
             Timestamp.from(Instant.now()), Timestamp.from(Instant.now()),
             currentServer, currentServer, 0, 1, lastIp, null, false,
             Timestamp.from(Instant.now()), Timestamp.from(Instant.now()));
    }

    // Getters
    @NotNull
    public UUID getPlayerUuid() {
        return playerUuid;
    }

    @NotNull
    public String getLastKnownName() {
        return lastKnownName;
    }

    @NotNull
    public Timestamp getFirstSeen() {
        return firstSeen;
    }

    @NotNull
    public Timestamp getLastSeen() {
        return lastSeen;
    }

    @Nullable
    public String getLastServer() {
        return lastServer;
    }

    @Nullable
    public String getCurrentServer() {
        return currentServer;
    }

    public long getTotalPlaytime() {
        return totalPlaytime;
    }

    public int getSessionCount() {
        return sessionCount;
    }

    @Nullable
    public String getLastIp() {
        return lastIp;
    }

    @Nullable
    public String getLastCountry() {
        return lastCountry;
    }

    public boolean isVpnUser() {
        return isVpnUser;
    }

    @NotNull
    public Timestamp getCreatedAt() {
        return createdAt;
    }

    @NotNull
    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    // Utility methods
    
    /**
     * Creates a copy with updated last seen timestamp.
     */
    @NotNull
    public PlayerData withUpdatedLastSeen() {
        return new PlayerData(playerUuid, lastKnownName, firstSeen, Timestamp.from(Instant.now()),
                            lastServer, currentServer, totalPlaytime, sessionCount,
                            lastIp, lastCountry, isVpnUser, createdAt, Timestamp.from(Instant.now()));
    }

    /**
     * Creates a copy with updated server information.
     */
    @NotNull
    public PlayerData withServerUpdate(@Nullable String newCurrentServer) {
        return new PlayerData(playerUuid, lastKnownName, firstSeen, Timestamp.from(Instant.now()),
                            currentServer, newCurrentServer, totalPlaytime, sessionCount,
                            lastIp, lastCountry, isVpnUser, createdAt, Timestamp.from(Instant.now()));
    }

    /**
     * Creates a copy with updated IP information.
     */
    @NotNull
    public PlayerData withIpUpdate(@Nullable String newIp, @Nullable String newCountry, boolean newIsVpn) {
        return new PlayerData(playerUuid, lastKnownName, firstSeen, Timestamp.from(Instant.now()),
                            lastServer, currentServer, totalPlaytime, sessionCount,
                            newIp, newCountry, newIsVpn, createdAt, Timestamp.from(Instant.now()));
    }

    /**
     * Creates a copy with updated playtime.
     */
    @NotNull
    public PlayerData withPlaytimeUpdate(long additionalPlaytime) {
        return new PlayerData(playerUuid, lastKnownName, firstSeen, Timestamp.from(Instant.now()),
                            lastServer, currentServer, totalPlaytime + additionalPlaytime, sessionCount + 1,
                            lastIp, lastCountry, isVpnUser, createdAt, Timestamp.from(Instant.now()));
    }

    /**
     * Validates the player data.
     */
    public boolean isValid() {
        return playerUuid != null && 
               lastKnownName != null && !lastKnownName.trim().isEmpty() &&
               lastKnownName.length() >= 3 && lastKnownName.length() <= 16 &&
               lastKnownName.matches("^[a-zA-Z0-9_]+$") &&
               firstSeen != null && lastSeen != null &&
               createdAt != null && updatedAt != null &&
               totalPlaytime >= 0 && sessionCount >= 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlayerData that = (PlayerData) o;
        return playerUuid.equals(that.playerUuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(playerUuid);
    }

    @Override
    public String toString() {
        return "PlayerData{" +
                "playerUuid=" + playerUuid +
                ", lastKnownName='" + lastKnownName + '\'' +
                ", currentServer='" + currentServer + '\'' +
                ", totalPlaytime=" + totalPlaytime +
                ", sessionCount=" + sessionCount +
                ", lastSeen=" + lastSeen +
                '}';
    }
}
