#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=Du bist bereits mit diesem Server verbunden\!
velocity.error.already-connected-proxy=Du bist bereits mit diesem Proxy verbunden\!
velocity.error.already-connecting=Du versuchst dich bereits mit einem Server zu verbinden\!
velocity.error.cant-connect=Kein Verbindungsaufbau zu {0} möglich\: {1}
velocity.error.connecting-server-error=Kein Verbindungsaufbau zu {0} möglich. Bitte versuche es später erneut.
velocity.error.connected-server-error=Bei der Verbindung zu {0} ist ein Problem aufgetreten.
velocity.error.internal-server-connection-error=Bei der Verbindung mit dem Server ist ein interner Fehler aufgetreten.
velocity.error.logging-in-too-fast=Du meldest dich zu schnell an, versuche es später noch einmal.
velocity.error.online-mode-only=Du bist nicht in deinem Minecraft Konto eingeloggt. Wenn du in deinem Minecraft Konto eingeloggt bist, versuche deinen Minecraft Client neu zu starten.
velocity.error.player-connection-error=Bei deiner Verbindung ist ein interner Fehler aufgetreten.
velocity.error.modern-forwarding-needs-new-client=Dieser Server ist nur mit der Minecraft Version 1.13 und höher kompatibel.
velocity.error.modern-forwarding-failed=Dein Server hat keine Weiterleitungsanforderung an den Proxy gesendet. Stelle sicher, dass der Server für die Velocity Weiterleitung konfiguriert ist.
velocity.error.moved-to-new-server=Du wurdest von {0} vom Server geworfen\: {1}
velocity.error.no-available-servers=Es gibt keine verfügbaren Server mit denen du dich verbinden kannst. Versuche es später erneut oder kontaktiere einen Admin.
velocity.error.illegal-chat-characters=Ungültige Zeichen im Chat
# Commands
velocity.command.generic-error=Beim Ausführen des Befehls ist ein Fehler aufgetreten.
velocity.command.command-does-not-exist=Dieser Befehl existiert nicht.
velocity.command.players-only=Nur Spieler können diesen Befehl ausführen.
velocity.command.server-does-not-exist=Der angegebene Server {0} existiert nicht.
velocity.command.player-not-found=Der angegebene Spieler {0} existiert nicht.
velocity.command.server-current-server=Du bist derzeit mit {0} verbunden.
velocity.command.server-too-many=Es sind zu viele Server eingerichtet. Verwende die Tabvervollständigung, um alle verfügbaren Server aufzulisten.
velocity.command.server-available=Verfügbare Server\:
velocity.command.server-tooltip-player-online={0} Spieler online
velocity.command.server-tooltip-players-online={0} Spieler online
velocity.command.server-tooltip-current-server=Du bist derzeit mit diesem Server verbunden
velocity.command.server-tooltip-offer-connect-server=Klicke, um dich mit diesem Server zu verbinden
velocity.command.glist-player-singular={0} Spieler ist derzeit mit dem Proxy verbunden.
velocity.command.glist-player-plural={0} Spieler sind derzeit mit dem Proxy verbunden.
velocity.command.glist-view-all=Um alle Spieler auf Servern aufzulisten, verwende /glist all.
velocity.command.reload-success=Velocity-Konfiguration erfolgreich neu geladen.
velocity.command.reload-failure=Die Velocity-Konfiguration konnte nicht neu geladen werden. Prüfe die Konsole für weitere Details.
velocity.command.version-copyright=Copyright 2018-2023 {0}. {1} ist lizenziert unter den Bedingungen der GNU General Public License v3.
velocity.command.no-plugins=Es sind derzeit keine Plugins installiert.
velocity.command.plugins-list=Plugins\: {0}
velocity.command.plugin-tooltip-website=Webseite\: {0}
velocity.command.plugin-tooltip-author=Entwickler\: {0}
velocity.command.plugin-tooltip-authors=Entwickler\: {0}
velocity.command.dump-uploading=Erfasste Daten werden hochgeladen...
velocity.command.dump-send-error=Bei der Kommunikation mit den Velocity-Servern ist ein Fehler aufgetreten. Diese Server sind möglicherweise vorübergehend nicht verfügbar oder es gibt ein Problem mit deinen Netzwerkeinstellungen. Weitere Informationen findest du in der Log-Datei oder in der Konsole deines Velocity-Servers.
velocity.command.dump-success=Ein anonymisierter Bericht mit nützlichen Informationen über diesen Proxy wurde erstellt. Wenn ein Entwickler den Bericht angefordert hat, kannst du diesen über folgenden Link mit ihm teilen\:
velocity.command.dump-will-expire=Dieser Link wird in ein paar Tagen ablaufen.
velocity.command.dump-server-error=Auf den Velocity-Servern ist ein Fehler aufgetreten und der Dump konnte nicht abgeschlossen werden. Bitte kontaktiere die Velocity-Mitarbeiter über das Problem mit Details zu diesem Fehler aus der Velocity-Konsole oder dem Serverprotokoll.
velocity.command.dump-offline=Wahrscheinliche Ursache\: Ungültige System-DNS-Einstellungen oder keine Internetverbindung
velocity.command.send-usage=/send <spieler> <server>
# Kick
velocity.kick.shutdown=Proxy fährt herunter.