package de.dasjeff.adventureSMPCore.managers;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import org.jetbrains.annotations.NotNull;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.logging.Level;

/**
 * Security manager for rate limiting and DoS protection.
 */
public class SecurityManager {

    private final AdventureSMPCore corePlugin;
    private final ConcurrentHashMap<UUID, AtomicLong> lastCommandTime = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<UUID, AtomicInteger> asyncOperationCount = new ConcurrentHashMap<>();
    
    private final long commandCooldownMs;
    private final int maxAsyncOperationsPerPlayer;

    public SecurityManager(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
        this.commandCooldownMs = corePlugin.getConfigManager().getMainConfig().getLong("security.command_cooldown_ms", 100);
        this.maxAsyncOperationsPerPlayer = corePlugin.getConfigManager().getMainConfig().getInt("security.max_async_operations_per_player", 10);
    }

    /**
     * Checks if a player can execute a command (rate limiting).
     */
    public boolean canExecuteCommand(@NotNull UUID playerUuid) {
        long currentTime = System.currentTimeMillis();
        AtomicLong lastTime = lastCommandTime.computeIfAbsent(playerUuid, k -> new AtomicLong(0));
        
        long timeDiff = currentTime - lastTime.get();
        if (timeDiff < commandCooldownMs) {
            return false;
        }
        
        lastTime.set(currentTime);
        return true;
    }

    /**
     * Checks if a player can start an async operation.
     */
    public boolean canStartAsyncOperation(@NotNull UUID playerUuid) {
        AtomicInteger count = asyncOperationCount.computeIfAbsent(playerUuid, k -> new AtomicInteger(0));
        
        if (count.get() >= maxAsyncOperationsPerPlayer) {
            corePlugin.getLogger().log(Level.WARNING, 
                "Player " + playerUuid + " exceeded max async operations limit (" + maxAsyncOperationsPerPlayer + ")");
            return false;
        }
        
        count.incrementAndGet();
        return true;
    }

    /**
     * Decrements async operation count for a player.
     */
    public void finishAsyncOperation(@NotNull UUID playerUuid) {
        AtomicInteger count = asyncOperationCount.get(playerUuid);
        if (count != null) {
            count.decrementAndGet();
            
            // Clean up if no operations running
            if (count.get() <= 0) {
                asyncOperationCount.remove(playerUuid);
            }
        }
    }

    /**
     * Gets current async operation count for a player.
     */
    public int getAsyncOperationCount(@NotNull UUID playerUuid) {
        AtomicInteger count = asyncOperationCount.get(playerUuid);
        return count != null ? count.get() : 0;
    }

    /**
     * Validates input string against common injection patterns.
     */
    public boolean isValidInput(@NotNull String input) {
        if (input.length() > 1000) {
            return false;
        }
        
        // Check for common SQL injection patterns (basic)
        String lower = input.toLowerCase();
        String[] blacklistedPatterns = {
            "drop table", "delete from", "insert into", "update set",
            "union select", "script>", "<iframe", "javascript:",
            "onload=", "onerror=", "onclick="
        };
        
        for (String pattern : blacklistedPatterns) {
            if (lower.contains(pattern)) {
                corePlugin.getLogger().log(Level.WARNING, 
                    "Potentially malicious input detected: " + input.substring(0, Math.min(50, input.length())));
                return false;
            }
        }
        
        return true;
    }

    /**
     * Cleans up security data for a player.
     */
    public void cleanup(@NotNull UUID playerUuid) {
        lastCommandTime.remove(playerUuid);
        asyncOperationCount.remove(playerUuid);
    }

    /**
     * Gets security stats for monitoring.
     */
    public String getSecurityStats() {
        return String.format(
            "Security Stats - Active Players: %d, Total Async Operations: %d",
            lastCommandTime.size(),
            asyncOperationCount.values().stream().mapToInt(AtomicInteger::get).sum()
        );
    }
} 