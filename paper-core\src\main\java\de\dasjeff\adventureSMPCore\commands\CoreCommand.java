package de.dasjeff.adventureSMPCore.commands;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.IModule;
import de.dasjeff.adventureSMPCore.util.PermissionUtil;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Core admin command for managing the AdventureSMPCore plugin.
 * Provides functionality to:
 * - Check system status and performance
 * - Reload modules
 * - Manage core settings
 */
public class CoreCommand extends AbstractCommand {

    private final long startTime = System.currentTimeMillis();

    public CoreCommand(AdventureSMPCore corePlugin) {
        super(corePlugin,
                "core",
                PermissionUtil.getFullPermission("admin"),
                false,
                "Core management command for AdventureSMPCore.",
                "/core <status|reload> [component]",
                Arrays.asList("corestatus", "advcore"));
    }

    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();
        switch (subCommand) {
            case "status" -> handleStatusCommand(sender, args);
            case "reload" -> handleReloadCommand(sender, args);
            default -> {
                // For backward compatibility with /corestatus
                if (isLegacyStatusCall()) {
                    handleStatusCommand(sender, args);
                } else {
                    sender.sendMessage("§cUnknown subcommand: " + subCommand);
                }
            }
        }
        return true;
    }
    
    // ===== HELP MESSAGE =====
    
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage("§6§l=== AdventureSMP-Core Commands ===");
        sender.sendMessage("§e/core status §7- §fShow system status");
        sender.sendMessage("§e/core status <cache|security|database|modules> §7- §fDetailed component status");
        sender.sendMessage("§e/core reload <module_name> §7- §fReload a specific module");
        sender.sendMessage("§e/core reload all §7- §fReload all modules");
        sender.sendMessage("§e/core help §7- §fShow this help message");
    }
    
    // ===== STATUS COMMAND =====

    private void handleStatusCommand(CommandSender sender, String[] args) {
        if (args.length <= 1) {
            sendOverallStatus(sender);
        } else {
            String component = args[1].toLowerCase();
            switch (component) {
                case "cache" -> sendCacheStatus(sender);
                case "security" -> sendSecurityStatus(sender);
                case "database" -> sendDatabaseStatus(sender);
                case "modules" -> sendModuleStatus(sender);
                default -> {
                    sender.sendMessage("§cUnknown component: " + component);
                    sender.sendMessage("§eAvailable: cache, security, database, modules");
                }
            }
        }
    }

    private void sendOverallStatus(CommandSender sender) {
        sender.sendMessage("§6§l=== AdventureSMP-Core Status ===");
        sendBasicInfo(sender);
        sendHealthChecks(sender);
        sendPerformanceInfo(sender);
        sender.sendMessage("§eUse '/core status <component>' for detailed info");
    }

    private void sendBasicInfo(CommandSender sender) {
        sender.sendMessage("§aVersion: §f" + corePlugin.getPluginMeta().getVersion());
        sender.sendMessage("§aUptime: §f" + getUptimeString());
    }

    private void sendHealthChecks(CommandSender sender) {
        sender.sendMessage("§aDatabase: " + getDatabaseStatus());
        sender.sendMessage("§aCache Manager: " + getCacheManagerStatus());
        sender.sendMessage("§aSecurity Manager: " + getSecurityManagerStatus());
        
        int moduleCount = getModuleCount();
        sender.sendMessage("§aLoaded Modules: §f" + moduleCount);
    }

    private void sendPerformanceInfo(CommandSender sender) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;
        sender.sendMessage("§aMemory Usage: §f" + usedMemory + "MB / " + totalMemory + "MB");
    }

    private void sendCacheStatus(CommandSender sender) {
        sender.sendMessage("§6§l=== Cache System Status ===");
        
        if (corePlugin.getCacheManager() == null) {
            sender.sendMessage("§cCache Manager is not available!");
            return;
        }
        
        sender.sendMessage("§aCache Manager: §fActive");
        sendHomeCacheStatus(sender);
        sendAdminCacheStatus(sender);
    }

    private void sendHomeCacheStatus(CommandSender sender) {
        if (corePlugin.getCacheManager().cacheExists("playerHomesCache")) {
            long cacheSize = corePlugin.getCacheManager().getCacheSize("playerHomesCache");
            sender.sendMessage("§aPlayer Homes Cache: §f" + cacheSize + " entries");
            
            String stats = corePlugin.getCacheManager().getCacheStats("playerHomesCache");
            if (stats != null && !stats.contains("disabled")) {
                sender.sendMessage("§7Cache Stats: " + stats);
            }
        }
    }

    private void sendAdminCacheStatus(CommandSender sender) {
        for (String cacheName : new String[]{"adminHomeAllPlayersCache", "adminHomePlayersWithHomesCache"}) {
            if (corePlugin.getCacheManager().cacheExists(cacheName)) {
                long size = corePlugin.getCacheManager().getCacheSize(cacheName);
                sender.sendMessage("§a" + cacheName + ": §f" + size + " entries");
            }
        }
    }

    private void sendSecurityStatus(CommandSender sender) {
        sender.sendMessage("§6§l=== Security System Status ===");
        
        if (corePlugin.getSecurityManager() == null) {
            sender.sendMessage("§cSecurity Manager is not available!");
            return;
        }
        
        sender.sendMessage("§aSecurity Manager: §fActive");
        sender.sendMessage("§7" + corePlugin.getSecurityManager().getSecurityStats());
        
        sendSecurityConfig(sender);
    }

    private void sendSecurityConfig(CommandSender sender) {
        long cooldown = corePlugin.getConfigManager().getMainConfig().getLong("security.command_cooldown_ms", 100);
        int maxOps = corePlugin.getConfigManager().getMainConfig().getInt("security.max_async_operations_per_player", 10);
        
        sender.sendMessage("§aCommand Cooldown: §f" + cooldown + "ms");
        sender.sendMessage("§aMax Async Ops/Player: §f" + maxOps);
    }

    private void sendDatabaseStatus(CommandSender sender) {
        sender.sendMessage("§6§l=== Database Status ===");
        
        if (corePlugin.getDatabaseManager() == null) {
            sender.sendMessage("§cDatabase Manager is not available!");
            return;
        }
        
        boolean connected = corePlugin.getDatabaseManager().isConnected();
        sender.sendMessage("§aConnection Status: " + (connected ? "§aConnected" : "§cDisconnected"));
        
        if (connected) {
            sendDatabaseDetails(sender);
            testDatabaseLatency(sender);
        }
    }

    private void sendDatabaseDetails(CommandSender sender) {
        var config = corePlugin.getConfigManager().getMainConfig();
        sender.sendMessage("§aHost: §f" + config.getString("database.host", "N/A"));
        sender.sendMessage("§aDatabase: §f" + config.getString("database.database", "N/A"));
        sender.sendMessage("§aPool Size: §f" + config.getInt("database.poolSize", 10));
    }

    private void testDatabaseLatency(CommandSender sender) {
        sender.sendMessage("§eTesting connection latency...");
        corePlugin.getServer().getScheduler().runTaskAsynchronously(corePlugin, () -> {
            long start = System.currentTimeMillis();
            try {
                var conn = corePlugin.getDatabaseManager().getConnection();
                conn.close();
                long latency = System.currentTimeMillis() - start;
                corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                    sender.sendMessage("§aConnection Latency: §f" + latency + "ms");
                });
            } catch (Exception e) {
                corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                    sender.sendMessage("§cConnection test failed: " + e.getMessage());
                });
            }
        });
    }

    private void sendModuleStatus(CommandSender sender) {
        sender.sendMessage("§6§l=== Module Status ===");
        
        if (corePlugin.getModuleManager() == null) {
            sender.sendMessage("§cModule Manager is not available!");
            return;
        }
        
        var modules = corePlugin.getModuleManager().getAllModules();
        sender.sendMessage("§aLoaded Modules: §f" + modules.size());
        
        for (var entry : modules.entrySet()) {
            sender.sendMessage("§a- " + entry.getValue().getName() + " §7(Active)");
        }
        
        sendModuleConfig(sender);
    }

    private void sendModuleConfig(CommandSender sender) {
        var enabledModules = corePlugin.getConfigManager().getMainConfig().getStringList("modules.enabled");
        sender.sendMessage("§aEnabled in Config: §f" + String.join(", ", enabledModules));
    }
    
    // ===== RELOAD COMMAND =====
    
    private void handleReloadCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§cMissing module name to reload.");
            sender.sendMessage("§eUsage: /core reload <module_name|all>");
            return;
        }
        
        String moduleArg = args[1].toLowerCase();
        if (moduleArg.equals("all")) {
            reloadAllModules(sender);
        } else {
            reloadSpecificModule(sender, moduleArg);
        }
    }
    
    private void reloadAllModules(CommandSender sender) {
        if (corePlugin.getModuleManager() == null) {
            sender.sendMessage("§cModule Manager is not available!");
            return;
        }
        
        sender.sendMessage("§6§l=== Reloading All Modules ===");
        sender.sendMessage("§eThis may take a moment...");
        
        corePlugin.getServer().getScheduler().runTaskAsynchronously(corePlugin, () -> {
            int result = corePlugin.getModuleManager().reloadAllModules();
            
            corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                sender.sendMessage("§aReload complete! Successfully reloaded §f" + result + 
                    " §amodules out of §f" + corePlugin.getModuleManager().getAllModules().size());
            });
        });
    }
    
    private void reloadSpecificModule(CommandSender sender, String moduleName) {
        if (corePlugin.getModuleManager() == null) {
            sender.sendMessage("§cModule Manager is not available!");
            return;
        }
        
        Map<String, IModule> modules = corePlugin.getModuleManager().getAllModules();
        
        final String exactName = modules.keySet().stream()
                .filter(key -> key.equalsIgnoreCase(moduleName))
                .findFirst()
                .orElse(null);
        
        if (exactName == null) {
            sender.sendMessage("§cModule '" + moduleName + "' not found!");
            sender.sendMessage("§eAvailable modules: §f" +
                    String.join(", ", modules.keySet()));
            return;
        }
        
        // Reload the module
        sender.sendMessage("§6§l=== Reloading Module: " + exactName + " ===");
        
        corePlugin.getServer().getScheduler().runTaskAsynchronously(corePlugin, () -> {
            boolean success = corePlugin.getModuleManager().reloadModule(exactName);
            
            corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                if (success) {
                    sender.sendMessage("§aModule '" + exactName + "' has been successfully reloaded!");
                } else {
                    sender.sendMessage("§cFailed to reload module '" + exactName + "'!");
                    sender.sendMessage("§ePlease check the console for errors.");
                }
            });
        });
    }
    
    // ===== HELPER METHODS =====
    
    private String getDatabaseStatus() {
        return (corePlugin.getDatabaseManager() != null && corePlugin.getDatabaseManager().isConnected()) 
            ? "§aOK" : "§cDisconnected";
    }

    private String getCacheManagerStatus() {
        return (corePlugin.getCacheManager() != null) ? "§aOK" : "§cUnavailable";
    }

    private String getSecurityManagerStatus() {
        return (corePlugin.getSecurityManager() != null) ? "§aOK" : "§cUnavailable";
    }

    private int getModuleCount() {
        return corePlugin.getModuleManager() != null ? corePlugin.getModuleManager().getAllModules().size() : 0;
    }

    private String getUptimeString() {
        long uptimeMs = System.currentTimeMillis() - startTime;
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%dd %dh %dm", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%dh %dm", hours, minutes % 60);
        } else {
            return String.format("%dm %ds", minutes, seconds % 60);
        }
    }
    
    private boolean isLegacyStatusCall() {
        // Check if this was invoked via the old corestatus command for backward compatibility
        Thread currentThread = Thread.currentThread();
        StackTraceElement[] stackTrace = currentThread.getStackTrace();
        for (StackTraceElement element : stackTrace) {
            if (element.getClassName().contains("CoreStatusCommand")) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        if (args.length == 1) {
            return Stream.of("status", "reload")
                    .filter(s -> s.startsWith(args[0].toLowerCase()))
                    .collect(Collectors.toList());
        }
        
        if (args.length == 2) {
            switch (args[0].toLowerCase()) {
                case "status":
                    return Stream.of("cache", "security", "database", "modules")
                            .filter(s -> s.startsWith(args[1].toLowerCase()))
                            .collect(Collectors.toList());
                case "reload":
                    List<String> options = new ArrayList<>();
                    options.add("all");
                    
                    // Add all module names
                    if (corePlugin.getModuleManager() != null) {
                        options.addAll(corePlugin.getModuleManager().getAllModules().keySet());
                    }
                    
                    return options.stream()
                            .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                            .collect(Collectors.toList());
            }
        }
        
        return Collections.emptyList();
    }
}
