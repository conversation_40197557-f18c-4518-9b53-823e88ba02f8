# ASMP-VCore - Phase 1 Implementation

## Übersicht

Phase 1 der ASMP-VCore Implementierung ist abgeschlossen! Diese Phase umfasst die komplette Grundinfrastruktur des Velocity-Plugins mit allen Core-Managern, dem modularen System und der Kommunikationsarchitektur.

## Implementierte Features

### ✅ 1.1 Projektsetup
- **Velocity-Plugin-Projekt**: Vollständig konfiguriert mit allen notwendigen Abhängigkeiten
- **Gradle-Build-Skript**: Optimiert für Velocity 3.4.0 mit allen erforderlichen Libraries
- **Grundlegende Plugin-Struktur**: Saubere Architektur mit Dependency Injection

### ✅ 1.2 Core-Manager
- **ConfigManager**: 
  - JSON-basierte Konfiguration mit Standardwerten
  - Automatische Konfigurationserstellung
  - Reload-Funktionalität
  - Typsichere Getter-Methoden

- **DatabaseManager**: 
  - HikariCP-Verbindungspool
  - MariaDB-Integration
  - Migrations-System für Schemaänderungen
  - Asynchrone Datenbankoperationen
  - Optimistische Sperren und Performance-Optimierungen

- **RedisManager**: 
  - Jedis-Verbindungspool
  - Pub/Sub-System für Echtzeit-Kommunikation
  - Pipeline-Operationen für Batch-Anfragen
  - Automatische Reconnection-Logik

- **SecurityManager**: 
  - Token-basierte Authentifizierung
  - HMAC-SHA256-Signierung für Nachrichten
  - Input-Validierung gegen Injection-Angriffe
  - Rate-Limiting und DoS-Schutz
  - Automatische Token-Rotation

- **MessageManager**: 
  - Nachrichtenformat und -routing
  - Handler-Registrierung nach Nachrichtentyp
  - Retry-Mechanismen mit exponentieller Backoff
  - Timeout-Behandlung

### ✅ 1.3 Modulare Architektur
- **IModule-Interface**: Definiert Lebenszyklus und Grundfunktionen
- **AbstractModule**: Basis-Implementierung mit Logging und Fehlerbehandlung
- **ModuleManager**: 
  - Lebenszyklus-Management (load, enable, disable, reload)
  - Abhängigkeitsverwaltung mit topologischer Sortierung
  - Event-System für Modul-Kommunikation
  - Fehlerbehandlung und Recovery

### ✅ 1.4 Caching-System
- **Zweistufiges Caching**:
  - Caffeine für lokalen In-Memory-Cache
  - Redis für verteilten Cache
- **Cache-Invalidierungsmechanismen**:
  - TTL-basierte Invalidierung
  - Event-basierte Invalidierung
  - Manuelle Invalidierung
- **Cache-Statistiken und Monitoring**

### ✅ 1.5 Kommunikationsprotokoll
- **NetworkMessage-Klasse**:
  - Header (Quelle, Ziel, Typ, Aktion)
  - JSON-Payload mit typsicheren Gettern
  - Metadaten (Zeitstempel, ID, Signatur)
- **Serialisierung/Deserialisierung**: GSON-basiert
- **Routing-Mechanismen**: Kanalbasiertes System
- **Fehlerbehandlung**: Umfassende Retry-Strategien

### ✅ Event-System
- **VCoreEvent**: Basis-Event-Klasse mit Cancellation-Support
- **EventManager**: 
  - Annotation-basierte Handler-Registrierung
  - Prioritäten-System
 
┌─────────────────────────────────────────────────────────────┐
│                    ASMPVCore (Main)                         │
├─────────────────────────────────────────────────────────────┤
│  ConfigManager │ SecurityManager │ CacheManager │ EventManager │
├─────────────────────────────────────────────────────────────┤
│  DatabaseManager │ RedisManager │ MessageManager │ ModuleManager │
├─────────────────────────────────────────────────────────────┤
│                        Modules                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ CoreModule  │ │ Future:     │ │ Future:     │            │
│  │             │ │ Punishment  │ │ PlayerLookup│            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
```

## Konfiguration

Die Konfiguration erfolgt über `config.json`:

```json
{
  "database": {
    "enabled": true,
    "host": "localhost",
    "port": 3306,
    "database": "asmp_vcore",
    "username": "root",
    "password": "your_password"
  },
  "redis": {
    "enabled": true,
    "host": "localhost",
    "port": 6379,
    "database": 0
  },
  "security": {
    "serverToken": "change-this-token",
    "hmacSecret": "change-this-secret",
    "tokenRotationHours": 24
  }
}
```

## Installation und Setup

1. **Abhängigkeiten installieren**:
   - MariaDB 10.5+
   - Redis 6.0+
   - Java 21

2. **Plugin kompilieren**:
   ```bash
   ./gradlew build
   ```

3. **Konfiguration anpassen**:
   - `config.json` mit korrekten Datenbank- und Redis-Einstellungen
   - Sicherheits-Tokens ändern

4. **Plugin starten**:
   - JAR-Datei in Velocity `plugins/` Ordner
   - Velocity neu starten

## Sicherheitsfeatures

- **HMAC-Signierung**: Alle Nachrichten werden mit HMAC-SHA256 signiert
- **Replay-Schutz**: Message-IDs werden getrackt
- **Input-Validierung**: Schutz vor SQL-Injection und XSS
- **Rate-Limiting**: DoS-Schutz auf Message-Ebene
- **Token-Rotation**: Automatische Sicherheits-Token-Erneuerung

## Performance-Optimierungen

- **Connection Pooling**: HikariCP für Datenbank, Jedis für Redis
- **Asynchrone Operationen**: Alle I/O-Operationen sind non-blocking
- **Caching**: Zweistufiges Caching reduziert Datenbankzugriffe
- **Pipeline-Operationen**: Batch-Redis-Operationen für bessere Performance

## Monitoring und Logging

- **Strukturiertes Logging**: SLF4J mit konfigurierbaren Log-Levels
- **Cache-Statistiken**: Hit/Miss-Raten und Performance-Metriken
- **Health-Checks**: Automatische Verbindungstests
- **Event-Tracking**: Vollständige Nachverfolgung aller System-Events

## Nächste Schritte (Phase 2)

- Integration mit Paper Core Plugin
- Bidirektionale Kommunikation
- Gemeinsame Datenmodelle
- Synchronisationsmechanismen

## Technische Details

- **Java Version**: 21
- **Velocity API**: 3.4.0
- **Abhängigkeiten**: HikariCP, Jedis, Caffeine, GSON
- **Architektur**: Modulares Plugin-System mit Event-driven Communication
- **Sicherheit**: HMAC-basierte Message-Authentifizierung

Die Implementierung folgt allen Best Practices für Velocity-Plugins und bietet eine solide Grundlage für die weiteren Phasen des ASMP-VCore Systems.
