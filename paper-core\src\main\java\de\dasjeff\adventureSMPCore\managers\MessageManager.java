package de.dasjeff.adventureSMPCore.managers;

import com.google.gson.JsonObject;
import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.communication.MessageHandler;
import de.dasjeff.adventureSMPCore.communication.NetworkMessage;
import org.bukkit.configuration.ConfigurationSection;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * MessageManager for Paper Core - handles message communication with VCore.
 * Provides message sending, routing, and handler registration.
 */
public class MessageManager {

    private final AdventureSMPCore plugin;
    private final ScheduledExecutorService scheduler;
    private final String serverId;
    private final String serverName;
    private final String serverType;
    
    // VCore integration settings
    private final boolean vcoreEnabled;
    private final String securityToken;
    private final String hmacSecret;

    public MessageManager(@NotNull AdventureSMPCore plugin) {
        this.plugin = plugin;
        
        // Get server configuration
        ConfigurationSection serverConfig = plugin.getConfig().getConfigurationSection("server");
        if (serverConfig != null) {
            this.serverId = serverConfig.getString("serverId", "paper-server-1");
            this.serverName = serverConfig.getString("serverName", "AdventureSMP Main");
            this.serverType = serverConfig.getString("serverType", "PAPER");
        } else {
            this.serverId = "paper-server-1";
            this.serverName = "AdventureSMP Main";
            this.serverType = "PAPER";
        }
        
        // Get VCore integration settings
        ConfigurationSection vcoreConfig = plugin.getConfig().getConfigurationSection("vcore");
        if (vcoreConfig != null) {
            this.vcoreEnabled = vcoreConfig.getBoolean("enabled", true);
            this.securityToken = vcoreConfig.getString("securityToken", "change-this-token");
            this.hmacSecret = vcoreConfig.getString("hmacSecret", "change-this-secret");
        } else {
            this.vcoreEnabled = true;
            this.securityToken = "change-this-token";
            this.hmacSecret = "change-this-secret";
        }
        
        this.scheduler = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "AdventureSMP-MessageManager");
            thread.setDaemon(true);
            return thread;
        });
        
        plugin.getLogger().info("MessageManager initialized for server: " + serverId);
    }

    /**
     * Sends a message to VCore (Velocity).
     */
    public CompletableFuture<Boolean> sendToVCore(@NotNull String messageType,
                                                @NotNull String action,
                                                @NotNull JsonObject payload) {
        return sendMessage("velocity-proxy", messageType, action, payload);
    }

    /**
     * Sends a broadcast message to all servers.
     */
    public CompletableFuture<Boolean> broadcastMessage(@NotNull String messageType,
                                                     @NotNull String action,
                                                     @NotNull JsonObject payload) {
        return sendMessage("*", messageType, action, payload);
    }

    /**
     * Sends a message to a specific server.
     */
    public CompletableFuture<Boolean> sendMessage(@NotNull String targetServer,
                                                @NotNull String messageType,
                                                @NotNull String action,
                                                @NotNull JsonObject payload) {
        NetworkMessage message = NetworkMessage.createTargeted(serverId, targetServer, messageType, action);
        message.setPayload(payload);
        
        return sendMessage(message);
    }

    /**
     * Sends a NetworkMessage.
     */
    public CompletableFuture<Boolean> sendMessage(@NotNull NetworkMessage message) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (!vcoreEnabled) {
                    plugin.getLogger().fine("VCore integration disabled - message not sent");
                    return false;
                }
                
                RedisManager redisManager = plugin.getRedisManager();
                if (redisManager == null || !redisManager.isEnabled()) {
                    plugin.getLogger().warning("Redis not available - message not sent");
                    return false;
                }
                
                // Add server information to payload
                addServerInfoToPayload(message);
                
                // Determine channel based on message type
                String channel = getChannelForMessageType(message.getMessageType());
                
                // Send via Redis
                boolean success = redisManager.publishMessage(channel, message);
                
                if (success) {
                    plugin.getLogger().fine("Message sent successfully: " + message);
                } else {
                    plugin.getLogger().warning("Failed to send message: " + message);
                }
                
                return success;
                
            } catch (Exception e) {
                plugin.getLogger().warning("Error sending message: " + message + " - " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }, scheduler);
    }

    /**
     * Adds server information to the message payload.
     */
    private void addServerInfoToPayload(@NotNull NetworkMessage message) {
        JsonObject payload = message.getPayload();
        
        // Add basic server info
        payload.addProperty("serverId", serverId);
        payload.addProperty("serverName", serverName);
        payload.addProperty("serverType", serverType);
        
        // Add current player count
        payload.addProperty("playerCount", plugin.getServer().getOnlinePlayers().size());
        payload.addProperty("maxPlayers", plugin.getServer().getMaxPlayers());
    }

    /**
     * Determines the Redis channel for a message type.
     */
    private String getChannelForMessageType(@NotNull String messageType) {
        return switch (messageType.toLowerCase()) {
            // Punishment messages
            case "punishment", "ban", "mute", "kick", "warn", "unban", "unmute" -> 
                RedisManager.CHANNEL_PUNISHMENT;
            
            // Player lookup and data messages
            case "player-data", "player-lookup", "player-search", "player-info" -> 
                RedisManager.CHANNEL_PLAYER_LOOKUP;
            
            // Report messages
            case "report", "report-create", "report-update", "report-close" -> 
                RedisManager.CHANNEL_REPORTS;
            
            // Server status and heartbeat messages
            case "system-heartbeat", "server-register", "server-update", "server-status", "heartbeat" -> 
                RedisManager.CHANNEL_SERVER_STATUS;
            
            // Player synchronization messages
            case "player-join", "player-quit", "player-server-switch", "session-start", "session-end" -> 
                RedisManager.CHANNEL_PLAYER_SYNC;
            
            // Global chat messages
            case "chat-message", "chat-broadcast", "global-chat" -> 
                RedisManager.CHANNEL_GLOBAL_CHAT;
            
            // System messages
            case "system", "shutdown", "maintenance", "reload" -> 
                RedisManager.CHANNEL_SYSTEM;
            
            default -> RedisManager.CHANNEL_SYSTEM;
        };
    }

    /**
     * Registers a message handler for a specific message type.
     */
    public void registerMessageHandler(@NotNull String messageType, @NotNull MessageHandler handler) {
        RedisManager redisManager = plugin.getRedisManager();
        if (redisManager != null) {
            redisManager.registerMessageHandler(messageType, handler);
            plugin.getLogger().fine("Registered message handler for type: " + messageType);
        }
    }

    /**
     * Unregisters a message handler.
     */
    public void unregisterMessageHandler(@NotNull String messageType) {
        RedisManager redisManager = plugin.getRedisManager();
        if (redisManager != null) {
            redisManager.unregisterMessageHandler(messageType);
            plugin.getLogger().fine("Unregistered message handler for type: " + messageType);
        }
    }

    // Getters
    public String getServerId() { return serverId; }
    public String getServerName() { return serverName; }
    public String getServerType() { return serverType; }
    public boolean isVCoreEnabled() { return vcoreEnabled; }
    public String getSecurityToken() { return securityToken; }
    public String getHmacSecret() { return hmacSecret; }

    /**
     * Shuts down the MessageManager.
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
        plugin.getLogger().info("MessageManager shut down");
    }
}
