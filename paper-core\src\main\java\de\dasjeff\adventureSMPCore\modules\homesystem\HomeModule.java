package de.dasjeff.adventureSMPCore.modules.homesystem;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.modules.IModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.commands.DelHomeCommand;
import de.dasjeff.adventureSMPCore.modules.homesystem.commands.HomeCommand;
import de.dasjeff.adventureSMPCore.modules.homesystem.commands.SetHomeCommand;
import de.dasjeff.adventureSMPCore.modules.homesystem.commands.AdminHomeCommand;
import de.dasjeff.adventureSMPCore.modules.homesystem.config.GuiConfig;
import de.dasjeff.adventureSMPCore.modules.homesystem.config.HomeConfig;
import de.dasjeff.adventureSMPCore.modules.homesystem.config.MessageConfig;
import de.dasjeff.adventureSMPCore.modules.homesystem.database.HomeDataAccessor;
import de.dasjeff.adventureSMPCore.modules.homesystem.gui.HomeGUIManager;
import de.dasjeff.adventureSMPCore.modules.homesystem.listeners.PlayerListener;
import de.dasjeff.adventureSMPCore.modules.homesystem.managers.TeleportManager;
import de.dasjeff.adventureSMPCore.modules.homesystem.service.HomeService;
import de.dasjeff.adventureSMPCore.modules.homesystem.validation.HomeValidator;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.command.CommandSender;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.entity.Player;

import java.util.logging.Level;

public class HomeModule implements IModule {

    public static final String MODULE_NAME = "HomeSystem";
    private AdventureSMPCore corePlugin;
    private HomeConfig homeConfig;
    private MessageConfig messageConfig;
    private GuiConfig guiConfig;
    private HomeDataAccessor dataAccessor;
    private HomeValidator validator;
    private HomeService homeService;
    private HomeGUIManager homeGUIManager;
    private TeleportManager teleportManager;

    private BukkitTask playerImportTask;
    private Runnable importCompleteCallback;
    private volatile int lastImportCount = 0;  // Store the last import count
    public static final String PLAYER_HOMES_CACHE_NAME = "playerHomesCache";


    @Override
    public String getName() {
        return MODULE_NAME;
    }

    @Override
    public void onLoad(AdventureSMPCore corePlugin) {
        this.corePlugin = corePlugin;
        corePlugin.getLogger().info("[" + MODULE_NAME + "] Loading module...");

        // Initialize configurations
        try {
            this.homeConfig = new HomeConfig(corePlugin);
            this.messageConfig = new MessageConfig(corePlugin);
            this.guiConfig = new GuiConfig(corePlugin);
            corePlugin.getLogger().info("[" + MODULE_NAME + "] Configurations loaded.");
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + MODULE_NAME + "] Failed to load configurations!", e);
            // Depending on severity, might want to prevent module enabling
            return;
        }
        corePlugin.getLogger().info("[" + MODULE_NAME + "] Module loaded.");
    }

    @Override
    public void onEnable(AdventureSMPCore corePlugin) {
        corePlugin.getLogger().info("[" + MODULE_NAME + "] Enabling module...");
        if (this.corePlugin == null) this.corePlugin = corePlugin; // Should be set in onLoad

        if (homeConfig == null || messageConfig == null || guiConfig == null) {
            corePlugin.getLogger().severe("[" + MODULE_NAME + "] Cannot enable module due to configuration loading failure in onLoad.");
            return;
        }

        // Initialize Database Accessor
        try {
            this.dataAccessor = new HomeDataAccessor(corePlugin, this);
            if (!this.dataAccessor.initializeDatabase()) {
                 corePlugin.getLogger().severe("[" + MODULE_NAME + "] Failed to initialize database tables. Module will not function correctly.");
                 return;
            }
            corePlugin.getLogger().info("[" + MODULE_NAME + "] Database accessor initialized and tables ensured.");
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + MODULE_NAME + "] Failed to initialize HomeDataAccessor!", e);
            return; // Critical failure
        }
        
        // Initialize Validator and Service Layer
        try {
            this.validator = new HomeValidator(corePlugin, homeConfig, dataAccessor);
            this.homeService = new HomeService(corePlugin, dataAccessor, validator);
            corePlugin.getLogger().info("[" + MODULE_NAME + "] Service and validation layers initialized.");
        } catch (Exception e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + MODULE_NAME + "] Failed to initialize service layers!", e);
            return;
        }
        
        // Initialize Caches via CorePlugin's CacheManager
        if (corePlugin.getCacheManager() != null) {
            try {
                corePlugin.getCacheManager().createCache(PLAYER_HOMES_CACHE_NAME, 250, 5, java.util.concurrent.TimeUnit.MINUTES);
                corePlugin.getLogger().info("[" + MODULE_NAME + "] '" + PLAYER_HOMES_CACHE_NAME + "' initialized.");
            } catch (IllegalArgumentException e) {
                corePlugin.getLogger().fine("[" + MODULE_NAME + "] Cache already exists, continuing...");
            }
        } else {
            corePlugin.getLogger().warning("[" + MODULE_NAME + "] CacheManager not available from CorePlugin. Home caching will be disabled.");
        }
        
        this.homeGUIManager = new HomeGUIManager(corePlugin, this);
        this.teleportManager = new TeleportManager(corePlugin, this);

        // Register Commands
        registerCommands();

        // Register Listeners
        registerListeners();

        // Check if this is the first time setup and auto-import player data if needed
        checkAndPerformInitialImport();

        corePlugin.getLogger().info("[" + MODULE_NAME + "] Module enabled successfully!");
    }

    @Override
    public void onDisable() {
        corePlugin.getLogger().info("[" + MODULE_NAME + "] Disabling module...");

        if (playerImportTask != null && !playerImportTask.isCancelled()) {
            playerImportTask.cancel();
            corePlugin.getLogger().info("[" + MODULE_NAME + "] Player import task cancelled.");
        }
        
        // Unregister commands/listeners if necessary (CommandManager handles this mostly)
        // Close GUI inventories if GUIManager is used and tracks open GUIs

        corePlugin.getLogger().info("[" + MODULE_NAME + "] Module disabled.");
    }

    private void registerCommands() {
        HomeCommand homeCommand = new HomeCommand(corePlugin, this);
        SetHomeCommand setHomeCommand = new SetHomeCommand(corePlugin, this);
        DelHomeCommand delHomeCommand = new DelHomeCommand(corePlugin, this);
        AdminHomeCommand adminHomeCommand = new AdminHomeCommand(corePlugin, this);
        
        corePlugin.getCommandManager().registerCommand(homeCommand);
        corePlugin.getCommandManager().registerCommand(setHomeCommand);
        corePlugin.getCommandManager().registerCommand(delHomeCommand);
        corePlugin.getCommandManager().registerCommand(adminHomeCommand);
        corePlugin.getLogger().info("[" + MODULE_NAME + "] Commands registered.");
    }

    private void registerListeners() {
         corePlugin.getListenerManager().registerListener(new PlayerListener(corePlugin, this));
         corePlugin.getListenerManager().registerListener(homeGUIManager); // Register GUI Manager as listener
        corePlugin.getLogger().info("[" + MODULE_NAME + "] Listeners registered.");
    }

    public void runPlayerImportTask() {
        if (playerImportTask != null && !playerImportTask.isCancelled()) {
            corePlugin.getLogger().warning("[" + MODULE_NAME + "] Player import task is already running or scheduled.");
            return;
        }
        corePlugin.getLogger().info("[" + MODULE_NAME + "] Starting player data import task asynchronously...");
        this.playerImportTask = Bukkit.getScheduler().runTaskAsynchronously(corePlugin, () -> {
            try {
                int importedCount = dataAccessor.importOfflinePlayers();
                lastImportCount = importedCount; // Store the count
                corePlugin.getLogger().info("[" + MODULE_NAME + "] Offline player import finished. Imported/Updated " + importedCount + " players.");

                // Callback ausführen wenn vorhanden
                if (importCompleteCallback != null) {
                    corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                        importCompleteCallback.run();
                        importCompleteCallback = null; // Reset callback nach Ausführung
                    });
                }

                // Mark task as completed by setting it to null
                playerImportTask = null;
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.SEVERE, "[" + MODULE_NAME + "] Error during offline player import: ", e);

                // Auch bei Fehlern den Callback ausführen (optional)
                if (importCompleteCallback != null) {
                    corePlugin.getServer().getScheduler().runTask(corePlugin, () -> {
                        importCompleteCallback.run();
                        importCompleteCallback = null;
                    });
                }

                // Mark task as completed even on error
                playerImportTask = null;
            }
        });
    }

    /**
     * Checks if the player import task is currently running.
     * 
     * @return true if import task is running, false otherwise
     */
    public boolean isImportTaskRunning() {
        return playerImportTask != null && !playerImportTask.isCancelled();
    }

    public void setImportCompleteCallback(Runnable callback) {
        this.importCompleteCallback = callback;
    }

    /**
     * Gets the count of players imported in the last import operation.
     * 
     * @return the number of players imported/updated
     */
    public int getLastImportCount() {
        return lastImportCount;
    }

    // Getters for other classes within the module to access shared components
    public AdventureSMPCore getCorePlugin() {
        return corePlugin;
    }

    public HomeConfig getHomeConfig() {
        return homeConfig;
    }

    public MessageConfig getMessageConfig() {
        return messageConfig;
    }

    public GuiConfig getGuiConfig() {
        return guiConfig;
    }
    
    public HomeDataAccessor getDataAccessor() {
        return dataAccessor;
    }

    public HomeValidator getValidator() {
        return validator;
    }

    public HomeService getHomeService() {
        return homeService;
    }

    public HomeGUIManager getHomeGUIManager() {
       return homeGUIManager;
    }

    public TeleportManager getTeleportManager() {
        return teleportManager;
    }



    /**
     * Checks if the player data table is empty and performs an automatic import if needed.
     * This is called once during module initialization.
     */
    private void checkAndPerformInitialImport() {
        corePlugin.getServer().getScheduler().runTaskAsynchronously(corePlugin, () -> {
            try {
                if (dataAccessor.isPlayerDataEmpty()) {
                    corePlugin.getLogger().info("[" + MODULE_NAME + "] Player data table is empty. Performing automatic initial import...");
                    corePlugin.getServer().getScheduler().runTask(corePlugin, this::runPlayerImportTask);
                } else {
                    corePlugin.getLogger().info("[" + MODULE_NAME + "] Player data table contains entries. Skipping automatic import.");
                }
            } catch (Exception e) {
                corePlugin.getLogger().log(Level.WARNING, "[" + MODULE_NAME + "] Could not check for initial import: ", e);
            }
        });
    }

    /**
     * Utility method to play sounds for players in the home system.
     * This provides a centralized way to handle sound playing with proper error handling.
     * 
     * @param sender The command sender (only plays sound if it's a player)
     * @param soundKey The sound key from the config
     */
    public void playHomeSound(CommandSender sender, String soundKey) {
        if (!(sender instanceof Player player)) return;
        if (soundKey == null || soundKey.isEmpty()) return;

        String normalizedSoundKey = soundKey.toUpperCase();
        
        try {
            @SuppressWarnings("deprecation")
            Sound sound = Sound.valueOf(normalizedSoundKey);
            player.playSound(player.getLocation(), sound, 1.0f, 1.0f);
        } catch (IllegalArgumentException e) {
            try {
                player.playSound(player.getLocation(), soundKey, 1.0f, 1.0f);

                corePlugin.getLogger().fine("[" + MODULE_NAME + "] Using custom sound: '" + soundKey + "'");
            } catch (Exception customSoundException) {
                corePlugin.getLogger().warning("[" + MODULE_NAME + "] Invalid sound key in homes_config.yml: '" + soundKey + 
                    "'. Please check your configuration and use valid Bukkit Sound enum values (e.g., ENTITY_PLAYER_LEVELUP, BLOCK_NOTE_BLOCK_PLING) or custom sounds.");
            }
        }
    }
} 